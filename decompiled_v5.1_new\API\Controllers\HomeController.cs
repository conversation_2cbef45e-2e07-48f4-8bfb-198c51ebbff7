using System;
using System.Threading.Tasks;
using API.BusService.System;
using API.Common;
using API.DataAccess.System;
using API.Models.Comm;
using API.Quartz;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using Quartz;

namespace API.Controllers;

public class HomeController(ISchedulerFactory schedulerFactory) : Controller()
{
	private readonly ISchedulerFactory _schedulerFactory = schedulerFactory;

	[HttpPost]
	public async Task<Response> SaveWidgetsConfig([FromBody] Request model)
	{
		Response mRet = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "key");
			string val = Util.GetJObject(model.jObjectParam, "val");
			model.jObjectParam["userId"] = BusSysUser.Instance.User.Id;
			if (jObject == "GRID" && await Util.Request("/Common/SaveWidgetsConfig", model, base.HttpContext) == null)
			{
				SysUser.EditSysUser(new JObject
				{
					["Fid"] = BusSysUser.Instance.User.Id,
					["FGrid"] = val
				}, BusSysUser.Instance.User.Name, BusSysUser.Instance.User.Id, model.curTime);
			}
		}
		catch
		{
			mRet.code = 500;
			mRet.message = "保存格式不正确！";
		}
		return mRet;
	}

	[HttpPost]
	public async Task<Response> Now()
	{
		Response mRet = new Response();
		try
		{
			HttpClientFactory httpClientFactory = new HttpClientFactory();
			JObject jObject = await httpClientFactory.Get("https://api.bilibili.com/x/click-interface/click/now");
			if (Util.GetJObject(jObject, "code") == "0")
			{
				long jObject2 = Util.GetJObject<long>(jObject["data"], "now");
				long num = DateTimeOffset.UtcNow.ToUnixTimeSeconds() - jObject2;
				if (num > 0)
				{
					mRet.data = "快" + num + "秒";
					Console.WriteLine(">>本地时间比B站快" + num + "秒");
				}
				else if (num < 0)
				{
					mRet.data = "慢" + Math.Abs(num) + "秒";
					Console.WriteLine(">>" + DateTime.Now.ToString("HH:mm:ss") + "\t本地时间比B站慢" + Math.Abs(num) + "秒");
				}
			}
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = ex.Message;
		}
		return mRet;
	}

	[HttpPost]
	public Response GetLocalStatus()
	{
		Response response = new Response();
		try
		{
			JArray jArray = new JArray();
			jArray.Add(SQLHelper.BiliLocalDB.RunExist("BiliBili"));
			response.data = jArray;
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public async Task<Response> GetVer(User user)
	{
		Response mRet = new Response();
		try
		{
			Request model = new Request
			{
				jObjectParam = new JObject
				{
					["userId"] = user.Id,
					["userName"] = user.Name,
					["catalog"] = "最新版本"
				}
			};
			Response response = mRet;
			response.data = await Util.Request("/Common/GetControlByCatalog", model, base.HttpContext);
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}

	[HttpPost]
	public async Task<Response> GetTipList([FromBody] Request model, User user)
	{
		Response mRet = new Response();
		try
		{
			Request model2 = new Request
			{
				jObjectParam = new JObject
				{
					["userId"] = user.Id,
					["userName"] = user.Name
				},
				jObjectSearch = model.jObjectSearch
			};
			Response response = mRet;
			response.data = await Util.Request("/Common/GetHomeList", model2, base.HttpContext);
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}

	[HttpPost]
	public async Task<Response> GetQuatzList()
	{
		Response mRet = new Response();
		try
		{
			Response response = mRet;
			response.data = await QzUtil.GetQuartzList(schedulerFactory);
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}
}
