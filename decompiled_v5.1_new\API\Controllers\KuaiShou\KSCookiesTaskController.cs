using System;
using System.Data;
using System.Threading.Tasks;
using API.BusService.System;
using API.Common;
using API.DataAccess.KuaiShou;
using API.Models.Comm;
using Microsoft.AspNetCore.Mvc;

namespace API.Controllers.KuaiShou;

public class KSCookiesTaskController : Controller
{
	[HttpPost]
	public async Task<Response> GetReceiveTaskList()
	{
		Response mRet = new Response();
		try
		{
			int id = BusSysUser.Instance.User.Id;
			Response response = mRet;
			response.data = await KSCookiesTask.GetReceiveTaskList(id);
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}

	[HttpPost]
	public async Task<Response> ReceiveTask([FromBody] Request model)
	{
		Response mRet = new Response();
		try
		{
			int id = BusSysUser.Instance.User.Id;
			string jObject = Util.GetJObject(model.jObjectParam, "cookieId");
			string jObject2 = Util.GetJObject(model.jObjectParam, "value");
			Response response = mRet;
			response.message = await KSCookiesTask.ReceiveTask(jObject, jObject2, id);
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}

	[HttpPost]
	public Response EditTag([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			int id = BusSysUser.Instance.User.Id;
			string jObject = Util.GetJObject(model.jObjectParam, "task");
			string jObject2 = Util.GetJObject(model.jObjectParam, "tag");
			KSCookiesTask.EditTag(jObject, jObject2, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response ExportCdkeyList([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			int id = BusSysUser.Instance.User.Id;
			string jObject = Util.GetJObject(model.jObjectParam, "cookieId");
			response.data = KSCookiesTask.ExportCdkeyList(jObject, BusSysUser.Instance.User.Id);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response SyncAll([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "cookieId");
			KSCookiesTask.SyncAll(jObject, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response SetStartRecordId([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			DataCURD.Save(model.jObjectParam, "TCookiesTask", "设置起始ID", "Fid", BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime, SQLHelper.KSLocalDB.InitCnn());
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response SetReceive([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			DataCURD.Save(model.jObjectParam, "TCookiesTask", "设置或取消领取", "Fid", BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime, SQLHelper.KSLocalDB.InitCnn());
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response GetTaskList()
	{
		Response response = new Response();
		try
		{
			DataTable taskList = KSCookiesTask.GetTaskList(BusSysUser.Instance.User.Id);
			response.data = Util.GetTableResponse(taskList);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response GetCookiesTaskList([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectSearch, "cookieId");
			string taskId = string.Join(",", Util.GetJObject<string[]>(model.jObjectSearch, "taskId") ?? Array.Empty<string>());
			string jObject2 = Util.GetJObject(model.jObjectSearch, "search");
			DataTable cookiesTaskList = KSCookiesTask.GetCookiesTaskList(jObject, taskId, jObject2, BusSysUser.Instance.User.Id);
			response.data = Util.GetTableResponse(cookiesTaskList);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response UpdateCookiesTask([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "cookieId");
			KSCookiesTask.UpdateCookiesTask(jObject, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
			response.message = "更新成功";
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}
}
