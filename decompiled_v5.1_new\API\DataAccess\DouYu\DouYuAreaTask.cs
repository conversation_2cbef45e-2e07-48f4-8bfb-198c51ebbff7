using System;
using System.Collections.Generic;
using System.Data;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using API.Common;
using Newtonsoft.Json.Linq;

namespace API.DataAccess.DouYu;

public class DouYuAreaTask
{
	public static DataTable GetAreaTaskList(string areaId, string search = "", int limit = 0, int offset = 0, string prop = "", string order = "")
	{
		string text = " SELECT CASE T1.FCompulsory WHEN 1 THEN '√' ELSE '' END AS FCompulsoryName,CASE T1.FEnable WHEN 1 THEN '已启用' ELSE '已禁用' END FEnableName";
		text += " ,CASE T1.FDaily WHEN 1 THEN '√' ELSE '' END AS FDailyName,CASE T1.FComplete WHEN 1 THEN '√' ELSE '' END AS FCompleteName";
		text += " ,T2.FName AS FAreaName,T2.FType AS FAreaType";
		text = text + " ,T1.* " + SQLHelper.total + " FROM TAreaTask T1 LEFT JOIN TArea T2 ON T2.Fid=T1.FAreaId";
		text += " WHERE 1=1 ";
		if (areaId != "")
		{
			text = text + " AND T1.FAreaId IN(" + areaId + ")";
		}
		if (search != "")
		{
			text = text + " AND (T1.FTaskName LIKE '%" + search + "%' OR T1.FAwardName LIKE '%" + search + "%' OR T1.FTaskKey LIKE '%" + search + "%' )";
		}
		if (prop == "")
		{
			prop = "T1.FEnable DESC,T1.FCompulsory DESC,T1.FDaily ASC,T1.FSort";
			order = "ASC";
		}
		return SQLHelper.DouYuLocalDB.RunSqlDt(text, limit, offset, prop, order);
	}

	public static async Task SaveAreaTask(JObject jObject, int userId, string userName, string curTime)
	{
		string taskUrl = Util.GetJObject(jObject, "FTaskUrl");
		string jObject2 = Util.GetJObject(jObject, "FTaskId");
		string jObject3 = Util.GetJObject(jObject, "FEnable");
		if (jObject3 == "0")
		{
			jObject["FCompulsory"] = "0";
		}
		DataTable cookiesList = DouYuCookies.GetCookiesList("", "", userId);
		if (cookiesList.Rows.Count == 0)
		{
			throw new Exception("请先添加账号！");
		}
		string jObject4 = Util.GetJObject(cookiesList.Rows[0], "FKey");
		string jObject5 = Util.GetJObject(cookiesList.Rows[0], "FCookie");
		string jObject6 = Util.GetJObject(cookiesList.Rows[0], "FHeaders");
		string jObject7 = Util.GetJObject(cookiesList.Rows[0], "FProxyAddress");
		string jObject8 = Util.GetJObject(cookiesList.Rows[0], "FProxyUserName");
		string jObject9 = Util.GetJObject(cookiesList.Rows[0], "FProxyPassword");
		HttpClientHandler defaultHandler = null;
		if (jObject7 != "")
		{
			defaultHandler = new HttpClientHandler
			{
				Proxy = new WebProxy
				{
					Address = new Uri(jObject7),
					Credentials = new NetworkCredential(jObject8, jObject9)
				}
			};
		}
		Dictionary<string, string> dictionary = HttpClientFactory.FormataHeader(jObject6, jObject5);
		string areaId = Util.GetJObject(jObject, "FAreaId");
		string sSql = " SELECT FReferer FROM TArea WHERE Fid=" + areaId;
		string value = SQLHelper.DouYuLocalDB.RunSqlStr(sSql);
		dictionary["Referer"] = value;
		dictionary["Host"] = "www.douyu.com";
		HttpClientFactory httpClientFactory = new HttpClientFactory(jObject4, dictionary, defaultHandler);
		JObject jToken = await httpClientFactory.Get(taskUrl + jObject2);
		if (Util.GetJObject(jToken, "error") == "0")
		{
			JArray jObject10 = Util.GetJObject<JArray>(jToken, "data");
			if (jObject10 != null && jObject10.Count == 1)
			{
				JToken jToken2 = jObject10[0];
				string text = "";
				string text2 = "";
				string text3 = "";
				string text4 = "";
				string text5 = "";
				JArray jArray = Util.GetJObject<JArray>(jToken2, "condCompleteList") ?? new JArray();
				if (jArray.Count > 0)
				{
					text2 = Util.GetJObject(jArray[0], "name");
				}
				JArray jArray2 = Util.GetJObject<JArray>(jToken2, "prizeInfo") ?? new JArray();
				if (jArray2.Count > 0)
				{
					text = Util.GetJObject(jArray2[0], "giftAlias");
					if (text2 == "")
					{
						text2 = Util.GetJObject(jArray2[0], "name");
					}
					text3 = Util.GetJObject(Util.GetJObject<JToken>(jArray2[0], "remain"), "remainDesc");
					JArray jArray3 = Util.GetJObject<JArray>(jArray2[0], "prizeInBag") ?? new JArray();
					if (jArray3.Count > 0)
					{
						text4 = Util.GetJObject(jArray3[0], "prizeId");
						text5 = Util.GetJObject(jArray3[0], "prizeName");
					}
				}
				text2 = text2.Replace(text5, "").Trim('-');
				jObject["FTaskUrl"] = taskUrl;
				jObject["FTaskId"] = Util.GetJObject(jToken2, "taskId");
				jObject["FTaskName"] = text2;
				jObject["FPrizeId"] = text4;
				jObject["FPrizeName"] = text5;
				jObject["FPrizeGiftAlias"] = text;
				jObject["FRemainDesc"] = text3;
			}
			sSql = " SELECT STUFF((SELECT ','+FTaskId FROM TAreaTask WHERE FAreaId =" + areaId + " FOR XML PATH('')),1,1,'')";
			string text6 = SQLHelper.DouYuLocalDB.RunSqlStr(sSql);
			jObject["FDate"] = curTime;
			DataCURD.Save(jObject, "TAreaTask", "修改分区任务对照", "Fid", userId, userName, curTime, SQLHelper.DouYuLocalDB.InitCnn());
			DataCURD.Save(new JObject
			{
				["FUpdateTime"] = curTime,
				["Fid"] = jObject["FAreaId"],
				["FTaskId"] = text6
			}, "TArea", "更新分区时间", "Fid", userId, userName, curTime, SQLHelper.DouYuLocalDB.InitCnn());
			return;
		}
		throw new Exception(Util.GetJObject(jToken, "msg"));
	}

	public static void DelAreaTask(JObject jObject, int userId, string userName, string curTime)
	{
		string jObject2 = Util.GetJObject(jObject, "id");
		DataCURD.Delete("TAreaTask", "删除分区任务对照", "Fid", jObject2, userId, userName, curTime, SQLHelper.DouYuLocalDB.InitCnn());
	}

	public static int EnableSwitch(JObject jObject, int userId, string userName, string curTime)
	{
		string jObject2 = Util.GetJObject(jObject, "Fid");
		string jObject3 = Util.GetJObject(jObject, "FEnable");
		string text = Util.GetJObject(jObject, "FCompulsory");
		if (jObject3 != "1")
		{
			text = "0";
		}
		DataCURD.Save(new JObject
		{
			["Fid"] = jObject2,
			["FEnable"] = jObject3,
			["FCompulsory"] = text
		}, "TAreaTask", "启动禁用分区任务", "Fid", userId, userName, curTime, SQLHelper.DouYuLocalDB.InitCnn());
		return int.Parse(jObject3);
	}
}
