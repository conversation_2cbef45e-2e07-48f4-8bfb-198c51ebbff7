using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using API.Common;
using API.Models.Comm;
using Microsoft.Data.SqlClient;
using Newtonsoft.Json.Linq;

namespace API.DataAccess.BiliBili;

public class BiliProxy
{
	public static async Task ShareProxy(string groupId, string name, int userId)
	{
		Util.WriteLog("BiliBili", "代理模块", "共享代理", "正在共享代理");
		Request model = new Request
		{
			jObjectParam = new JObject { ["userId"] = userId },
			jObjectSearch = new JObject { ["p2"] = name }
		};
		await Util.Request("/Common/GetHomeList", model);
		string sSql = " SELECT * FROM TProxy WHERE FUserId=" + userId + " AND FEnable=1 AND FGroupId=" + groupId;
		DataTable dataTable = SQLHelper.BiliLocalDB.RunSqlDt(sSql);
		foreach (DataRow row in dataTable.Rows)
		{
			string addr = Util.GetJObject(row, "FAddress");
			string user = Util.GetJObject(row, "FUserName");
			string pass = Util.GetJObject(row, "FPassword");
			if (!(await TestProxy(new JObject
			{
				["FAddress"] = addr,
				["FUserName"] = user,
				["FPassword"] = pass
			})).Contains("错误信息"))
			{
				model = new Request
				{
					jObjectParam = new JObject { ["userId"] = userId },
					jObjectSearch = new JObject
					{
						["p4"] = addr,
						["p5"] = user,
						["p6"] = pass
					}
				};
				await Util.Request("/Common/GetHomeList", model);
			}
		}
		Util.WriteLog("BiliBili", "代理模块", "共享代理", "共享代理结束");
	}

	public static async Task Cancel(int userId)
	{
		Request model = new Request
		{
			jObjectParam = new JObject { ["userId"] = userId },
			jObjectSearch = new JObject { ["p3"] = "1" }
		};
		await Util.Request("/Common/GetHomeList", model);
	}

	public static string ExportProxy(string groupId, User user)
	{
		string sSql = " SELECT FSort AS 序号,FAddress+(CASE FUserName WHEN '' THEN '' ELSE ':'+FUserName+':'+FPassword END) AS 代理地址 FROM TProxy WHERE FUserId=" + user.Id + " AND FGroupId=" + groupId + " ORDER BY FSort";
		DataTable dt = SQLHelper.BiliLocalDB.RunSqlDt(sSql);
		Util.FileDownload(out string absolute, out string relative);
		ExcelHelper.X2003.TableToExcelForXLS(dt, absolute);
		return relative;
	}

	public static async Task ImportProxy(string src, string groupId, User user, string curTime)
	{
		string file = Directory.GetCurrentDirectory() + "\\WWWRoot\\" + src;
		DataTable dt = ExcelHelper.X2003.ExcelToTableForXLS(file);
		for (int i = 0; i < dt.Rows.Count; i++)
		{
			int.TryParse(Util.GetJObject(dt.Rows[i], "序号"), out var result);
			string[] array = Util.GetJObject(dt.Rows[i], "代理地址").Split(':');
			string ipAddress = array[0] + ":" + array[1] + ":" + array[2];
			string text = "";
			string text2 = "";
			if (array.Length >= 5)
			{
				text = array[3];
				text2 = array[4];
			}
			try
			{
				await SaveProxy(new JObject
				{
					["Fid"] = 0,
					["FUserId"] = user.Id,
					["FGroupId"] = groupId,
					["FAddress"] = ipAddress,
					["FUserName"] = text,
					["FPassword"] = text2,
					["FSort"] = result
				}, user, curTime);
			}
			catch (Exception ex)
			{
				Util.WriteLog("BiliBili", "代理模块", "测试代理", ipAddress + " 代理错误：" + ex.Message);
			}
		}
	}

	public static void DelGroup(string selected, int userId, string userName, string curTime)
	{
		string text = " UPDATE TProxyGroup SET FCurTime='" + curTime + "' WHERE FUserId=" + userId + " AND Fid IN (" + selected + ")";
		text = text + " UPDATE TProxy SET FCurTime='" + curTime + "' WHERE FUserId=" + userId + " AND FGroupId IN (" + selected + ")";
		SQLHelper.BiliLocalDB.RunSqlStr(text);
		DataCURD.Delete("TProxyGroup", "删除", "FCurTime", curTime, userId, userName, curTime, SQLHelper.BiliLocalDB.InitCnn());
		DataCURD.Delete("TProxy", "删除", "FCurTime", curTime, userId, userName, curTime, SQLHelper.BiliLocalDB.InitCnn());
	}

	public static string SaveGroup(JObject jObject, User user, string curTime)
	{
		string jObject2 = Util.GetJObject(jObject, "Fid");
		string jObject3 = Util.GetJObject(jObject, "FName");
		string sSql = " SELECT Fid FROM TProxyGroup WHERE FName='" + jObject3 + "' AND Fid!=" + jObject2;
		string text = SQLHelper.BiliLocalDB.RunSqlStr(sSql);
		if (text != "")
		{
			throw new Exception("分组名称或分组站址存在重复！");
		}
		jObject["FUserId"] = user.Id;
		if (jObject2 == "0")
		{
			return DataCURD.Save(jObject, "TProxyGroup", "新增代理分组", "Fid", user.Id, user.Name, curTime, SQLHelper.BiliLocalDB.InitCnn());
		}
		return DataCURD.Save(jObject, "TProxyGroup", "编辑代理分组", "Fid", user.Id, user.Name, curTime, SQLHelper.BiliLocalDB.InitCnn());
	}

	public static JArray GetProxyGroupList(int userId)
	{
		string text = " SELECT 0 AS Fid,0 AS FUserId,'' AS FMethod,'默认分组' AS FName,'' AS FAddress,'' AS FSuccessKey,'' AS FSuccessValue,'' AS FDataKey,'' AS FPrefix";
		text += " ,'' AS FIPKey,'' AS FPortKey,'' AS FUserCodeKey,'' AS FUserPWDKey,'' AS FRemarks, -999999999 AS FSort,1 AS FEnable UNION ALL";
		text += " SELECT Fid, FUserId, FMethod, FName, FAddress, FSuccessKey, FSuccessValue, FDataKey, FPrefix, FIPKey, FPortKey, FUserCodeKey, FUserPWDKey, FRemarks, FSort,FEnable";
		text = text + " FROM TProxyGroup WHERE FUserId=" + userId + " ORDER BY FSort";
		DataTable o = SQLHelper.BiliLocalDB.RunSqlDt(text);
		JArray jArray = new JArray();
		jArray.Add(new JObject
		{
			["Fid"] = "",
			["FName"] = "所有分组",
			["children"] = JArray.FromObject(o)
		});
		return jArray;
	}

	public static DataTable GetProxyList(string id, string groupId, string search, string enable, int userId, int limit = 0, int offset = 0, string prop = "", string order = "")
	{
		string text = " SELECT Fid,CASE FEnable WHEN 1 THEN FAddress ELSE '代理已禁用' END AS FAddressName,FGroupId,FAddress,FUserName,FPassword,FMsg,FEnable,ISNULL(T2.FCount,0) AS FCount,FSort";
		text = text + " ,'序号：'+CAST(FSort AS NVARCHAR(100))+' | '+FAddress+':'+FUserName+' | 已用 '+CAST(ISNULL(T2.FCount,0) AS NVARCHAR(100))+' 个' AS FLabel" + SQLHelper.total;
		text = text + " FROM TProxy T1 LEFT JOIN (SELECT FProxyId,COUNT(*) AS FCount FROM TCookies WHERE FProxyId!=0 AND FUserId=" + userId + " GROUP BY FProxyId) T2 ON T2.FProxyId=T1.Fid WHERE FUserId=" + userId;
		if (id != "")
		{
			text = text + " AND Fid=" + id;
		}
		if (groupId != "")
		{
			text = ((!(enable == "1")) ? (text + " AND FGroupId IN (" + groupId + ")") : (text + " AND (FGroupId IN (" + groupId + ") OR FGroupId IN (SELECT Fid FROM TProxyGroup WHERE FMethod='默认分组'))"));
		}
		if (search != "")
		{
			text = text + " AND FAddress LIKE '%" + search + "%'";
		}
		if (enable != "")
		{
			text = text + " AND FEnable=" + enable;
		}
		if (prop == "")
		{
			prop = "FEnable DESC,FSort";
			order = "ASC";
		}
		return SQLHelper.BiliLocalDB.RunSqlDt(text, limit, offset, prop, order);
	}

	public static async Task<string> SaveProxy(JObject jObject, User user, string curTime, bool noTest = false)
	{
		SqlConnection conn = SQLHelper.BiliLocalDB.InitCnn();
		SqlCommand pCmd = conn.CreateCommand();
		pCmd.Transaction = conn.BeginTransaction();
		string id = "0";
		try
		{
			SQLHelper.RunTableLockx("TCookiesTask", pCmd);
			if (Util.GetJObject(jObject, "type") == "TestAll")
			{
				string sSql = " SELECT * FROM TProxy WHERE FUserId=" + user.Id + " AND FGroupId=" + Util.GetJObject(jObject, "groupId");
				JArray jArray = JArray.FromObject(SQLHelper.RunSqlDt(sSql, pCmd));
				foreach (JToken item in jArray)
				{
					jObject = JObject.FromObject(item);
					string text = await TestProxy(jObject);
					if (text.Contains("错误信息"))
					{
						jObject["FEnable"] = 0;
						jObject["FMsg"] = text;
						jObject["FUserId"] = user.Id;
						DataCURD.Save(jObject, "TProxy", "编辑代理", "Fid", user.Id, user.Name, curTime, pCmd);
					}
				}
			}
			else if (Util.GetJObject(jObject, "type") == "all")
			{
				string sSql = " UPDATE TProxy SET FEnable=" + Util.GetJObject(jObject, "FEnable") + " WHERE FUserId=" + user.Id + " AND FGroupId=" + Util.GetJObject(jObject, "groupId");
				SQLHelper.RunSqlText(sSql, pCmd);
			}
			else
			{
				string jObject2 = Util.GetJObject(jObject, "FAddress");
				string jObject3 = Util.GetJObject(jObject, "FGroupId");
				string jObject4 = Util.GetJObject(jObject, "FUserName");
				string sSql = " SELECT ISNULL((SELECT Fid FROM TProxy WHERE FAddress='" + jObject2 + "' AND FUserName='" + jObject4 + "' AND FGroupId=" + jObject3 + " AND FUserId=" + user.Id + "),0)";
				id = SQLHelper.RunSqlStr(sSql, pCmd);
				jObject["Fid"] = id;
				string text2 = await TestProxy(jObject);
				if (text2.Contains("错误信息") && !noTest)
				{
					jObject["FEnable"] = 0;
				}
				jObject["FMsg"] = text2;
				jObject["FUserId"] = user.Id;
				id = DataCURD.Save(jObject, "TProxy", "编辑代理", "Fid", user.Id, user.Name, curTime, pCmd);
			}
			pCmd.Transaction.Commit();
			return id;
		}
		catch (Exception ex)
		{
			pCmd?.Transaction?.Rollback();
			throw new Exception(ex.Message);
		}
		finally
		{
			conn.Close();
			conn.Dispose();
		}
	}

	public static async Task<string> TestProxy(JObject jObject)
	{
		string addr = Util.GetJObject(jObject, "FAddress");
		string jObject2 = Util.GetJObject(jObject, "FUserName");
		string jObject3 = Util.GetJObject(jObject, "FPassword");
		string msg = "";
		try
		{
			if (addr != "")
			{
				HttpClientHandler defaultHandler = new HttpClientHandler
				{
					Proxy = new WebProxy
					{
						Address = new Uri(addr),
						Credentials = new NetworkCredential(jObject2, jObject3)
					}
				};
				HttpClientFactory httpClientFactory = new HttpClientFactory("", new Dictionary<string, string> { { "Content-Type", "application/x-www-form-urlencoded" } }, defaultHandler);
				msg = await httpClientFactory.GetIp();
				Util.WriteLog("BiliBili", "代理模块", "测试代理", (msg.Contains("错误信息") ? (addr + "：") : "") + msg);
			}
			return msg;
		}
		catch (Exception ex)
		{
			Util.WriteLog("BiliBili", "代理模块", "测试代理", (msg.Contains("错误信息") ? (addr + "：") : "") + ex.Message);
			return "错误信息：" + ex.Message;
		}
	}
}
