using System.CodeDom.Compiler;
using System.Runtime.CompilerServices;

namespace System.Text.RegularExpressions.Generated;

[GeneratedCode("System.Text.RegularExpressions.Generator", "8.0.10.46610")]
[SkipLocalsInit]
internal sealed class _003CRegexGenerator_g_003EF096B0AF7CDBBC9952D9841FCC0106FE8A95A05E0F5D575C676BD2604719D3EFE__MyRegex_2 : Regex
{
	private sealed class RunnerFactory : RegexRunnerFactory
	{
		private sealed class Runner : RegexRunner
		{
			protected override void Scan(ReadOnlySpan<char> inputSpan)
			{
				while (TryFindNextPossibleStartingPosition(inputSpan) && !TryMatchAtCurrentPosition(inputSpan) && runtextpos != inputSpan.Length)
				{
					runtextpos++;
					if (_003CRegexGenerator_g_003EF096B0AF7CDBBC9952D9841FCC0106FE8A95A05E0F5D575C676BD2604719D3EFE__Utilities.s_hasTimeout)
					{
						CheckTimeout();
					}
				}
			}

			private bool TryFindNextPossibleStartingPosition(ReadOnlySpan<char> inputSpan)
			{
				int num = runtextpos;
				if ((uint)num < (uint)inputSpan.Length)
				{
					int num2 = inputSpan.Slice(num).IndexOfAnyDigit();
					if (num2 >= 0)
					{
						runtextpos = num + num2;
						return true;
					}
				}
				runtextpos = inputSpan.Length;
				return false;
			}

			private bool TryMatchAtCurrentPosition(ReadOnlySpan<char> inputSpan)
			{
				int num = runtextpos;
				int start = num;
				ReadOnlySpan<char> readOnlySpan = inputSpan.Slice(num);
				int i;
				for (i = 0; (uint)i < (uint)readOnlySpan.Length && char.IsDigit(readOnlySpan[i]); i++)
				{
				}
				if (i == 0)
				{
					return false;
				}
				readOnlySpan = readOnlySpan.Slice(i);
				Capture(0, start, runtextpos = num + i);
				return true;
			}
		}

		protected override RegexRunner CreateInstance()
		{
			return new Runner();
		}
	}

	internal static readonly _003CRegexGenerator_g_003EF096B0AF7CDBBC9952D9841FCC0106FE8A95A05E0F5D575C676BD2604719D3EFE__MyRegex_2 Instance = new _003CRegexGenerator_g_003EF096B0AF7CDBBC9952D9841FCC0106FE8A95A05E0F5D575C676BD2604719D3EFE__MyRegex_2();

	private _003CRegexGenerator_g_003EF096B0AF7CDBBC9952D9841FCC0106FE8A95A05E0F5D575C676BD2604719D3EFE__MyRegex_2()
	{
		pattern = "\\d+";
		roptions = RegexOptions.None;
		Regex.ValidateMatchTimeout(_003CRegexGenerator_g_003EF096B0AF7CDBBC9952D9841FCC0106FE8A95A05E0F5D575C676BD2604719D3EFE__Utilities.s_defaultTimeout);
		internalMatchTimeout = _003CRegexGenerator_g_003EF096B0AF7CDBBC9952D9841FCC0106FE8A95A05E0F5D575C676BD2604719D3EFE__Utilities.s_defaultTimeout;
		factory = new RunnerFactory();
		capsize = 1;
	}
}
