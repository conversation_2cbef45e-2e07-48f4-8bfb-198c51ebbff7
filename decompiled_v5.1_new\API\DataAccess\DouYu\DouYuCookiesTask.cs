using System;
using System.Data;
using API.Common;
using API.Models.Comm;
using Microsoft.Data.SqlClient;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace API.DataAccess.DouYu;

public class DouYuCookiesTask
{
	public static void RestoreDefault(string cookieId, string areaId, int userId, string userName, string curTime)
	{
		SqlConnection sqlConnection = SQLHelper.DouYuLocalDB.InitCnn();
		SqlCommand sqlCommand = sqlConnection.CreateCommand();
		sqlCommand.Transaction = sqlConnection.BeginTransaction();
		try
		{
			string text = " UPDATE TCookiesTask SET FCompulsory=TT.FCompulsory,FSort=TT.FSort,FEnable=TT.FEnable";
			text += " FROM TAreaTask TT WHERE TT.FTaskId=TCookiesTask.FTaskId AND TT.FAreaId=TCookiesTask.FAreaId";
			text = text + " AND TCookiesTask.FUserId=" + userId + " AND TCookiesTask.FCookieId=" + cookieId;
			if (areaId != "")
			{
				text = text + " AND TT.FAreaId=" + areaId;
			}
			SQLHelper.DouYuLocalDB.RunSqlText(text);
			DataCURD.WriteLog("TCookiesTask", "恢复默认值", "FAreaTaskId", "", JsonConvert.SerializeObject(new JObject
			{
				["type"] = "编辑",
				["data"] = new JArray()
			}), userId, userName, curTime, sqlCommand);
			sqlCommand.Transaction.Commit();
		}
		catch (Exception ex)
		{
			sqlCommand.Transaction.Rollback();
			throw new Exception(ex.Message);
		}
		finally
		{
			sqlConnection.Close();
			sqlConnection.Dispose();
		}
	}

	public static void SyncAll(string cookieId, string areaId, int userId, string userName, string curTime)
	{
		SqlConnection sqlConnection = SQLHelper.DouYuLocalDB.InitCnn();
		SqlCommand sqlCommand = sqlConnection.CreateCommand();
		sqlCommand.Transaction = sqlConnection.BeginTransaction();
		try
		{
			string text = " UPDATE T1 SET T1.FCompulsory=TT.FCompulsory,T1.FSort=TT.FSort,T1.FEnable=TT.FEnable";
			text += " FROM TCookiesTask T1,TCookiesTask TT WHERE TT.FTaskId=T1.FTaskId AND TT.FAreaId=T1.FAreaId";
			text = text + " AND TT.FUserId=" + userId + " AND TT.FCookieId=" + cookieId;
			if (areaId != "")
			{
				text = text + " AND TT.FAreaId=" + areaId;
			}
			SQLHelper.DouYuLocalDB.RunSqlText(text);
			DataCURD.WriteLog("TCookiesTask", "同步所有", "FAreaTaskId", "", JsonConvert.SerializeObject(new JObject
			{
				["type"] = "编辑",
				["data"] = new JArray()
			}), userId, userName, curTime, sqlCommand);
			sqlCommand.Transaction.Commit();
		}
		catch (Exception ex)
		{
			sqlCommand.Transaction.Rollback();
			throw new Exception(ex.Message);
		}
		finally
		{
			sqlConnection.Close();
			sqlConnection.Dispose();
		}
	}

	public static void DelAll(string cookieId, string areaId, int userId, string userName, string curTime)
	{
		SqlConnection sqlConnection = SQLHelper.DouYuLocalDB.InitCnn();
		SqlCommand sqlCommand = sqlConnection.CreateCommand();
		sqlCommand.Transaction = sqlConnection.BeginTransaction();
		try
		{
			if (areaId == "")
			{
				throw new Exception("请选择分区！");
			}
			string sSql = " SELECT * FROM TCookiesTask WHERE FUserId=" + userId + " AND FCookieId=" + cookieId + " AND FAreaId=" + areaId;
			DataTable o = SQLHelper.DouYuLocalDB.RunSqlDt(sSql);
			sSql = " DELETE TCookiesTask WHERE FUserId=" + userId + " AND FCookieId=" + cookieId + " AND FAreaId=" + areaId;
			SQLHelper.DouYuLocalDB.RunSqlText(sSql);
			DataCURD.WriteLog("TCookiesTask", "删除账号所有任务", "FAreaTaskId", "", JsonConvert.SerializeObject(new JObject
			{
				["type"] = "删除",
				["data"] = JArray.FromObject(o)
			}), userId, userName, curTime, sqlCommand);
			sqlCommand.Transaction.Commit();
		}
		catch (Exception ex)
		{
			sqlCommand.Transaction.Rollback();
			throw new Exception(ex.Message);
		}
		finally
		{
			sqlConnection.Close();
			sqlConnection.Dispose();
		}
	}

	public static void DelCookiesTask(string cookieId, string areaId, int userId)
	{
		string text = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss:fff");
		string sSql = " UPDATE TCookiesTask SET FDate='" + text + "' WHERE FCookieId IN (" + cookieId + ") AND FAreaId=" + areaId + " AND FUserId=" + userId;
		SQLHelper.DouYuLocalDB.RunSqlText(sSql);
		DataCURD.Delete("TCookiesTask", "定时任务批量操作", "FDate", text, userId, "定时任务", text, SQLHelper.DouYuLocalDB.InitCnn());
	}

	public static DataTable GetCookiesTaskList(string cookieId, string areaId, int userId, int limit = 0, int offset = 0, string prop = "", string order = "")
	{
		string text = " SELECT T2.FName AS FAreaName";
		text += " ,CASE WHEN T1.FEnable=0 THEN '已禁用' WHEN T1.FStatus=4 THEN '已抢光' WHEN T1.FStatus = 1 THEN '未完成' WHEN T1.FStatus= 2 THEN '待领取' WHEN T1.FStatus = 3 THEN '已领取' WHEN T1.FStatus = 5 THEN '任务结束' END AS FStatusName";
		text += " ,CASE T1.FDaily WHEN 1 THEN '√' ELSE '' END AS FDailyName,CASE T1.FComplete WHEN 1 THEN '√' ELSE '' END AS FCompleteName";
		text += " ,CASE T1.FStatus WHEN 1 THEN -9998 WHEN 2 THEN -9999 ELSE T1.FSort END AS FSort2";
		text += " ,T1.* FROM TCookiesTask T1 LEFT JOIN TArea T2 ON T2.Fid=T1.FAreaId";
		text = text + " WHERE 1=1 AND T1.FCookieId=" + cookieId + " AND T1.FUserId=" + userId;
		if (areaId != "")
		{
			text = text + " AND T1.FAreaId IN(" + areaId + ")";
		}
		if (prop == "")
		{
			prop = " T1.FEnable DESC,T1.FDaily ASC,FSort2,T1.FSort";
			order = "ASC";
		}
		return SQLHelper.DouYuLocalDB.RunSqlDt(text, limit, offset, prop, order);
	}

	public static void SetCompulsory(JObject jObject, int userId, string userName, string curTime)
	{
		DataCURD.Save(jObject, "TCookiesTask", "设置或取消强制任务", "Fid", userId, userName, curTime, SQLHelper.DouYuLocalDB.InitCnn());
	}

	public static void SetSort(JObject jObject, int userId, string userName, string curTime)
	{
		DataCURD.Save(jObject, "TCookiesTask", "设置任务排序", "Fid", userId, userName, curTime, SQLHelper.DouYuLocalDB.InitCnn());
	}

	public static int EnableSwitch(JObject jObject, int userId, string userName, string curTime)
	{
		string jObject2 = Util.GetJObject(jObject, "Fid");
		string jObject3 = Util.GetJObject(jObject, "FEnable");
		string text = Util.GetJObject(jObject, "FCompulsory");
		if (jObject3 != "1")
		{
			text = "0";
		}
		DataCURD.Save(new JObject
		{
			["Fid"] = jObject2,
			["FEnable"] = jObject3,
			["FCompulsory"] = text
		}, "TCookiesTask", "启动禁用任务", "Fid", userId, userName, curTime, SQLHelper.DouYuLocalDB.InitCnn());
		return int.Parse(jObject3);
	}

	public static DataTable GetReceiveTaskList(string areaId, string cookieId, TaskType taskType, int userId)
	{
		string text = " SELECT * FROM TCookiesTask T1";
		text = text + " WHERE T1.FEnable=1 AND T1.FUserId=" + userId;
		if (areaId != "")
		{
			text = text + " AND T1.FAreaId IN (" + areaId + ")";
		}
		if (cookieId != "")
		{
			text = text + " AND T1.FCookieId IN (" + cookieId + ")";
		}
		switch (taskType)
		{
		case TaskType.Normal:
			text += " AND T1.FDaily=0 AND T1.FStatus=2";
			break;
		case TaskType.Daily:
			text += " AND T1.FDaily=1";
			break;
		}
		text += "ORDER BY T1.FCompulsory DESC,T1.FSort";
		return SQLHelper.DouYuLocalDB.RunSqlDt(text);
	}
}
