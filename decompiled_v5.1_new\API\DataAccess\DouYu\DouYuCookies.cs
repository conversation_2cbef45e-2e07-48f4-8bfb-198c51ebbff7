using System;
using System.Data;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using API.Common;
using API.DataAccess.System;
using API.Models.Comm;
using Microsoft.Data.SqlClient;
using Newtonsoft.Json.Linq;

namespace API.DataAccess.DouYu;

public class DouYuCookies
{
	public static async Task AddCookies(string cookieNum, int userId, string userName, string curTime, SqlCommand? pCmd = null)
	{
		SqlConnection cnn = null;
		try
		{
			if (pCmd == null)
			{
				cnn = SQLHelper.DouYuLocalDB.InitCnn();
				pCmd = cnn.CreateCommand();
				pCmd.Transaction = cnn.BeginTransaction();
			}
			if (!int.TryParse(cookieNum, out var result))
			{
				throw new Exception("添加的账号数量不正确！");
			}
			if (10 < result || result <= 0)
			{
				throw new Exception("添加的账号数量不正确！");
			}
			JObject jObjectParam = new JObject
			{
				["userId"] = userId,
				["userName"] = userName,
				["type"] = "斗鱼",
				["addCount"] = cookieNum
			};
			Request request = new Request
			{
				jObjectParam = jObjectParam,
				curTime = curTime
			};
			object obj = await Util.Request("/Common/AddCookies", request);
			JArray jArray = ((obj != null) ? JArray.FromObject(obj) : SysUserCookies.AddCookies(request));
			DataTable sourceDataTable = jArray.ToObject<DataTable>() ?? new DataTable();
			SQLHelper.SqlBulkCopyByDataTable("TCookies", sourceDataTable, pCmd);
			if (cnn != null)
			{
				pCmd.Transaction.Commit();
			}
		}
		catch (Exception ex)
		{
			if (cnn != null)
			{
				pCmd?.Transaction.Rollback();
			}
			throw new Exception(ex.Message.ToString());
		}
		finally
		{
			if (cnn != null)
			{
				cnn.Close();
				cnn.Dispose();
			}
		}
	}

	public static DataTable GetCookiesList(string id, string areaId, string search, string enable, int userId, int limit = 0, int offset = 0, string prop = "", string order = "")
	{
		areaId = ((areaId == "") ? "0" : areaId);
		string text = " ";
		text += " SELECT T1.Fid,T1.FKey,T1.FName,T1.FRoomId,T1.FIdentifying,T1.FCookie,T1.FHeaders,T1.FCountryId,T1.FTel,T1.FSMSUrl";
		text += " ,'https://www.douyu.com/'+T1.FRoomId AS FRoomNo ,T1.FExpirationTime,T1.FProxyId,T1.FSort,T1.FCookieExpires,T1.FInfo";
		text += " ,CASE WHEN T1.FExpirationTime<=GETDATE() THEN '账号已到期' WHEN T1.FBrowserStatus=0 THEN '浏览器登录失效'";
		text += "       WHEN T1.FCookieExpires <=GETDATE() THEN 'Cookie已过期' ELSE T1.FStatus END AS FStatusName";
		text += " ,T2.FAddress AS FProxyAddress,T2.FUserName AS FProxyUserName,T2.FPassword AS FProxyPassword";
		text += " ,T3.FTaskName";
		text = text + SQLHelper.total + " FROM TCookies T1 ";
		text = text + " LEFT JOIN TProxy T2 ON T2.FEnable=1 AND T2.Fid=T1.FProxyId AND T2.FUserId=" + userId;
		text += " LEFT JOIN (SELECT TT.FCookieId,TT.FTaskName FROM (SELECT FCookieId,FTaskName, ROW_NUMBER() OVER (PARTITION BY FCookieId ORDER BY FSort) AS FNo ";
		text = text + " FROM TCookiesTask WHERE FEnable=1 AND FDaily=0 AND FStatus=2 AND FUserId=" + userId + " AND FAreaId=" + areaId + ")";
		text += " TT WHERE TT.FNo = 1) T3 ON T3.FCookieId=T1.Fid";
		text = text + " WHERE T1.FUserId=" + userId;
		if (id != "")
		{
			text = text + " AND T1.Fid IN (" + id + ")";
		}
		if (search != "")
		{
			text = text + " AND ( T1.FName LIKE '%" + search + "%' OR T1.FRoomId LIKE '%" + search + "%' )";
		}
		if (enable == "1")
		{
			text += " AND T1.FExpirationTime>GETDATE()";
		}
		if (prop == "")
		{
			prop = "FSort";
			order = "ASC";
		}
		return SQLHelper.DouYuLocalDB.RunSqlDt(text, limit, offset, prop, order);
	}

	public static DataTable GetCookiesListByKey(string id)
	{
		string sSql = " SELECT FName,FRoomId,FIdentifying,FCsrf,FCookie,FHeaders,FInfo FROM TCookies WHERE Fid=" + id;
		return SQLHelper.DouYuLocalDB.RunSqlDt(sSql);
	}

	public static DataTable GetCookiesList(string id, string identifying, int userId)
	{
		string text = " ";
		text += " SELECT T1.Fid,T1.FKey,T1.FName,T1.FRoomId,T1.FIdentifying,T1.FCookie,T1.FHeaders,T1.FCountryId,T1.FTel,T1.FSMSUrl,T1.FProxyId";
		text += " ,T2.FAddress AS FProxyAddress,T2.FUserName AS FProxyUserName,T2.FPassword AS FProxyPassword";
		text = text + SQLHelper.total + " FROM TCookies T1 ";
		text = text + " LEFT JOIN TProxy T2 ON T2.FEnable=1 AND T2.Fid=T1.FProxyId AND T2.FUserId=" + userId;
		text = text + " WHERE T1.FUserId=" + userId;
		text += " AND T1.FExpirationTime>GETDATE()";
		if (identifying != "")
		{
			text = text + " AND T1.FIdentifying IN (" + identifying + ")";
		}
		if (id != "")
		{
			text = text + " AND T1.Fid IN (" + id + ")";
		}
		return SQLHelper.DouYuLocalDB.RunSqlDt(text);
	}

	public static async Task EditCookie(JObject jObject, int userId, string userName, string curTime, SqlCommand? pCmd = null)
	{
		SqlConnection cnn = null;
		try
		{
			if (pCmd == null)
			{
				cnn = SQLHelper.DouYuLocalDB.InitCnn();
				pCmd = cnn.CreateCommand();
				pCmd.Transaction = cnn.BeginTransaction();
			}
			string jObject2 = Util.GetJObject(jObject, "FIdentifying");
			string jObject3 = Util.GetJObject(jObject, "FKey");
			if (jObject2 != "")
			{
				Request request = new Request
				{
					jObjectParam = new JObject
					{
						["userId"] = userId,
						["userName"] = userName,
						["key"] = jObject3,
						["identifying"] = jObject2
					},
					curTime = curTime
				};
				object obj = await Util.Request("/Common/EditCookie", request);
				if (obj == null)
				{
					obj = SysUserCookies.EditCookie(request);
				}
				jObject["FExpirationTime"] = obj.ToString();
			}
			DataCURD.Save(jObject, "TCookies", "编辑账号", "Fid", userId, userName, curTime, pCmd);
			if (cnn != null)
			{
				pCmd.Transaction.Commit();
			}
		}
		catch (Exception ex)
		{
			if (cnn != null)
			{
				pCmd?.Transaction.Rollback();
			}
			throw new Exception(ex.Message.ToString());
		}
		finally
		{
			if (cnn != null)
			{
				cnn.Close();
				cnn.Dispose();
			}
		}
	}

	public static async Task DelCookie(JObject jObject, int userId, string userName, string curTime, SqlCommand? pCmd = null)
	{
		SqlConnection cnn = null;
		try
		{
			if (pCmd == null)
			{
				cnn = SQLHelper.DouYuLocalDB.InitCnn();
				pCmd = cnn.CreateCommand();
				pCmd.Transaction = cnn.BeginTransaction();
			}
			string id = Util.GetJObject(jObject, "Fid");
			string jObject2 = Util.GetJObject(jObject, "FIdentifying");
			string key = Util.GetJObject(jObject, "FKey");
			Request request = new Request
			{
				jObjectParam = new JObject
				{
					["userId"] = userId,
					["userName"] = userName,
					["key"] = key,
					["identifying"] = jObject2
				},
				curTime = curTime
			};
			if (await Util.Request("/Common/DelCookie", request) == null)
			{
				_ = (object)SysUserCookies.DelCookie(request);
			}
			DataCURD.Delete("TCookies", "删除账号", "Fid", id, userId, userName, curTime, pCmd);
			DataCURD.Delete("TCdkey", "删除Cdkey", "FCookieId", id, userId, userName, curTime, pCmd);
			DataCURD.Delete("TCookiesLive", "删除直播视频", "FCookieId", id, userId, userName, curTime, pCmd);
			DataCURD.Delete("TCookiesLiveBulletScreen", "删除直播弹幕", "FCookieId", id, userId, userName, curTime, pCmd);
			DataCURD.Delete("TCookiesLiveConfig", "删除直播配置", "FCookieId", id, userId, userName, curTime, pCmd);
			DataCURD.Delete("TCookiesTask", "删除任务信息", "FCookieId", id, userId, userName, curTime, pCmd);
			try
			{
				string path = Directory.GetCurrentDirectory() + "\\Util\\Chromium\\DouYu-" + key;
				if (!Directory.Exists(path))
				{
					Directory.Delete(path, recursive: true);
				}
			}
			catch (IOException ex)
			{
				Console.WriteLine("无法删除文件夹: " + ex.Message);
			}
			catch (UnauthorizedAccessException ex2)
			{
				Console.WriteLine("没有权限删除文件夹: " + ex2.Message);
			}
			if (cnn != null)
			{
				pCmd.Transaction.Commit();
			}
		}
		catch (Exception ex3)
		{
			if (cnn != null)
			{
				pCmd?.Transaction.Rollback();
			}
			throw new Exception(ex3.Message.ToString());
		}
		finally
		{
			if (cnn != null)
			{
				cnn.Close();
				cnn.Dispose();
			}
		}
	}

	public static async Task EmptyCookie(JObject jObject, int userId, string userName, string curTime, SqlCommand? pCmd = null)
	{
		SqlConnection cnn = null;
		try
		{
			if (pCmd == null)
			{
				cnn = SQLHelper.DouYuLocalDB.InitCnn();
				pCmd = cnn.CreateCommand();
				pCmd.Transaction = cnn.BeginTransaction();
			}
			string id = Util.GetJObject(jObject, "Fid");
			string jObject2 = Util.GetJObject(jObject, "FIdentifying");
			string key = Util.GetJObject(jObject, "FKey");
			Request request = new Request
			{
				jObjectParam = new JObject
				{
					["userId"] = userId,
					["userName"] = userName,
					["key"] = key,
					["identifying"] = jObject2
				},
				curTime = curTime
			};
			if (await Util.Request("/Common/EmptyCookie", request) == null)
			{
				_ = (object)SysUserCookies.DelCookie(request);
			}
			jObject = new JObject
			{
				["Fid"] = id,
				["FStatus"] = "",
				["FName"] = "",
				["FRoomId"] = "",
				["FIdentifying"] = "",
				["FCookie"] = "",
				["FHeaders"] = "",
				["FInfo"] = "",
				["FCountryId"] = "0",
				["FTel"] = "",
				["FSMSUrl"] = "",
				["FProxyId"] = "0",
				["FSort"] = "9999",
				["FCookieExpires"] = "NULL",
				["FBrowserStatus"] = "NULL"
			};
			DataCURD.Save(jObject, "TCookies", "重置账号", "Fid", userId, userName, curTime, pCmd);
			DataCURD.Delete("TCdkey", "重置账号Cdkey", "FCookieId", id, userId, userName, curTime, pCmd);
			DataCURD.Delete("TCookiesLive", "重置账号直播视频", "FCookieId", id, userId, userName, curTime, pCmd);
			DataCURD.Delete("TCookiesLiveBulletScreen", "重置账号直播弹幕", "FCookieId", id, userId, userName, curTime, pCmd);
			DataCURD.Delete("TCookiesLiveConfig", "重置账号直播配置", "FCookieId", id, userId, userName, curTime, pCmd);
			DataCURD.Delete("TCookiesTask", "重置账号任务信息", "FCookieId", id, userId, userName, curTime, pCmd);
			try
			{
				string path = Directory.GetCurrentDirectory() + "\\Util\\Chromium\\DouYu-" + key;
				if (!Directory.Exists(path))
				{
					Directory.Delete(path, recursive: true);
				}
			}
			catch (IOException ex)
			{
				Console.WriteLine("无法删除文件夹: " + ex.Message);
			}
			catch (UnauthorizedAccessException ex2)
			{
				Console.WriteLine("没有权限删除文件夹: " + ex2.Message);
			}
			if (cnn != null)
			{
				pCmd.Transaction.Commit();
			}
		}
		catch (Exception ex3)
		{
			if (cnn != null)
			{
				pCmd?.Transaction.Rollback();
			}
			throw new Exception(ex3.Message.ToString());
		}
		finally
		{
			if (cnn != null)
			{
				cnn.Close();
				cnn.Dispose();
			}
		}
	}

	public static async Task UpdateCookieExpires(int userId, string userName, string curTime)
	{
		Request request = new Request
		{
			jObjectParam = new JObject
			{
				["userId"] = userId,
				["userName"] = userName,
				["type"] = "斗鱼"
			},
			curTime = curTime
		};
		object obj = await Util.Request("/Common/GetUserCookiesList", request);
		if (obj == null)
		{
			obj = SysUserCookies.GetUserCookiesList(request);
		}
		JArray source = JArray.FromObject(obj);
		DataTable cookiesList = GetCookiesList("", "", "", "", userId);
		foreach (JObject item in source.Cast<JObject>())
		{
			DataRow[] array = cookiesList.Select("FKey='" + Util.GetJObject(item, "FKey") + "'") ?? Array.Empty<DataRow>();
			if (array.Length != 0)
			{
				DataCURD.Save(item, "TCookies", "更新到期时间", "FKey", userId, userName, curTime, SQLHelper.DouYuLocalDB.InitCnn());
				continue;
			}
			item["Fid"] = 0;
			item["FUserId"] = userId;
			DataCURD.Save(item, "TCookies", "新增账号", "Fid", userId, userName, curTime, SQLHelper.DouYuLocalDB.InitCnn());
		}
	}

	public static string GetCookieName(string key, string id = "")
	{
		string text = " SELECT FName FROM TCookies WHERE 1=1";
		if (key != "")
		{
			text = text + " AND FKey='" + key + "'";
		}
		else
		{
			if (!(id != ""))
			{
				return "未知账号";
			}
			text = text + " AND Fid=" + id;
		}
		return SQLHelper.DouYuLocalDB.RunSqlStr(text);
	}
}
