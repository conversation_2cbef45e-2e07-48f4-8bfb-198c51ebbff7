using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using API.Common;
using API.DataAccess.BiliBili;
using API.Models.Comm;
using Microsoft.AspNetCore.Mvc;

namespace API.Controllers.BiliBili;

public class BiliArticlesPlayController : Controller
{
	[HttpPost]
	public Response GetArticlesPlayList([FromBody] Request model, User user)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectSearch, "search");
			string jObject2 = Util.GetJObject(model.jObjectSearch, "areaId");
			string jObject3 = Util.GetJObject(model.jObjectSearch, "st");
			string jObject4 = Util.GetJObject(model.jObjectSearch, "et");
			response.data = Util.GetTableResponse(BiliArticlesPlay.GetArticlesPlayList(jObject2, jObject, jObject3, jObject4, user.Id, model.limit, model.offset, model.prop, model.order));
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public async Task<Response> ImportArticlesPlay([FromBody] Request model, User user)
	{
		Response mRet = new Response();
		try
		{
			string src = Util.GetJObject(model.jObjectParam, "src");
			int player = Util.GetJObject<int>(model.jObjectParam, "player");
			string type = Util.GetJObject(model.jObjectParam, "type");
			int num = Util.GetJObject<int>(model.jObjectParam, "num");
			decimal group = Util.GetJObject<decimal>(model.jObjectParam, "group");
			new List<Task>(1).Add(Task.Run(async delegate
			{
				await BiliArticlesPlay.ImportArticlesPlay(src, player, type, num, group, model.curTime, user);
			}));
			await Task.Delay(1000);
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}

	[HttpPost]
	public async Task<Response> ContinuePlayer([FromBody] Request model, User user)
	{
		Response mRet = new Response();
		try
		{
			int player = Util.GetJObject<int>(model.jObjectParam, "player");
			string type = Util.GetJObject(model.jObjectParam, "type");
			int num = Util.GetJObject<int>(model.jObjectParam, "num");
			decimal group = Util.GetJObject<decimal>(model.jObjectParam, "group");
			new List<Task>(1).Add(Task.Run(async delegate
			{
				await BiliArticlesPlay.ImportArticlesPlay("", player, type, num, group, model.curTime, user);
			}));
			await Task.Delay(1000);
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}

	[HttpPost]
	public Response BreakPlayer(User user)
	{
		Response response = new Response();
		try
		{
			BiliArticlesPlay.BreakPlayer(user);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}
}
