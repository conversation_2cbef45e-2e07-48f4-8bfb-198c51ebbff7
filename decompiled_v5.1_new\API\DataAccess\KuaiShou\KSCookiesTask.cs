using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using API.Common;
using Microsoft.Data.SqlClient;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace API.DataAccess.KuaiShou;

public class KSCookiesTask
{
	public static async Task<JArray?> GetReceiveTaskList(int userId)
	{
		if (userId == 0)
		{
			throw new Exception("请重新登陆！");
		}
		string text = "https://mate.gifshow.com/rest/n/live/mate/authortask/v2/tasks?";
		text += "gameId=-1&kpf=IPHONE&appver=**********&kpn=KUAISHOU_LIVE_MATE&c=a&ud=&mod=iPhone12%2C3&sys=ios15.2.1&ver=4.1&net=WIFI&did=&language=zh-Hans-CN%3Bq%3D1&token=&client_key=&country_code=cn";
		HttpClientFactory httpClientFactory = new HttpClientFactory();
		JObject jObject = await httpClientFactory.Get(text, new Dictionary<string, string> { { "Content-Type", "application/json" } });
		if (Util.GetJObject(jObject, "result") == "1")
		{
			return Util.GetJObject<JArray>(jObject, "tasks");
		}
		throw new Exception(JsonConvert.SerializeObject(jObject));
	}

	public static async Task<string> ReceiveTask(string cookieId, string value, int userId)
	{
		DataTable dt = KSCookies.GetCookiesList("", cookieId, "", userId);
		if (dt.Rows.Count == 0)
		{
			throw new Exception("请选择账号");
		}
		string message = "";
		for (int i = 0; i < dt.Rows.Count; i++)
		{
			string name = Util.GetJObject(dt.Rows[i], "FName");
			string jObject = Util.GetJObject(dt.Rows[i], "FCookie");
			string cookieByKey = Util.GetCookieByKey(jObject, "token");
			string cookieByKey2 = Util.GetCookieByKey(jObject, "client_key");
			string jObject2 = Util.GetJObject(dt.Rows[i], "FIdentifying");
			string cookieByKey3 = Util.GetCookieByKey(jObject, "did");
			HttpClientFactory httpClientFactory = new HttpClientFactory(jObject2);
			string text = "https://mate.gifshow.com/rest/n/live/mate/authortask/v2/receive-task";
			text = text + "?kpf=IPHONE&net=WIFI&appver=5.11.21.262&kpn=KUAISHOU_LIVE_MATE&taskType=2&c=a&ud=" + jObject2 + "&mod=iPhone12%2C3&ver=5.11&taskId=" + value + "&did=" + cookieByKey3 + "&sys=ios15.2.1&language=zh-Hans-CN%3Bq%3D1&client_key=" + cookieByKey2 + "&country_code=cn&token=" + cookieByKey;
			string jObject3 = Util.GetJObject(await httpClientFactory.Get(text, new Dictionary<string, string> { { "Content-Type", "application/json" } }), "result");
			if (jObject3 != "1")
			{
				message = message + name + ":领取失败" + jObject3 + ",";
			}
		}
		return message + "其他账号已经领取成功。PS：68005已领取";
	}

	public static void EditTag(string task, string tag, int userId, string userName, string curTime)
	{
		DataCURD.Delete("TTaskTag", "删除任务与Tag对照", "FTask", task, userId, userName, curTime, SQLHelper.KSLocalDB.InitCnn());
		DataCURD.Save(new JObject
		{
			["Fid"] = 0,
			["FTask"] = task,
			["FTag"] = tag
		}, "TTaskTag", "添加任务与Tag对照", "Fid", userId, userName, curTime, SQLHelper.KSLocalDB.InitCnn());
		string sSql = " DELETE TTaskTag WHERE FTask NOT IN (SELECT FTitle FROM TCookiesTask)";
		SQLHelper.KSLocalDB.RunSqlText(sSql);
	}

	public static string ExportCdkeyList(string cookieId, int userId)
	{
		string text = " SELECT  FName AS '账号名称',FTitle AS '分区名称',FSubTitle AS '任务名称',FSubRewardName AS '奖励名称',FSubCdkey AS 'CDKEY' FROM TCookiesTask T1";
		text = text + " LEFT JOIN TCookies T2 ON T2.Fid=T1.FCookieId WHERE T1.FSubCdkey!='' AND T1.FUserId=" + userId;
		if (cookieId != "")
		{
			text = text + " AND T1.FCookieId IN (" + cookieId + ")";
		}
		text += " ORDER BY T2.FSort DESC";
		DataTable dt = SQLHelper.KSLocalDB.RunSqlDt(text);
		Util.FileDownload(out string absolute, out string relative);
		ExcelHelper.X2003.TableToExcelForXLS(dt, absolute);
		return relative;
	}

	public static void SyncAll(string cookieId, int userId, string userName, string curTime)
	{
		SqlConnection sqlConnection = SQLHelper.KSLocalDB.InitCnn();
		SqlCommand sqlCommand = sqlConnection.CreateCommand();
		sqlCommand.Transaction = sqlConnection.BeginTransaction();
		try
		{
			string text = " UPDATE T1 SET T1.FEnable=TT.FEnable,T1.FStartRecordId=TT.FStartRecordId";
			text += " FROM TCookiesTask T1,TCookiesTask TT WHERE TT.FSubTaskId=T1.FSubTaskId AND TT.FTaskId=T1.FTaskId";
			text = text + " AND TT.FUserId=" + userId + " AND TT.FCookieId=" + cookieId;
			SQLHelper.RunSqlText(text, sqlCommand);
			DataCURD.WriteLog("TCookiesTask", "同步所有", "FSubTaskId", "", JsonConvert.SerializeObject(new JObject
			{
				["type"] = "编辑",
				["data"] = new JArray()
			}), userId, userName, curTime, sqlCommand);
			sqlCommand.Transaction.Commit();
		}
		catch (Exception ex)
		{
			sqlCommand.Transaction.Rollback();
			throw new Exception(ex.Message);
		}
		finally
		{
			sqlConnection.Close();
			sqlConnection.Dispose();
		}
	}

	public static DataTable GetTaskList(int userId, string taskName = "")
	{
		string text = " SELECT FTaskId AS Fid,FTitle AS FName,ISNULL(T2.FTag,'') AS FTag FROM TCookiesTask T1 LEFT JOIN TTaskTag T2 ON T2.FTask=T1.FTitle WHERE FUserId=" + userId;
		if (taskName != "")
		{
			text = text + " AND FTitle='" + taskName + "'";
		}
		text += "  GROUP BY FTaskId,FTitle,FTag";
		return SQLHelper.KSLocalDB.RunSqlDt(text);
	}

	public static DataTable GetCookiesTaskList(string cookieId, string taskId, string search, int userId)
	{
		string text = " SELECT * FROM TCookiesTask WHERE FUserId=" + userId;
		if (cookieId != "")
		{
			text = text + " AND FCookieId IN (" + cookieId + ")";
		}
		if (taskId != "")
		{
			text = text + " AND FTaskId IN (" + taskId + ")";
		}
		if (search != "")
		{
			text = text + " AND (FSubRewardName LIKE '%" + search + "%' OR FSubTitle LIKE '%" + search + "%')";
		}
		text += " ORDER BY CASE WHEN FSubCdkey!='' THEN 2 WHEN FSubRemainNum='0' THEN 3 WHEN FEnable=1 THEN 0 ELSE 1 END ASC";
		return SQLHelper.KSLocalDB.RunSqlDt(text);
	}

	public static void UpdateCookiesTask(string cookieId, int userId, string userName, string curTime)
	{
		List<Task> list = new List<Task>();
		ConcurrentBag<JToken> bagJArray = new ConcurrentBag<JToken>();
		DataTable cookiesList = KSCookies.GetCookiesList("", cookieId, "", userId);
		foreach (DataRow row in cookiesList.Rows)
		{
			string id = Util.GetJObject(row, "Fid");
			string identifying = Util.GetJObject(row, "FIdentifying");
			string jObject = Util.GetJObject(row, "FCookie");
			string token = Util.GetCookieByKey(jObject, "token");
			string odid = Util.GetCookieByKey(jObject, "odid");
			string client_key = Util.GetCookieByKey(jObject, "client_key");
			DataCURD.Delete("TCookiesTask", "删除Cookie任务", "FCookieId", id, userId, userName, curTime, SQLHelper.KSLocalDB.InitCnn());
			list.Add(Task.Run(async delegate
			{
				HttpClientFactory httpClientFactory = new HttpClientFactory(identifying, new Dictionary<string, string> { { "Content-Type", "application/json;charset=UTF-8" } });
				string text = "https://mate.ksapisrv.com/rest/n/live/mate/authortask/rewardRecords";
				text = text + "?appver=5.11.21.262&c=a&did=" + odid + "&ver=5.11&kpn=KUAISHOU_LIVE_MATE&ud=" + identifying + "&kpf=IPHONE&mod=iPhone12,3&sys=ios15.2.1&net=WIFI";
				Dictionary<string, string> dictionary = new Dictionary<string, string>
				{
					{ "appver", "5.11.21.262" },
					{ "c", "a" },
					{ "did", odid },
					{ "ver", "5.11" },
					{ "kpn", "KUAISHOU_LIVE_MATE" },
					{ "ud", identifying },
					{ "kpf", "IPHONE" },
					{ "mod", "iPhone12,3" },
					{ "sys", "ios15.2.1" },
					{ "net", "WIFI" },
					{ "client_key", client_key },
					{ "count", "20" },
					{ "country_code", "cn" },
					{ "language", "zh-Hans-CN;q=1" },
					{ "pcursor", "" },
					{ "token", token }
				};
				string text2 = "client_key=" + client_key + "&count=20&country_code=cn&language=zh-Hans-CN;q=1&pcursor=&sig=" + Util.Signature(dictionary, "382700b563f4") + "&token=" + token;
				JObject jToken = await httpClientFactory.Post(text, text2, new Dictionary<string, string> { { "Content-Type", "application/x-www-form-urlencoded" } });
				JArray jArrayCdkey = new JArray();
				if (Util.GetJObject(jToken, "result") == "1")
				{
					jArrayCdkey = Util.GetJObject<JArray>(jToken, "rewardRecords") ?? new JArray();
				}
				string text3 = "https://mate.ksapisrv.com/rest/n/live/mate/authortask/v2/my-tasks";
				text3 = text3 + "?kpf=IPHONE&net=WIFI&appver=5.11.21.262&kpn=KUAISHOU_LIVE_MATE&c=a&ud=" + identifying + "&mod=iPhone12%2C3&sys=ios15.2.1&ver=5.11&language=zh-Hans-CN%3Bq%3D1";
				text3 = text3 + "&did=" + odid + "&token=" + token + "&client_key=" + client_key + "&country_code=cn";
				JObject jToken2 = await httpClientFactory.Get(text3, null, 60.0);
				if (Util.GetJObject(jToken2, "result") == "1")
				{
					JArray jArray2 = Util.GetJObject<JArray>(jToken2, "tasks") ?? new JArray();
					for (int i = 0; i < jArray2.Count; i++)
					{
						string jObject2 = Util.GetJObject(jArray2[i], "taskId");
						string jObject3 = Util.GetJObject(jArray2[i], "title");
						string jObject4 = Util.GetJObject(jArray2[i], "activePeriod");
						JArray jArray3 = Util.GetJObject<JArray>(jArray2[i], "subTask") ?? new JArray();
						for (int j = 0; j < jArray3.Count; j++)
						{
							string jObject5 = Util.GetJObject(jArray3[j], "taskId");
							string jObject6 = Util.GetJObject(jArray3[j], "title");
							string jObject7 = Util.GetJObject(jArray3[j], "rewardName");
							string subRecordId = Util.GetJObject(jArray3[j], "recordId");
							string jObject8 = Util.GetJObject(jArray3[j], "remainNum");
							string jObject9 = Util.GetJObject(jArray3[j], "rewardLimitBold");
							string text4 = "";
							string text5 = "";
							JArray jArray4 = Util.GetJObject<JArray>(jArray3[j], "subTaskTitles") ?? new JArray();
							if (jArray4.Count > 0)
							{
								text4 = Util.GetJObject(jArray4[0], "completeRatio") + "，" + Util.GetJObject(jArray4[0], "title");
							}
							if (jArray4.Count > 1)
							{
								text5 = Util.GetJObject(jArray4[1], "completeRatio") + "，" + Util.GetJObject(jArray4[1], "title");
							}
							string text6 = "";
							long num = long.Parse((j > 0) ? Util.GetJObject(jArray3[j - 1], "recordId") : "0") + 1;
							IEnumerable<JToken> enumerable = jArrayCdkey.Where((JToken jToken3) => jToken3["id"]?.ToString() == subRecordId);
							foreach (JToken item2 in enumerable)
							{
								JArray jArray5 = Util.GetJObject<JArray>(item2, "cdkey") ?? new JArray();
								text6 = (jArray5.Any() ? Util.GetJObject(jArray5[0], "password") : "");
							}
							JObject item = new JObject
							{
								["Fid"] = 0,
								["FUserId"] = userId,
								["FCookieId"] = id,
								["FTaskId"] = jObject2,
								["FTitle"] = jObject3,
								["FActivePeriod"] = jObject4,
								["FSubTaskId"] = jObject5,
								["FSubTitle"] = jObject6,
								["FSubRewardName"] = jObject7,
								["FSubRecordId"] = subRecordId,
								["FSubRemainNum"] = jObject8,
								["FSubRewardLimitBold"] = jObject9,
								["FSubCompleteRatio1"] = text4,
								["FSubCompleteRatio2"] = text5,
								["FStartRecordId"] = num,
								["FSubCdkey"] = text6
							};
							bagJArray.Add(item);
						}
					}
				}
				else
				{
					DataCURD.Save(new JObject
					{
						["Fid"] = id,
						["FStatus"] = Util.GetJObject(jToken2, "error_msg")
					}, "TCookies", "编辑账号", "Fid", userId, userName, curTime, SQLHelper.KSLocalDB.InitCnn());
				}
			}));
		}
		foreach (Task item3 in list)
		{
			item3.Wait();
		}
		JArray jArray = JArray.FromObject(bagJArray);
		SQLHelper.KSLocalDB.SqlBulkCopyByDataTable("TCookiesTask", jArray.ToObject<DataTable>() ?? new DataTable());
	}
}
