using System;
using API.BusService.System;
using API.Common;
using API.DataAccess.DouYu;
using API.Models.Comm;
using Microsoft.AspNetCore.Mvc;

namespace API.Controllers.DouYu;

public class DouYuLogController : Controller
{
	[HttpPost]
	public Response WriteLog([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			DouYuLog.WriteLog(model, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}
}
