using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.IO.Compression;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json.Linq;

namespace API.Common;

public class HttpClientFactory : HttpClient
{
	private readonly string _name = "";

	private readonly ServiceCollection _serviceCollection = new ServiceCollection();

	private readonly ServiceProvider _serviceProvider;

	private readonly IHttpClientFactory _httpClientFactory;

	private readonly Dictionary<string, string> _defaultHeaders = new Dictionary<string, string>();

	public HttpClientFactory(string name = "", Dictionary<string, string>? defaultHeaders = null, HttpClientHandler? defaultHandler = null)
	{
		if (defaultHandler == null)
		{
			defaultHandler = new HttpClientHandler();
		}
		_name = name + Guid.NewGuid();
		_serviceCollection.AddHttpClient(_name).SetHandlerLifetime(TimeSpan.FromMinutes(120.0)).ConfigurePrimaryHttpMessageHandler((IServiceProvider _) => defaultHandler);
		_serviceProvider = _serviceCollection.BuildServiceProvider();
		_httpClientFactory = _serviceProvider.GetService<IHttpClientFactory>() ?? throw new Exception("初始化失败");
		if (defaultHeaders == null)
		{
			return;
		}
		foreach (string key in defaultHeaders.Keys)
		{
			string text = defaultHeaders[key];
			if (text != null && !(text == ""))
			{
				_defaultHeaders.Add(key, defaultHeaders[key]);
			}
		}
	}

	public void AddHeaders(Dictionary<string, string>? defaultHeaders, string cookie)
	{
		if (defaultHeaders != null)
		{
			foreach (string key in defaultHeaders.Keys)
			{
				string text = defaultHeaders[key];
				if (text != null && !(text == "") && !_defaultHeaders.TryAdd(key, defaultHeaders[key]))
				{
					_defaultHeaders[key] = defaultHeaders[key];
				}
			}
		}
		if (cookie != "")
		{
			_defaultHeaders["Cookie"] = cookie;
		}
	}

	public async Task<JObject> Post(string url, string @params, Dictionary<string, string>? defaultHeaders = null, double timeoutSeconds = 30.0)
	{
		JObject jObject = new JObject();
		JObject responseHeaders = new JObject();
		try
		{
			if (defaultHeaders != null)
			{
				foreach (string key in defaultHeaders.Keys)
				{
					string text = defaultHeaders[key];
					if (text != null && !(text == "") && !_defaultHeaders.TryAdd(key, defaultHeaders[key]))
					{
						_defaultHeaders[key] = defaultHeaders[key];
					}
				}
			}
			Activity.Current = null;
			if (_defaultHeaders["Content-Type"] == null || !(_defaultHeaders["Content-Type"].ToString() != ""))
			{
				throw new Exception("请求头缺少Content-Type！");
			}
			string text2 = _defaultHeaders["Content-Type"].ToString();
			HttpClient httpClient = _httpClientFactory.CreateClient(_name);
			httpClient.Timeout = TimeSpan.FromSeconds(timeoutSeconds);
			HttpRequestMessage httpRequestMessage;
			if (text2.Split(";")[0] == "multipart/form-data")
			{
				MultipartFormDataContent multipartFormDataContent = new MultipartFormDataContent();
				StringBuilder stringBuilder = new StringBuilder("");
				string text3 = "--" + text2.Split(";")[1].Trim().Replace("boundary=", "");
				string[] array = @params.Split('&');
				for (int i = 0; i < array.Length; i++)
				{
					string[] array2 = array[i].Split("=");
					stringBuilder.Append(text3 + "\r\n").Append("Content-Disposition: form-data; name=\"" + array2[0] + "\"\r\n\r\n" + array2[1] + "\r\n");
				}
				stringBuilder.Append(text3 + "--\r\n");
				multipartFormDataContent.Add(new StringContent(stringBuilder.ToString()));
				multipartFormDataContent.Headers.ContentType = MediaTypeHeaderValue.Parse(text2);
				httpRequestMessage = new HttpRequestMessage(HttpMethod.Post, url)
				{
					Content = multipartFormDataContent,
					Version = HttpVersion.Version20
				};
			}
			else
			{
				httpRequestMessage = new HttpRequestMessage(HttpMethod.Post, url)
				{
					Content = new StringContent(@params, Encoding.UTF8, text2.Split(";")[0]),
					Version = HttpVersion.Version20
				};
			}
			foreach (string key2 in _defaultHeaders.Keys)
			{
				string value = _defaultHeaders[key2];
				if (!(key2 == "Authorization"))
				{
					if (!(key2 == "Content-Type"))
					{
						httpRequestMessage.Headers.Add(key2, value);
					}
				}
				else
				{
					value = Uri.EscapeDataString(_defaultHeaders[key2]);
					httpRequestMessage.Headers.Add(key2, value);
				}
			}
			HttpResponseMessage httpResponseMessage = await httpClient.SendAsync(httpRequestMessage);
			if (httpResponseMessage.IsSuccessStatusCode)
			{
				HttpResponseHeaders headers = httpResponseMessage.Headers;
				foreach (KeyValuePair<string, IEnumerable<string>> item in headers)
				{
					responseHeaders[item.Key] = string.Join("; ", item.Value);
				}
			}
			Stream stream = await httpResponseMessage.Content.ReadAsStreamAsync();
			ICollection<string> contentEncoding = httpResponseMessage.Content.Headers.ContentEncoding;
			if (contentEncoding.Contains("gzip"))
			{
				stream = new GZipStream(stream, CompressionMode.Decompress);
			}
			else if (contentEncoding.Contains("deflate"))
			{
				stream = new DeflateStream(stream, CompressionMode.Decompress);
			}
			else if (contentEncoding.Contains("br"))
			{
				stream = new BrotliStream(stream, CompressionMode.Decompress);
			}
			StreamReader streamReader = new StreamReader(stream);
			string text4 = streamReader.ReadToEnd();
			try
			{
				jObject = JObject.Parse(text4);
			}
			catch
			{
				jObject["code"] = -100;
				jObject["message"] = text4;
				jObject["msg"] = text4;
				jObject["data"] = null;
			}
			jObject["responseHeaders"] = responseHeaders;
		}
		catch (Exception ex)
		{
			jObject["code"] = -100;
			jObject["message"] = ex.Message;
			jObject["msg"] = ex.Message;
			jObject["data"] = null;
		}
		return jObject;
	}

	public async Task<JObject> Put(string url, string @params, Dictionary<string, string>? defaultHeaders = null, double timeoutSeconds = 30.0)
	{
		JObject jObject = new JObject();
		JObject responseHeaders = new JObject();
		try
		{
			if (defaultHeaders != null)
			{
				foreach (string key in defaultHeaders.Keys)
				{
					string text = defaultHeaders[key];
					if (text != null && !(text == "") && !_defaultHeaders.TryAdd(key, defaultHeaders[key]))
					{
						_defaultHeaders[key] = defaultHeaders[key];
					}
				}
			}
			Activity.Current = null;
			if (_defaultHeaders["Content-Type"] == null || !(_defaultHeaders["Content-Type"].ToString() != ""))
			{
				throw new Exception("请求头缺少Content-Type！");
			}
			string text2 = _defaultHeaders["Content-Type"].ToString();
			HttpClient httpClient = _httpClientFactory.CreateClient(_name);
			httpClient.Timeout = TimeSpan.FromSeconds(timeoutSeconds);
			HttpRequestMessage httpRequestMessage;
			if (text2.Split(";")[0] == "multipart/form-data")
			{
				MultipartFormDataContent multipartFormDataContent = new MultipartFormDataContent();
				StringBuilder stringBuilder = new StringBuilder("");
				string text3 = "--" + text2.Split(";")[1].Trim().Replace("boundary=", "");
				string[] array = @params.Split('&');
				for (int i = 0; i < array.Length; i++)
				{
					string[] array2 = array[i].Split("=");
					stringBuilder.Append(text3 + "\r\n").Append("Content-Disposition: form-data; name=\"" + array2[0] + "\"\r\n\r\n" + array2[1] + "\r\n");
				}
				stringBuilder.Append(text3 + "--\r\n");
				multipartFormDataContent.Add(new StringContent(stringBuilder.ToString()));
				multipartFormDataContent.Headers.ContentType = MediaTypeHeaderValue.Parse(text2);
				httpRequestMessage = new HttpRequestMessage(HttpMethod.Put, url)
				{
					Content = multipartFormDataContent,
					Version = HttpVersion.Version20
				};
			}
			else
			{
				httpRequestMessage = new HttpRequestMessage(HttpMethod.Put, url)
				{
					Content = new StringContent(@params, Encoding.UTF8, text2.Split(";")[0]),
					Version = HttpVersion.Version20
				};
			}
			foreach (string key2 in _defaultHeaders.Keys)
			{
				string value = _defaultHeaders[key2];
				if (!(key2 == "Authorization"))
				{
					if (!(key2 == "Content-Type"))
					{
						httpRequestMessage.Headers.Add(key2, value);
					}
				}
				else
				{
					value = Uri.EscapeDataString(_defaultHeaders[key2]);
					httpRequestMessage.Headers.Add(key2, value);
				}
			}
			HttpResponseMessage httpResponseMessage = await httpClient.SendAsync(httpRequestMessage);
			if (httpResponseMessage.IsSuccessStatusCode)
			{
				HttpResponseHeaders headers = httpResponseMessage.Headers;
				foreach (KeyValuePair<string, IEnumerable<string>> item in headers)
				{
					responseHeaders[item.Key] = string.Join("; ", item.Value);
				}
			}
			Stream stream = await httpResponseMessage.Content.ReadAsStreamAsync();
			ICollection<string> contentEncoding = httpResponseMessage.Content.Headers.ContentEncoding;
			if (contentEncoding.Contains("gzip"))
			{
				stream = new GZipStream(stream, CompressionMode.Decompress);
			}
			else if (contentEncoding.Contains("deflate"))
			{
				stream = new DeflateStream(stream, CompressionMode.Decompress);
			}
			else if (contentEncoding.Contains("br"))
			{
				stream = new BrotliStream(stream, CompressionMode.Decompress);
			}
			StreamReader streamReader = new StreamReader(stream);
			string text4 = streamReader.ReadToEnd();
			if (text4 == "MULTIPART_PUT_SUCCESS")
			{
				jObject["code"] = 0;
				jObject["message"] = "";
				jObject["msg"] = "";
				jObject["data"] = text4;
			}
			else
			{
				jObject = JObject.Parse(text4);
			}
			jObject["responseHeaders"] = responseHeaders;
		}
		catch (Exception ex)
		{
			jObject["code"] = -100;
			jObject["message"] = ex.Message;
			jObject["msg"] = ex.Message;
			jObject["data"] = null;
		}
		return jObject;
	}

	public async Task<JObject> Get(string url, Dictionary<string, string>? defaultHeaders = null, double timeoutSeconds = 30.0)
	{
		JObject jObject = new JObject();
		JObject responseHeaders = new JObject();
		try
		{
			if (defaultHeaders != null)
			{
				foreach (string key in defaultHeaders.Keys)
				{
					string text = defaultHeaders[key];
					if (text != null && !(text == "") && !_defaultHeaders.TryAdd(key, defaultHeaders[key]))
					{
						_defaultHeaders[key] = defaultHeaders[key];
					}
				}
			}
			HttpRequestMessage httpRequestMessage = new HttpRequestMessage(HttpMethod.Get, url)
			{
				Version = HttpVersion.Version20
			};
			foreach (string key2 in _defaultHeaders.Keys)
			{
				string value = _defaultHeaders[key2];
				if (!(key2 == "Authorization"))
				{
					if (!(key2 == "Content-Type"))
					{
						httpRequestMessage.Headers.Add(key2, value);
					}
				}
				else
				{
					value = Uri.EscapeDataString(_defaultHeaders[key2]);
					httpRequestMessage.Headers.Add(key2, value);
				}
			}
			Activity.Current = null;
			HttpClient httpClient = _httpClientFactory.CreateClient(_name);
			httpClient.Timeout = TimeSpan.FromSeconds(timeoutSeconds);
			HttpResponseMessage httpResponseMessage = await httpClient.SendAsync(httpRequestMessage);
			if (httpResponseMessage.IsSuccessStatusCode)
			{
				HttpResponseHeaders headers = httpResponseMessage.Headers;
				foreach (KeyValuePair<string, IEnumerable<string>> item in headers)
				{
					responseHeaders[item.Key] = string.Join("; ", item.Value);
				}
			}
			ICollection<string> contentEncoding = httpResponseMessage.Content.Headers.ContentEncoding;
			Stream stream = await httpResponseMessage.Content.ReadAsStreamAsync();
			if (contentEncoding.Contains("gzip"))
			{
				stream = new GZipStream(stream, CompressionMode.Decompress);
			}
			else if (contentEncoding.Contains("deflate"))
			{
				stream = new DeflateStream(stream, CompressionMode.Decompress);
			}
			else if (contentEncoding.Contains("br"))
			{
				stream = new BrotliStream(stream, CompressionMode.Decompress);
			}
			StreamReader streamReader = new StreamReader(stream);
			string text2 = streamReader.ReadToEnd();
			try
			{
				jObject = JObject.Parse(text2);
			}
			catch
			{
				jObject["code"] = -100;
				jObject["message"] = text2;
				jObject["msg"] = text2;
				jObject["data"] = null;
			}
			jObject["responseHeaders"] = responseHeaders;
		}
		catch (Exception ex)
		{
			jObject["code"] = -100;
			jObject["message"] = ex.Message;
			jObject["msg"] = ex.Message;
			jObject["data"] = null;
			jObject["responseHeaders"] = new JObject();
		}
		return jObject;
	}

	public async Task<string> GetStr(string url, Dictionary<string, string>? defaultHeaders = null, double timeoutSeconds = 30.0)
	{
		JObject responseHeaders = new JObject();
		string result;
		try
		{
			if (defaultHeaders != null)
			{
				foreach (string key in defaultHeaders.Keys)
				{
					string text = defaultHeaders[key];
					if (text != null && !(text == "") && !_defaultHeaders.TryAdd(key, defaultHeaders[key]))
					{
						_defaultHeaders[key] = defaultHeaders[key];
					}
				}
			}
			HttpRequestMessage httpRequestMessage = new HttpRequestMessage(HttpMethod.Get, url)
			{
				Version = HttpVersion.Version20
			};
			foreach (string key2 in _defaultHeaders.Keys)
			{
				string value = _defaultHeaders[key2];
				if (!(key2 == "Authorization"))
				{
					if (!(key2 == "Content-Type"))
					{
						httpRequestMessage.Headers.Add(key2, value);
					}
				}
				else
				{
					value = Uri.EscapeDataString(_defaultHeaders[key2]);
					httpRequestMessage.Headers.Add(key2, value);
				}
			}
			Activity.Current = null;
			HttpClient httpClient = _httpClientFactory.CreateClient(_name);
			httpClient.Timeout = TimeSpan.FromSeconds(timeoutSeconds);
			HttpResponseMessage httpResponseMessage = await httpClient.SendAsync(httpRequestMessage);
			if (httpResponseMessage.IsSuccessStatusCode)
			{
				HttpResponseHeaders headers = httpResponseMessage.Headers;
				foreach (KeyValuePair<string, IEnumerable<string>> item in headers)
				{
					responseHeaders[item.Key] = string.Join("; ", item.Value);
				}
			}
			ICollection<string> contentEncoding = httpResponseMessage.Content.Headers.ContentEncoding;
			Stream stream = await httpResponseMessage.Content.ReadAsStreamAsync();
			if (contentEncoding.Contains("gzip"))
			{
				stream = new GZipStream(stream, CompressionMode.Decompress);
			}
			else if (contentEncoding.Contains("deflate"))
			{
				stream = new DeflateStream(stream, CompressionMode.Decompress);
			}
			else if (contentEncoding.Contains("br"))
			{
				stream = new BrotliStream(stream, CompressionMode.Decompress);
			}
			StreamReader streamReader = new StreamReader(stream);
			result = streamReader.ReadToEnd();
		}
		catch (Exception ex)
		{
			result = ex.Message;
		}
		return result;
	}

	public async Task<JObject> Upload(string url, string method, string fileName, int start = 0, int length = -1, Dictionary<string, string>? defaultHeaders = null, HttpClientHandler? defaultHandler = null)
	{
		JObject jObject = new JObject();
		try
		{
			await Task.Run(delegate
			{
				HttpWebRequest httpWebRequest = (HttpWebRequest)WebRequest.Create(url);
				if (defaultHandler != null && defaultHandler?.Proxy != null)
				{
					httpWebRequest.Proxy = defaultHandler.Proxy;
				}
				foreach (string key in _defaultHeaders.Keys)
				{
					string value = _defaultHeaders[key];
					if (!(key == "Authorization"))
					{
						if (!(key == "Content-Type"))
						{
							httpWebRequest.Headers.Add(key, value);
						}
					}
					else
					{
						value = Uri.EscapeDataString(_defaultHeaders[key]);
						httpWebRequest.Headers.Add(key, value);
					}
				}
				httpWebRequest.Method = method;
				httpWebRequest.Timeout = -1;
				FileInfo fileInfo = new FileInfo(fileName);
				if (length == -1)
				{
					length = (int)fileInfo.Length;
				}
				if (start + length > fileInfo.Length)
				{
					length = (int)(fileInfo.Length - start);
				}
				byte[] array = new byte[length];
				using (FileStream fileStream = File.OpenRead(fileName))
				{
					fileStream.Position = start;
					fileStream.Read(array, 0, length);
				}
				using (Stream stream = httpWebRequest.GetRequestStream())
				{
					stream.Write(array, 0, array.Length);
				}
				HttpWebResponse httpWebResponse = (HttpWebResponse)httpWebRequest.GetResponse();
				StreamReader streamReader = new StreamReader(httpWebResponse.GetResponseStream());
				string text = streamReader.ReadToEnd();
				if (text == "MULTIPART_PUT_SUCCESS")
				{
					jObject["code"] = 0;
					jObject["message"] = "";
					jObject["msg"] = "";
					jObject["data"] = text;
				}
				else
				{
					jObject = JObject.Parse(text);
				}
			});
		}
		catch (Exception ex)
		{
			jObject["code"] = -100;
			jObject["message"] = ex.Message;
			jObject["msg"] = ex.Message;
			jObject["data"] = null;
			jObject["responseHeaders"] = new JObject();
		}
		return jObject;
	}

	public async Task<string> GetIp()
	{
		Stopwatch stopwatch = Stopwatch.StartNew();
		stopwatch.Start();
		string str = "";
		string text2;
		try
		{
			HttpClient httpClient = _httpClientFactory.CreateClient(_name);
			httpClient.Timeout = TimeSpan.FromSeconds(5.0);
			str = await httpClient.GetStringAsync("https://api.live.bilibili.com/xlive/web-room/v1/index/getIpInfo");
			JObject jObject = JObject.Parse(str);
			if (!(Util.GetJObject(jObject, "code") == "0"))
			{
				return "错误信息：" + Util.GetJObject(jObject, "message");
			}
			string jObject2 = Util.GetJObject(jObject["data"], "isp");
			string jObject3 = Util.GetJObject(jObject["data"], "addr");
			string text = Util.GetJObject(jObject["data"], "province") + Util.GetJObject(jObject["data"], "city");
			text2 = jObject2 + "：" + jObject3 + "，地址：" + text;
		}
		catch (Exception ex)
		{
			return "错误信息：" + ((str == "") ? ex.Message.ToString() : str);
		}
		stopwatch.Stop();
		return "延迟" + (int)stopwatch.Elapsed.TotalMilliseconds + "毫秒，" + text2;
	}

	public static Dictionary<string, string> FormataHeader(string header, string cookie = "")
	{
		Dictionary<string, string> dictionary = new Dictionary<string, string>();
		try
		{
			if (header != "")
			{
				dictionary = JObject.Parse(header).ToObject<Dictionary<string, string>>() ?? new Dictionary<string, string>();
			}
			if (cookie != "")
			{
				dictionary.Add("Cookie", cookie);
			}
		}
		catch
		{
			throw new Exception("请求头格式不正确！");
		}
		return dictionary;
	}
}
