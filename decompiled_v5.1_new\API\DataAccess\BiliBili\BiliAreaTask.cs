using System;
using System.Data;
using System.Threading.Tasks;
using API.Common;
using Newtonsoft.Json.Linq;

namespace API.DataAccess.BiliBili;

public class BiliAreaTask
{
	public static DataTable GetAreaTaskList(string id, string areaId, string search = "", string enable = "", string type = "", int limit = 0, int offset = 0, string prop = "", string order = "")
	{
		string text = " SELECT CASE T1.FCompulsory WHEN 1 THEN '√' ELSE '' END AS FCompulsoryName,CASE T1.FEnable WHEN 1 THEN '已启用' ELSE '已禁用' END FEnableName";
		text += " ,CASE T1.FDaily WHEN 1 THEN '√' ELSE '' END AS FDailyName,CASE T1.FComplete WHEN 1 THEN '√' ELSE '' END AS FCompleteName,T1.FType AS FTaskType";
		text += " ,T2.FTotalKey,T2.FName AS FAreaName,T2.FType AS FAreaType,T2.FReceiveUrl,T2.FBlackboardUrl,T2.FSubmissionTag";
		text = text + " ,T1.* " + SQLHelper.total + " FROM TAreaTask T1 LEFT JOIN TArea T2 ON T2.Fid=T1.FAreaId";
		text += " WHERE 1=1 ";
		if (id != "")
		{
			text = text + " AND T1.Fid=" + id;
		}
		if (type != "")
		{
			text = text + " AND T1.FType='" + type + "'";
		}
		if (areaId != "")
		{
			text = text + " AND T1.FAreaId IN(" + areaId + ")";
		}
		if (search != "")
		{
			text = text + " AND (T1.FTaskName LIKE '%" + search + "%' OR T1.FAwardName LIKE '%" + search + "%' OR T1.FTaskKey LIKE '%" + search + "%' )";
		}
		if (enable != "")
		{
			text = text + " AND T1.FEnable=" + enable;
		}
		if (prop == "")
		{
			prop = "T1.FEnable DESC,T1.FCompulsory DESC,T1.FDaily ASC,T1.FSort";
			order = "ASC";
		}
		return SQLHelper.BiliLocalDB.RunSqlDt(text, limit, offset, prop, order);
	}

	public static async Task SaveAreaTask(JObject jObject, int userId, string userName, string curTime)
	{
		string message = "";
		string taskUrl = Util.GetJObject(jObject, "FTaskUrl");
		string taskKey = Util.GetJObject(jObject, "FTaskKey");
		string areaType = Util.GetJObject(jObject, "FAreaType");
		string jObject2 = Util.GetJObject(jObject, "FEnable");
		JArray jArray = new JArray();
		if (jObject2 == "0")
		{
			jObject["FCompulsory"] = "0";
		}
		JObject jObjectTemp = new JObject();
		HttpClientFactory httpClientFactory = new HttpClientFactory("SaveAreaTask");
		int limit = 0;
		string dailyKey = "";
		if (taskKey.Contains(','))
		{
			JObject jObject3 = await httpClientFactory.Get("https://api.bilibili.com/x/task/totalv2?csrf=&task_ids=" + taskKey);
			if (!(Util.GetJObject(jObject3, "code") == "0"))
			{
				throw new Exception(Util.GetJObject(jObject3, "message"));
			}
			JArray jArray2 = Util.GetJObject<JArray>(jObject3["data"], "list") ?? new JArray();
			foreach (JToken item in jArray2)
			{
				string jObject4 = Util.GetJObject(item, "task_type");
				string jObject5 = Util.GetJObject(item, "statistic_type");
				string jObject6 = Util.GetJObject(item, "period_type");
				string jObject7 = Util.GetJObject(item, "award_type");
				JArray jArray3 = Util.GetJObject<JArray>(item, "accumulative_check_points") ?? new JArray();
				JArray jArray4 = Util.GetJObject<JArray>(item, "check_points") ?? new JArray();
				if (jArray3.Count > 0)
				{
					foreach (JToken item2 in jArray3)
					{
						string jObject8 = Util.GetJObject(item2, "sid");
						if (jObject8 != "")
						{
							jArray.Add(jObject8);
							jObjectTemp[jObject8] = jObject4 + jObject5 + jObject6 + jObject7;
						}
						JArray jObject9 = Util.GetJObject<JArray>(item2, "list");
						if (jObject9 != null && jObject9.Count > 0)
						{
							if (limit == 0)
							{
								limit = Util.GetJObject<int>(jObject9[0], "limit");
							}
							else if (Util.GetJObject<int>(jObject9[0], "limit") > limit)
							{
								limit = Util.GetJObject<int>(jObject9[0], "limit");
								dailyKey = "," + jObject8;
							}
							else if (Util.GetJObject<int>(jObject9[0], "limit") == limit)
							{
								dailyKey = dailyKey + "," + jObject8;
							}
						}
					}
				}
				else if (jArray4.Count > 0)
				{
					foreach (JToken item3 in jArray4)
					{
						string jObject10 = Util.GetJObject(item3, "sid");
						if (jObject10 != "")
						{
							jArray.Add(jObject10);
							jObjectTemp[jObject10] = jObject4 + jObject5 + jObject6 + jObject7;
						}
					}
				}
				else
				{
					string jObject11 = Util.GetJObject(item, "task_id");
					jArray.Add(jObject11);
					jObjectTemp[jObject11] = jObject4 + jObject5 + jObject6 + jObject7;
				}
			}
		}
		else
		{
			jArray.Add(taskKey);
		}
		int j = 0;
		while (j < jArray.Count)
		{
			taskKey = jArray[j].ToString();
			jObject["FTaskKey"] = taskKey;
			JObject jObjectTask = await httpClientFactory.Get(taskUrl + taskKey);
			if (jArray.Count != 1)
			{
				jObject["FComplete"] = 0;
				jObject["FSort"] = Util.GetJObject<int>(jObject, "FSort") - j;
				string jObject12 = Util.GetJObject(jObjectTemp, taskKey);
				if (jObject12 != "")
				{
					switch (jObject12)
					{
					case "1141":
						jObject["FType"] = "投稿";
						break;
					case "1211":
						jObject["FType"] = "里程";
						if (dailyKey.Contains(taskKey))
						{
							jObject["FComplete"] = 1;
						}
						break;
					case "1111":
						jObject["FType"] = "每日";
						break;
					default:
						jObject["FType"] = "未知";
						break;
					}
				}
			}
			await Task.Delay(1100);
			int result;
			if (Util.GetJObject(jObjectTask, "code") == "0")
			{
				JToken jToken = jObjectTask["data"];
				if (areaType == "方式一")
				{
					jObject["FTaskId"] = Util.GetJObject(jToken, "task_id");
					jObject["FTaskName"] = Util.GetJObject(jToken, "task_name") + ((Util.GetJObject(jToken, "task_desc") == "") ? "" : (" - " + Util.GetJObject(jToken, "task_desc")));
					JToken jObject13 = Util.GetJObject<JToken>(jToken, "reward_info");
					jObject["FAwardId"] = Util.GetJObject(jObject13, "award_inner_id");
					jObject["FAwardName"] = Util.GetJObject(jObject13, "award_name");
					jObject["FActivityId"] = Util.GetJObject(jToken, "act_id");
					jObject["FActName"] = Util.GetJObject(jToken, "act_name");
					jObject["FActId"] = 0;
					string jObject14 = Util.GetJObject(jObject13, "award_description");
					JToken jObject15 = Util.GetJObject<JToken>(jToken, "stock_info");
					string jObject16 = Util.GetJObject(jObject15, "total_stock");
					string jObject17 = Util.GetJObject(jObject15, "day_stock");
					string text = Util.Regex(jObject14, "本奖品共", "份");
					if (text == "")
					{
						text = Util.Regex(jObject14, "本奖品", "份");
					}
					string text2 = Util.Regex(jObject14, "每日限量发放", "份");
					if (text2 == "")
					{
						text2 = Util.Regex(jObject14, "每日限量", "份");
					}
					text = ((text != "") ? text : jObject16);
					text2 = ((text2 != "") ? text2 : jObject17);
					if (!int.TryParse(text, out result))
					{
						text = jObject16;
					}
					if (!int.TryParse(text2, out result))
					{
						text2 = jObject17;
					}
					int num = int.Parse(text) - int.Parse(text) * int.Parse(jObject16) / 100;
					int num2 = int.Parse(text2) - int.Parse(text2) * int.Parse(jObject17) / 100;
					jObject["FStockTotal"] = text;
					jObject["FStockConsumed"] = num;
					jObject["FPeriodTotal"] = text2;
					jObject["FPeriodConsumed"] = num2;
				}
				else
				{
					JToken jObject18 = Util.GetJObject<JToken>(jToken, "task_info");
					jObject["FTaskId"] = Util.GetJObject(jObject18, "id");
					jObject["FTaskName"] = Util.GetJObject(jObject18, "task_name");
					JToken jObject19 = Util.GetJObject<JToken>(jObject18, "reward_info");
					jObject["FAwardId"] = Util.GetJObject(jObject19, "reward_id");
					jObject["FAwardName"] = Util.GetJObject(jObject19, "reward_name");
					jObject["FActivityId"] = Util.GetJObject(jObject19, "reward_act_id");
					JToken jObject20 = Util.GetJObject<JToken>(jToken, "act_info");
					jObject["FActName"] = Util.GetJObject(jObject20, "act_name");
					jObject["FActId"] = Util.GetJObject(jObject20, "id");
					JArray jObject21 = Util.GetJObject<JArray>(jObject18, "reward_stock_configs");
					if (jObject21 != null)
					{
						for (int i = 0; i < jObject21.Count; i++)
						{
							string jObject22 = Util.GetJObject(jObject21[i], "cycle_type");
							if (jObject22 == "1")
							{
								jObject["FStockTotal"] = Util.GetJObject(jObject21[i], "total");
								jObject["FStockConsumed"] = Util.GetJObject(jObject21[i], "consumed");
							}
							else if (jObject22 == "2")
							{
								jObject["FPeriodTotal"] = Util.GetJObject(jObject21[i], "total");
								jObject["FPeriodConsumed"] = Util.GetJObject(jObject21[i], "consumed");
							}
						}
					}
				}
				jObject["FDate"] = curTime;
				if (jArray.Count != 1)
				{
					string sSql = " SELECT ISNULL((SELECT Fid FROM TAreaTask WHERE FTaskKey='" + taskKey + "'),0)";
					string text3 = SQLHelper.BiliLocalDB.RunSqlStr(sSql);
					jObject["Fid"] = text3;
				}
				DataCURD.Save(jObject, "TAreaTask", "修改分区任务对照", "Fid", userId, userName, curTime, SQLHelper.BiliLocalDB.InitCnn());
				DataCURD.Save(new JObject
				{
					["FUpdateTime"] = curTime,
					["Fid"] = jObject["FAreaId"]
				}, "TArea", "更新分区时间", "Fid", userId, userName, curTime, SQLHelper.BiliLocalDB.InitCnn());
			}
			else
			{
				if (jArray.Count == 1)
				{
					throw new Exception(Util.GetJObject(jObjectTask, "message"));
				}
				message = message + taskKey + "：" + Util.GetJObject(jObjectTask, "message") + "；";
			}
			result = j++;
		}
	}

	public static void DelAreaTask(JObject jObject, int userId, string userName, string curTime)
	{
		string jObject2 = Util.GetJObject(jObject, "id");
		if (jObject2 != "")
		{
			DataCURD.Delete("TAreaTask", "删除分区任务对照", "Fid", jObject2, userId, userName, curTime, SQLHelper.BiliLocalDB.InitCnn());
		}
		string jObject3 = Util.GetJObject(jObject, "areaId");
		if (jObject3 != "")
		{
			DataCURD.Delete("TAreaTask", "删除分区任务对照", "FAreaId", jObject3, userId, userName, curTime, SQLHelper.BiliLocalDB.InitCnn());
		}
	}

	public static int EnableSwitch(JObject jObject, int userId, string userName, string curTime)
	{
		string jObject2 = Util.GetJObject(jObject, "Fid");
		string jObject3 = Util.GetJObject(jObject, "FEnable");
		string text = Util.GetJObject(jObject, "FCompulsory");
		if (jObject3 != "1")
		{
			text = "0";
		}
		DataCURD.Save(new JObject
		{
			["Fid"] = jObject2,
			["FEnable"] = jObject3,
			["FCompulsory"] = text
		}, "TAreaTask", "启动禁用分区任务", "Fid", userId, userName, curTime, SQLHelper.BiliLocalDB.InitCnn());
		return int.Parse(jObject3);
	}
}
