using System;
using System.Data;
using System.IO;
using API.Common;
using Microsoft.Data.SqlClient;
using Newtonsoft.Json.Linq;

namespace API.DataAccess.BiliBili;

public class BiliLive
{
	public static void SaveLiveRtmp(JObject jObject, int userId, string userName, string curTime)
	{
		string jObject2 = Util.GetJObject(jObject, "FCookie");
		DataCURD.Save(jObject, "TCookiesLive", "保存直播RTMP", "FCookieId", userId, userName, curTime, SQLHelper.BiliLocalDB.InitCnn());
		jObject = new JObject
		{
			["Fid"] = Util.GetJObject(jObject, "FCookieId"),
			["FCookie"] = jObject2,
			["FCsrf"] = Util.GetCookieByKey(jObject2, "bili_jct"),
			["FBrowserStatus"] = 1
		};
		long num = long.Parse(Util.GetCookieByKey(jObject2, "bili_ticket_expires", "0"));
		if (num != 0L)
		{
			jObject["FCookieExpires"] = DateTimeOffset.FromUnixTimeSeconds(num).LocalDateTime.ToString("yyyy-MM-dd HH:mm:ss");
		}
		DataCURD.Save(jObject, "TCookies", "只保存Cookie", "Fid", userId, userName, curTime, SQLHelper.BiliLocalDB.InitCnn());
	}

	public static void SaveLive(JObject jObject, int userId, string userName, string curTime)
	{
		SqlConnection sqlConnection = SQLHelper.BiliLocalDB.InitCnn();
		SqlCommand sqlCommand = sqlConnection.CreateCommand();
		sqlCommand.Transaction = sqlConnection.BeginTransaction();
		try
		{
			string jObject2 = Util.GetJObject(jObject, "cookieId");
			string jObject3 = Util.GetJObject(jObject, "areaId");
			JArray jArray = Util.GetJObject<JArray>(jObject, "livePath") ?? new JArray();
			string text = "";
			text = ((jArray.Count <= 0) ? Util.GetJObject(jObject, "livePath").Split(",")[0] : Util.GetJObject(jArray[0], "url"));
			string path = Directory.GetCurrentDirectory() + "\\WWWRoot\\" + text;
			if (!File.Exists(path))
			{
				text = "";
			}
			JArray jArray2 = Util.GetJObject<JArray>(jObject, "liveConfig") ?? new JArray();
			DataCURD.Delete("TCookiesLive", "删除直播地址", "FUserId,FCookieId,FAreaId", userId + "," + jObject2 + "," + jObject3, userId, userName, curTime, sqlCommand);
			DataCURD.Delete("TCookiesLiveConfig", "删除弹幕礼物配置", "FUserId,FCookieId,FAreaId", userId + "," + jObject2 + "," + jObject3, userId, userName, curTime, sqlCommand);
			JObject jObject4 = new JObject
			{
				["Fid"] = 0,
				["FUserId"] = userId,
				["FCookieId"] = jObject2,
				["FAreaId"] = jObject3,
				["FLivePath"] = text
			};
			DataCURD.Save(jObject4, "TCookiesLive", "新增直播地址", "Fid", userId, userName, curTime, sqlCommand);
			foreach (JToken item in jArray2)
			{
				string jObject5 = Util.GetJObject(item, "FRoomNo");
				if (jObject5 != "")
				{
					JObject jObject6 = new JObject
					{
						["Fid"] = 0,
						["FUserId"] = userId,
						["FCookieId"] = jObject2,
						["FAreaId"] = jObject3,
						["FRoomNo"] = jObject5,
						["FGiftName"] = Util.GetJObject(item, "FGiftName"),
						["FGiftCode"] = Util.GetJObject(item, "FGiftCode"),
						["FGiftPrice"] = Util.GetJObject(item, "FGiftPrice", "0"),
						["FGiftNum"] = Util.GetJObject(item, "FGiftNum", "0"),
						["FBulletScreenNum"] = Util.GetJObject(item, "FBulletScreenNum", "0"),
						["FWatchMinutes"] = Util.GetJObject(item, "FWatchMinutes", "0")
					};
					DataCURD.Save(jObject6, "TCookiesLiveConfig", "新增弹幕礼物配置", "Fid", userId, userName, curTime, sqlCommand);
				}
			}
			sqlCommand.Transaction.Commit();
		}
		catch (Exception ex)
		{
			sqlCommand.Transaction.Rollback();
			throw new Exception(ex.Message);
		}
		finally
		{
			sqlConnection.Close();
			sqlConnection.Dispose();
		}
	}

	public static string GetLivePathList(string cookieId, string areaId, int userId)
	{
		string sSql = " SELECT FLivePath FROM TCookiesLive WHERE FUserId=" + userId + " AND FCookieId=" + cookieId + " AND FAreaId=" + areaId;
		return SQLHelper.BiliLocalDB.RunSqlStr(sSql);
	}

	public static DataTable GetLivePathAll(string cookieId, string areaId, int userId)
	{
		string text = " SELECT T1.Fid,T2.FName AS FAreaName,T1.FCookieId,T1.FLivePath,T1.FRtmp,T1.FError,T1.FLiveLog FROM TCookiesLive T1 ";
		text += " LEFT JOIN TArea T2 ON T2.Fid=T1.FAreaId";
		text = text + " WHERE T1.FUserId=" + userId + " AND T1.FCookieId IN (" + cookieId + ") AND T1.FAreaId=" + areaId;
		return SQLHelper.BiliLocalDB.RunSqlDt(text);
	}

	public static void SaveLiveLog(string cookieId, string areaId, int userId, string liveLog = "")
	{
		SqlConnection sqlConnection = SQLHelper.BiliLocalDB.InitCnn();
		SqlCommand sqlCommand = sqlConnection.CreateCommand();
		sqlCommand.Transaction = sqlConnection.BeginTransaction();
		try
		{
			SQLHelper.RunTableLockx("TCookiesLive", sqlCommand);
			string text = " IF((SELECT 1 FROM TCookiesLive WHERE FUserId=" + userId + " AND FCookieId=" + cookieId + " AND FAreaId=" + areaId + ") IS NULL)";
			text = text + " INSERT TCookiesLive (FUserId,FCookieId,FAreaId,FLivePath,FRtmp,FError,FLiveLog) VALUES (" + userId + "," + cookieId + "," + areaId + ",'','','','" + liveLog + "')";
			text = text + " ELSE UPDATE TCookiesLive SET FLiveLog='" + liveLog + "' WHERE FUserId=" + userId + " AND FCookieId=" + cookieId + " AND FAreaId=" + areaId;
			SQLHelper.RunSqlText(text, sqlCommand);
			sqlCommand.Transaction.Commit();
		}
		catch (Exception ex)
		{
			sqlCommand.Transaction.Rollback();
			throw new Exception(ex.Message);
		}
		finally
		{
			sqlConnection.Close();
			sqlConnection.Dispose();
		}
	}

	public static DataTable GetLiveConfigList(string cookieId, string areaId, int userId)
	{
		string text = " SELECT 0 AS Fid,FCookieId,FAreaId,FRoomNo,FGiftName,FGiftCode,FGiftPrice,FGiftNum,FBulletScreenNum,FWatchMinutes";
		text = text + " FROM TCookiesLiveConfig WHERE FUserId=" + userId;
		if (cookieId != "")
		{
			text = text + "  AND FCookieId IN (" + cookieId + ")";
		}
		if (areaId != "")
		{
			text = text + " AND FAreaId=" + areaId;
		}
		text += " ORDER BY FWatchMinutes ASC";
		return SQLHelper.BiliLocalDB.RunSqlDt(text);
	}

	public static DataTable GetLiveGiftList(string cookieId, string areaId, int userId)
	{
		string text = " SELECT FCookieId,FRoomNo,FGiftName,FGiftCode,FGiftNum,FGiftPrice FROM TCookiesLiveConfig ";
		text = text + " WHERE FCookieId IN (" + cookieId + ") AND FAreaId=" + areaId + " AND FUserId=" + userId + " AND FGiftNum>0";
		return SQLHelper.BiliLocalDB.RunSqlDt(text);
	}

	public static DataTable GetLiveMsgList(string type, string cookieId, string areaId)
	{
		string text = " SELECT * FROM TBulletSceen WHERE FListen IN ('" + type + "') AND ( FCookieId='' OR ','+FCookieId+',' LIKE '%" + cookieId + "%')";
		text = text + " AND (FAreaId='' OR ','+FAreaId+',' LIKE '%" + areaId + "%')";
		return SQLHelper.BiliLocalDB.RunSqlDt(text);
	}

	public static JObject GetLiveConfig(string cookieId, string areaId, int userId)
	{
		JObject jObject = new JObject { ["livePath"] = "" };
		string areaName = BiliArea.GetAreaName(areaId);
		string text = Directory.GetCurrentDirectory() + "\\WWWRoot\\files\\Bili\\Live\\" + areaName + "\\";
		if (Directory.Exists(text))
		{
			JArray jArray = new JArray();
			if (text != null)
			{
				string[] files = Directory.GetFiles(text);
				string[] array = files;
				foreach (string text2 in array)
				{
					string text3 = text2.ToLower().Replace((Directory.GetCurrentDirectory() + "\\wwwroot\\").ToLower(), "");
					jArray.Add(new JObject
					{
						["url"] = text3,
						["name"] = Path.GetFileName(text2)
					});
				}
			}
			jObject["livePath"] = jArray;
		}
		jObject["liveConfig"] = JArray.FromObject(GetLiveConfigList(cookieId, areaId, userId));
		return jObject;
	}

	public static DataTable GetWatchMinutesAll(string cookieId, string areaId, int userId)
	{
		string text = " SELECT FCookieId,FRoomNo,FWatchMinutes+1 AS FWatchMinutes FROM TCookiesLiveConfig WHERE FWatchMinutes!=0 AND FUserId=" + userId;
		text = text + " AND FCookieId IN (" + cookieId + ") AND FAreaId =" + areaId;
		return SQLHelper.BiliLocalDB.RunSqlDt(text);
	}
}
