using System;
using System.Data;
using System.Net;
using System.Threading.Tasks;
using API.Common;
using API.DataAccess.System;
using API.Models.Comm;
using Microsoft.AspNetCore.Mvc;

namespace API.Controllers.System;

public class SysNoticeController : Controller
{
	[HttpPost]
	public async Task<Response> GetSysNoticeList([FromBody] Request model, User user)
	{
		Response mRet = new Response();
		try
		{
			model.jObjectParam["userId"] = user.Id;
			object obj = await Util.Request("/Common/GetSysNoticeList", model, base.HttpContext);
			if (obj == null)
			{
				string jObject = Util.GetJObject(model.jObjectSearch, "id", "0");
				DataTable sysNoticeList = SysNotice.GetSysNoticeList(jObject, user);
				mRet.data = Util.GetTableResponse(sysNoticeList);
			}
			else
			{
				mRet.data = obj;
			}
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}

	[HttpPost]
	public Response SaveSysNotice([FromBody] Request model, User user)
	{
		Response response = new Response();
		try
		{
			model.jObjectParam["FContent"] = WebUtility.UrlDecode(Util.GetJObject(model.jObjectParam, "FHtml"));
			model.jObjectParam["FDate"] = DateTime.Now;
			DataCURD.Save(model.jObjectParam, "TSysNotice", "保存公告", "Fid", user.Id, user.Name, model.curTime, SQLHelper.LocalDB.InitCnn());
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response DelSysNotice([FromBody] Request model, User user)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "id", "0");
			if (("," + user.Rights + ",").Contains(",999,"))
			{
				DataCURD.Delete("TSysNotice", "删除公告", "Fid", jObject, user.Id, user.Name, model.curTime, SQLHelper.LocalDB.InitCnn());
			}
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public async Task<Response> StarSysNotice([FromBody] Request model, User user)
	{
		Response mRet = new Response();
		try
		{
			model.jObjectParam["userId"] = user.Id;
			if (await Util.Request("/Common/StarSysNotice", model, base.HttpContext) == null)
			{
				string jObject = Util.GetJObject(model.jObjectParam, "id", "0");
				SysNotice.StarSysNotice(jObject, user);
			}
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}
}
