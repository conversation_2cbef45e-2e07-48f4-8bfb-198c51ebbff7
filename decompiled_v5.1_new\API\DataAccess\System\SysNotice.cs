using System.Data;
using API.Common;
using API.Models.Comm;

namespace API.DataAccess.System;

public class SysNotice
{
	public static DataTable GetSysNoticeList(string id, User user)
	{
		string text = ((!(id == "0")) ? ("SELECT Fid, FTitle, FContent AS FHtml, FPrivate,FSort FROM TSysNotice WHERE CAST(Fid AS NVARCHAR(100))='" + id + "'") : ("SELECT Fid,FTitle,FDate,CAST(LEN(FLikes) - LEN(REPLACE(FLikes, ',', ''))AS NVARCHAR(100)) AS FLike,CHARINDEX('," + user.Id + ",',','+FLikes+',') AS FStar FROM TSysNotice WHERE 0=" + id));
		if (!("," + user.Rights + ",").Contains(",999,"))
		{
			text = text + " AND FPrivate <= (SELECT FMoney FROM TSysUser WHERE Fid=" + user.Id + ")";
		}
		text += " ORDER BY FSort";
		return SQLHelper.LocalDB.RunSqlDt(text);
	}

	public static void StarSysNotice(string id, User user)
	{
		string sSql = " UPDATE TSysNotice SET FLikes=FLikes+'" + user.Id + ",' WHERE Fid=" + id + " AND CHARINDEX('," + user.Id + ",',','+FLikes+',')=0";
		if (SQLHelper.LocalDB.RunSqlText(sSql) == 0)
		{
			sSql = " UPDATE TSysNotice SET FLikes=REPLACE(FLikes,'" + user.Id + ",','') WHERE Fid=" + id;
			SQLHelper.LocalDB.RunSqlText(sSql);
		}
	}
}
