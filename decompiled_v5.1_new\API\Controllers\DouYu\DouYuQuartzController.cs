using System;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using API.BusService.DouYu;
using API.BusService.System;
using API.Common;
using API.DataAccess.DouYu;
using API.Models.Comm;
using API.Quartz;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using Quartz;

namespace API.Controllers.DouYu;

public class DouYuQuartzController(ISchedulerFactory schedulerFactory) : Controller()
{
	private readonly ISchedulerFactory _schedulerFactory = schedulerFactory;

	[HttpPost]
	public async Task<Response> ManualExecQuartz([FromBody] Request model)
	{
		Response mRet = new Response();
		try
		{
			JArray jObject = Util.GetJObject<JArray>(model.jObjectParam, "array");
			string cookieId = string.Join(",", (jObject ?? new JArray()).Select((JToken token) => (token["Fid"] ?? ((JToken)"")).ToString()).ToArray());
			string areaId = Util.GetJObject(model.jObjectParam, "areaId", "0");
			string jobName = Util.GetJObject(model.jObjectParam, "jobName");
			string delay = Util.GetJObject(model.jObjectParam, "delay", "0");
			if (jobName == "")
			{
				throw new Exception("缺少任务信息！");
			}
			IScheduler scheduler = await schedulerFactory.GetScheduler();
			string jobGroup = "手动执行-斗鱼";
			if (await QzUtil.ExecutingJob(scheduler, jobGroup, jobName))
			{
				while (await QzUtil.ExecutingJob(scheduler, jobGroup, jobName))
				{
					await scheduler.Interrupt(new JobKey(jobName, jobGroup));
					await Task.Delay(500);
				}
				mRet.message = "任务已停止！";
			}
			else
			{
				JObject jObject2 = new JObject();
				jObject2["areaId"] = areaId;
				jObject2["cookieId"] = cookieId;
				jObject2["param"] = Util.GetJObject(model.jObjectParam, "param", "0");
				await QzUtil.ScheduleJobPlus<QzDouYu>(scheduler, jobGroup, jobName, "", 0, jObject2, BusSysUser.Instance.User.Id);
				if (delay == "1")
				{
					await Task.Delay(1000);
					while (await QzUtil.ExecutingJob(scheduler, jobGroup, jobName))
					{
						await Task.Delay(500);
					}
					mRet.message = "任务执行完毕！";
				}
				else
				{
					mRet.message = "任务已启动！";
				}
			}
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}

	[HttpPost]
	public async Task<Response> GetQuartzList([FromBody] Request model)
	{
		Response mRet = new Response();
		try
		{
			IScheduler scheduler = await schedulerFactory.GetScheduler();
			string jObject = Util.GetJObject(model.jObjectSearch, "search");
			string jObject2 = Util.GetJObject(model.jObjectSearch, "groupId");
			DataTable quartzList = DouYuQuartz.GetQuartzList("", jObject2, jObject, BusSysUser.Instance.User.Id);
			mRet.data = Util.GetTableResponse(await BusDouYuQuartz.GetQuartzList(scheduler, quartzList));
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}

	[HttpPost]
	public Response GetQuartzJobList([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectSearch, "search");
			DataTable quartzJobList = DouYuQuartz.GetQuartzJobList(jObject);
			response.data = Util.GetTableResponse(quartzJobList);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public async Task<Response> SaveQuartz([FromBody] Request model)
	{
		Response mRet = new Response();
		try
		{
			await DouYuQuartz.SaveQuartz(schedulerFactory, model.jObjectParam, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}

	[HttpPost]
	public async Task<Response> DelQuartz([FromBody] Request model)
	{
		Response mRet = new Response();
		try
		{
			await DouYuQuartz.DelQuartz(schedulerFactory, model.jObjectParam, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}

	[HttpPost]
	public async Task<Response> EnableQuartz([FromBody] Request model)
	{
		Response mRet = new Response();
		try
		{
			Response response = mRet;
			response.data = await DouYuQuartz.EnableQuartz(schedulerFactory, model.jObjectParam, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
		}
		catch (Exception ex)
		{
			string jObject = Util.GetJObject(model.jObjectParam, "FEnable");
			mRet.data = ((!(jObject == "1")) ? 1 : 0);
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}

	[HttpPost]
	public async Task<Response> ShutdownQuartz([FromBody] Request model)
	{
		Response mRet = new Response();
		try
		{
			await BusDouYuQuartz.ShutdownQuartz(schedulerFactory, model.jObjectParam);
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}

	[HttpPost]
	public async Task<Response> StartQuartz([FromBody] Request model)
	{
		Response mRet = new Response();
		try
		{
			await BusDouYuQuartz.StartQuartz(schedulerFactory, model.jObjectParam, BusSysUser.Instance.User.Id);
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}

	[HttpPost]
	public async Task<Response> ExecQuartz([FromBody] Request model)
	{
		Response mRet = new Response();
		try
		{
			await BusDouYuQuartz.ExecQuartz(await schedulerFactory.GetScheduler(), model.jObjectParam);
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}

	[HttpPost]
	public async Task<Response> StopQuartz([FromBody] Request model)
	{
		Response mRet = new Response();
		try
		{
			await BusDouYuQuartz.StopQuartz(await schedulerFactory.GetScheduler(), model.jObjectParam);
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}

	[HttpPost]
	public Response ShareQuartz([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			response.data = DouYuQuartz.ShareQuartz(model.jObjectParam, BusSysUser.Instance.User.Id);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response ExportQuartz([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			DouYuQuartz.ExportQuartz(model.jObjectParam, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}
}
