using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using API.BusService.DouYu;
using API.BusService.System;
using API.Common;
using API.DataAccess.DouYu;
using API.Models.Comm;
using Newtonsoft.Json.Linq;
using Quartz;

namespace API.Quartz;

public class QzDouYu
{
	[DisallowConcurrentExecution]
	public class 更新Cookie信息 : IJob
	{
		public async Task Execute(IJobExecutionContext context)
		{
			string[] jobGroup = (context.JobDetail.JobDataMap.GetString("jobGroup") ?? "").Split("-");
			string jobName = context.JobDetail.JobDataMap.GetString("jobName") ?? "";
			try
			{
				JObject @params = (context.JobDetail.JobDataMap.Get("params") as JObject) ?? new JObject();
				int seconds = context.JobDetail.JobDataMap.GetInt("seconds");
				int userId = context.JobDetail.JobDataMap.GetInt("userId");
				Util.GetJObject(@params, "areaId");
				string cookieId = Util.GetJObject(@params, "cookieId");
				Util.GetJObject(@params, "param");
				string keys = await QzUtil.Validate(userId, BusSysUser.Instance.User.Address, "斗鱼");
				int jObject = Util.GetJObject<int>(@params, "difference");
				if (jObject != 0)
				{
					int num = new Random().Next(0, jObject);
					seconds = ((seconds != 0) ? (seconds + num) : 0);
					DateTime datetime = DateTime.Now.AddSeconds(num);
					Util.WriteLog(jobGroup[1], jobGroup[0], jobName, datetime.ToString("yyyy-MM-dd HH:mm:ss") + "开始执行");
					while (DateTime.Now <= datetime && QzUtil.IsExecute(seconds, context))
					{
						await QzUtil.Delay();
					}
				}
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, "开始");
				DataTable dtCookies = DouYuCookies.GetCookiesList(cookieId, keys, userId);
				for (int i = 0; i < dtCookies.Rows.Count; i++)
				{
					if (!QzUtil.IsExecute(seconds, context))
					{
						break;
					}
					DataRow dataRow = dtCookies.Rows[i];
					string text = dataRow["Fid"].ToString() ?? "";
					string name = dataRow["FName"].ToString() ?? "";
					string text2 = dataRow["FKey"].ToString() ?? "";
					string json = dataRow["FHeaders"].ToString() ?? "";
					JObject jToken = JObject.Parse(json);
					string jObject2 = Util.GetJObject(jToken, "User-Agent");
					string text3 = AppSettings.GetVal("TokenPrefix") + Token.GetTokenValue(userId.ToString());
					string val = AppSettings.GetVal("TokenKey");
					string val2 = AppSettings.GetVal("Url", "Kestrel:Endpoints:Http");
					string text4 = Directory.GetCurrentDirectory() + "\\Util\\Chromium\\DouYu-" + text2;
					string fileName = text4 + "\\Chromium.exe";
					ProcessStartInfo processStartInfo = new ProcessStartInfo();
					processStartInfo.FileName = fileName;
					processStartInfo.Arguments = "-a " + text3 + " -c \"" + BusSysUser.Instance.User.Cookie + "\" -t Login -i " + text + " -k " + text2 + " -s 60 -u " + val2 + " --token " + val + " --ua \"" + jObject2 + "\" -m \"https://www.douyu.com/creator/main/live\"";
					ProcessStartInfo startInfo = processStartInfo;
					Process process = Process.Start(startInfo);
					Util.WriteLog(jobGroup[1], name, jobName, "开始更新Cookie");
					process?.WaitForExit();
					await Task.Delay(2000);
					Util.WriteLog(jobGroup[1], name, jobName, "Cookie更新结束");
				}
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, QzUtil.WriteStatus(context, seconds));
			}
			catch (Exception ex)
			{
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, ex.Message, ConsoleColor.Red);
			}
		}
	}

	[DisallowConcurrentExecution]
	public class 更新Cdkey列表 : IJob
	{
		public async Task Execute(IJobExecutionContext context)
		{
			string[] jobGroup = (context.JobDetail.JobDataMap.GetString("jobGroup") ?? "").Split("-");
			string jobName = context.JobDetail.JobDataMap.GetString("jobName") ?? "";
			ConcurrentBag<JToken> bagUser = new ConcurrentBag<JToken>();
			ConcurrentBag<JToken> bagCdkey = new ConcurrentBag<JToken>();
			List<Task> taskList = new List<Task>();
			try
			{
				JObject @params = (context.JobDetail.JobDataMap.Get("params") as JObject) ?? new JObject();
				int seconds = context.JobDetail.JobDataMap.GetInt("seconds");
				int userId = context.JobDetail.JobDataMap.GetInt("userId");
				string areaId = Util.GetJObject(@params, "areaId");
				string cookieId = Util.GetJObject(@params, "cookieId");
				string keys = await QzUtil.Validate(userId, BusSysUser.Instance.User.Address, "斗鱼");
				int jObject = Util.GetJObject<int>(@params, "difference");
				if (jObject != 0)
				{
					int num = new Random().Next(0, jObject);
					seconds = ((seconds != 0) ? (seconds + num) : 0);
					DateTime datetime = DateTime.Now.AddSeconds(num);
					Util.WriteLog(jobGroup[1], jobGroup[0], jobName, datetime.ToString("yyyy-MM-dd HH:mm:ss") + "开始执行");
					while (DateTime.Now <= datetime && QzUtil.IsExecute(seconds, context))
					{
						await QzUtil.Delay();
					}
				}
				DataTable cookiesList = DouYuCookies.GetCookiesList(cookieId, keys, userId);
				DataTable areaList = DouYuArea.GetAreaList(areaId, BusSysUser.Instance.User.Organization.斗鱼);
				if (areaList.Rows.Count == 0)
				{
					throw new Exception("暂无该分区权限");
				}
				string actUrl = Util.GetJObject(areaList.Rows[0], "FActivityUrl");
				string actAlias = Util.GetJObject(areaList.Rows[0], "FActAlias");
				string referer = Util.GetJObject(areaList.Rows[0], "FReferer");
				string areaName = Util.GetJObject(areaList.Rows[0], "FName");
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, "开始");
				for (int i = 0; i < cookiesList.Rows.Count; i++)
				{
					if (!QzUtil.IsExecute(seconds, context))
					{
						break;
					}
					DataRow drCookie = cookiesList.Rows[i];
					taskList.Add(Task.Run(async delegate
					{
						await Execute2(jobGroup, jobName, userId, areaId, areaName, drCookie, actUrl, actAlias, referer, bagCdkey, bagUser);
					}));
				}
				foreach (Task item in taskList)
				{
					item.Wait();
				}
				JArray jArray = new JArray();
				foreach (JToken item2 in bagCdkey)
				{
					jArray.Merge(item2);
				}
				if (jArray.Count > 0)
				{
					DataTable dataTable = jArray.ToObject<DataTable>();
					if (dataTable != null)
					{
						SQLHelper.DouYuLocalDB.SqlBulkCopyByDataTable("TCdkey", dataTable);
						DouYuCdkey.UpdateCdkeyList(cookieId, areaId, userId);
					}
				}
				BusDouYuCookies.UpdateCookieStatus(bagUser, userId);
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, QzUtil.WriteStatus(context, seconds));
			}
			catch (Exception ex)
			{
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, ex.Message, ConsoleColor.Red);
			}
		}

		public static async Task Execute2(string[] jobGroup, string jobName, int userId, string areaId, string areaName, DataRow drCookie, string actUrl, string actAlias, string referer, ConcurrentBag<JToken> bagCdkey, ConcurrentBag<JToken> bagUser)
		{
			JArray jArrayCdkey = new JArray();
			string cookieId = drCookie["Fid"].ToString() ?? "";
			string name = drCookie["FKey"].ToString() ?? "";
			string cookie = drCookie["FCookie"].ToString() ?? "";
			string cookieName = drCookie["FName"].ToString() ?? "";
			string header = drCookie["FHeaders"].ToString() ?? "";
			string text = drCookie["FProxyAddress"].ToString() ?? "";
			string userName = drCookie["FProxyUserName"].ToString() ?? "";
			string password = drCookie["FProxyPassword"].ToString() ?? "";
			try
			{
				HttpClientHandler defaultHandler = null;
				if (text != "")
				{
					defaultHandler = new HttpClientHandler
					{
						Proxy = new WebProxy
						{
							Address = new Uri(text),
							Credentials = new NetworkCredential(userName, password)
						}
					};
				}
				Dictionary<string, string> dictionary = HttpClientFactory.FormataHeader(header, cookie);
				dictionary["Referer"] = referer;
				dictionary["Host"] = "www.douyu.com";
				HttpClientFactory httpClientFactory = new HttpClientFactory(name, dictionary, defaultHandler);
				string typeName = jobGroup[1];
				string name2 = cookieName;
				Util.WriteLog(typeName, name2, jobName, await httpClientFactory.GetIp());
				Util.WriteLog(jobGroup[1], cookieName, jobName, "【" + areaName + "】开始更新");
				for (int i = 0; i < 10; i++)
				{
					JObject res = await httpClientFactory.Get(actUrl + "?pageSize=10&currentPage=" + (i + 1) + "&actAlias=" + actAlias);
					await Task.Delay(1000);
					if (Util.GetJObject(res, "error") == "0")
					{
						JObject jObject = Util.GetJObject<JObject>(res, "data");
						JArray jObject2 = Util.GetJObject<JArray>(jObject, "bags");
						if (jObject2 == null)
						{
							continue;
						}
						foreach (JToken item2 in jObject2)
						{
							JArray jObject3 = Util.GetJObject<JArray>(item2, "prizes");
							if (jObject3 != null && jObject3.Count > 0 && Util.GetJObject(jObject3[0], "ext") != "")
							{
								string jObject4 = Util.GetJObject(jObject3[0], "ext");
								string jObject5 = Util.GetJObject(item2, "bname");
								string jObject6 = Util.GetJObject(jObject3[0], "obtTime");
								string jObject7 = Util.GetJObject(jObject3[0], "pid");
								string jObject8 = Util.GetJObject(item2, "balias");
								jObject6 = DateTimeOffset.FromUnixTimeSeconds(long.Parse(jObject6)).ToString("yyyy-MM-dd HH:mm:ss");
								JObject item = new JObject
								{
									["FStatus"] = -1,
									["FUserId"] = userId,
									["FAreaId"] = areaId,
									["FCookieId"] = cookieId,
									["FName"] = jObject5,
									["FCdkey"] = jObject4,
									["FDate"] = jObject6,
									["FPrizeId"] = jObject7,
									["FPrizeGiftAlias"] = jObject8,
									["FAlias"] = actAlias
								};
								jArrayCdkey.Add(item);
							}
						}
						if (jObject2.Count != 10)
						{
							break;
						}
						continue;
					}
					BusDouYuCookies.AddCookieStatus(bagUser, cookieId, Util.GetJObject(res, "msg"));
					throw new Exception(Util.GetJObject(res, "msg"));
				}
				if (jArrayCdkey.Count > 0)
				{
					bagCdkey.Add(jArrayCdkey);
				}
				Util.WriteLog(jobGroup[1], cookieName, jobName, "【" + areaName + "】更新结束");
			}
			catch (Exception ex)
			{
				Util.WriteLog(jobGroup[1], cookieName, jobName, ex.Message, ConsoleColor.Red);
			}
		}
	}

	[DisallowConcurrentExecution]
	public class 更新任务信息 : IJob
	{
		public async Task Execute(IJobExecutionContext context)
		{
			string[] jobGroup = (context.JobDetail.JobDataMap.GetString("jobGroup") ?? "").Split("-");
			string jobName = context.JobDetail.JobDataMap.GetString("jobName") ?? "";
			ConcurrentBag<JToken> bagUser = new ConcurrentBag<JToken>();
			ConcurrentBag<JToken> bagCookieTask = new ConcurrentBag<JToken>();
			List<Task> taskList = new List<Task>();
			try
			{
				JObject @params = (context.JobDetail.JobDataMap.Get("params") as JObject) ?? new JObject();
				int seconds = context.JobDetail.JobDataMap.GetInt("seconds");
				int userId = context.JobDetail.JobDataMap.GetInt("userId");
				string areaId = Util.GetJObject(@params, "areaId");
				string cookieId = Util.GetJObject(@params, "cookieId");
				string keys = await QzUtil.Validate(userId, BusSysUser.Instance.User.Address, "斗鱼");
				int jObject = Util.GetJObject<int>(@params, "difference");
				if (jObject != 0)
				{
					int num = new Random().Next(0, jObject);
					seconds = ((seconds != 0) ? (seconds + num) : 0);
					DateTime datetime = DateTime.Now.AddSeconds(num);
					Util.WriteLog(jobGroup[1], jobGroup[0], jobName, datetime.ToString("yyyy-MM-dd HH:mm:ss") + "开始执行");
					while (DateTime.Now <= datetime && QzUtil.IsExecute(seconds, context))
					{
						await QzUtil.Delay();
					}
				}
				DataTable cookiesList = DouYuCookies.GetCookiesList(cookieId, keys, userId);
				DataTable areaList = DouYuArea.GetAreaList(areaId, BusSysUser.Instance.User.Organization.斗鱼);
				DataTable dtAreaTask = DouYuAreaTask.GetAreaTaskList(areaId);
				if (areaList.Rows.Count == 0)
				{
					throw new Exception("暂无该分区权限");
				}
				string taskUrl = Util.GetJObject(areaList.Rows[0], "FTaskUrl") + Util.GetJObject(areaList.Rows[0], "FTaskId");
				string referer = Util.GetJObject(areaList.Rows[0], "FReferer");
				string areaName = Util.GetJObject(areaList.Rows[0], "FName");
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, "开始");
				for (int i = 0; i < cookiesList.Rows.Count; i++)
				{
					if (!QzUtil.IsExecute(seconds, context))
					{
						break;
					}
					DataRow drCookie = cookiesList.Rows[i];
					taskList.Add(Task.Run(async delegate
					{
						await Execute2(jobGroup, jobName, userId, areaId, areaName, drCookie, taskUrl, referer, dtAreaTask, bagCookieTask, bagUser);
					}));
				}
				foreach (Task item in taskList)
				{
					item.Wait();
				}
				JArray jArray = new JArray();
				foreach (JToken item2 in bagCookieTask)
				{
					jArray.Merge(item2);
				}
				if (jArray.Count > 0)
				{
					DataTable dataTable = jArray.ToObject<DataTable>();
					if (dataTable != null)
					{
						DouYuCookiesTask.DelCookiesTask(cookieId, areaId, userId);
						SQLHelper.DouYuLocalDB.SqlBulkCopyByDataTable("TCookiesTask", dataTable);
					}
				}
				BusDouYuCookies.UpdateCookieStatus(bagUser, userId);
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, QzUtil.WriteStatus(context, seconds));
			}
			catch (Exception ex)
			{
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, ex.Message, ConsoleColor.Red);
			}
		}

		public static async Task Execute2(string[] jobGroup, string jobName, int userId, string areaId, string areaName, DataRow drCookie, string taskUrl, string referer, DataTable dtAreaTask, ConcurrentBag<JToken> bagCookieTask, ConcurrentBag<JToken> bagUser)
		{
			JArray jArrayCookieTask = new JArray();
			string cookieId = drCookie["Fid"].ToString() ?? "";
			string name = drCookie["FKey"].ToString() ?? "";
			string cookie = drCookie["FCookie"].ToString() ?? "";
			string cookieName = drCookie["FName"].ToString() ?? "";
			string header = drCookie["FHeaders"].ToString() ?? "";
			string text = drCookie["FProxyAddress"].ToString() ?? "";
			string userName = drCookie["FProxyUserName"].ToString() ?? "";
			string password = drCookie["FProxyPassword"].ToString() ?? "";
			try
			{
				Util.WriteLog(jobGroup[1], cookieName, jobName, "开始");
				HttpClientHandler defaultHandler = null;
				if (text != "")
				{
					defaultHandler = new HttpClientHandler
					{
						Proxy = new WebProxy
						{
							Address = new Uri(text),
							Credentials = new NetworkCredential(userName, password)
						}
					};
				}
				Dictionary<string, string> dictionary = HttpClientFactory.FormataHeader(header, cookie);
				dictionary["Referer"] = referer;
				dictionary["Host"] = "www.douyu.com";
				HttpClientFactory httpClientFactory = new HttpClientFactory(name, dictionary, defaultHandler);
				string typeName = jobGroup[1];
				string name2 = cookieName;
				Util.WriteLog(typeName, name2, jobName, await httpClientFactory.GetIp());
				Util.WriteLog(jobGroup[1], cookieName, jobName, "【" + areaName + "】开始更新");
				JObject jToken = await httpClientFactory.Get(taskUrl);
				if (Util.GetJObject(jToken, "error") == "0")
				{
					JArray jObject = Util.GetJObject<JArray>(jToken, "data");
					if (jObject != null)
					{
						foreach (JToken item2 in jObject)
						{
							string text2 = "";
							string text3 = "";
							string text4 = "";
							string text5 = "";
							string text6 = "";
							JArray jArray = Util.GetJObject<JArray>(item2, "condCompleteList") ?? new JArray();
							if (jArray.Count > 0)
							{
								text3 = Util.GetJObject(jArray[0], "name");
							}
							JArray jArray2 = Util.GetJObject<JArray>(item2, "prizeInfo") ?? new JArray();
							if (jArray2.Count > 0)
							{
								text2 = Util.GetJObject(jArray2[0], "giftAlias");
								if (text3 == "")
								{
									text3 = Util.GetJObject(jArray2[0], "name");
								}
								text4 = Util.GetJObject(Util.GetJObject<JToken>(jArray2[0], "remain"), "remainDesc");
								JArray jArray3 = Util.GetJObject<JArray>(jArray2[0], "prizeInBag") ?? new JArray();
								if (jArray3.Count > 0)
								{
									text5 = Util.GetJObject(jArray3[0], "prizeId");
									text6 = Util.GetJObject(jArray3[0], "prizeName");
								}
							}
							text3 = text3.Replace(text6, "").Trim('-');
							string text7 = "0";
							string text8 = "0";
							string text9 = "0";
							string text10 = "0";
							DataRow[] array = dtAreaTask.Select("FTaskId=" + Util.GetJObject(item2, "taskId"));
							if (array.Length == 1)
							{
								text7 = Util.GetJObject(array[0], "FDaily");
								text8 = Util.GetJObject(array[0], "FSort");
								text9 = Util.GetJObject(array[0], "FComplete");
								text10 = Util.GetJObject(array[0], "FCompulsory");
							}
							JObject item = new JObject
							{
								["Fid"] = "0",
								["FUserId"] = userId,
								["FAreaId"] = areaId,
								["FCookieId"] = cookieId,
								["FStatus"] = Util.GetJObject(item2, "status"),
								["FTaskId"] = Util.GetJObject(item2, "taskId"),
								["FTaskName"] = text3,
								["FPrizeId"] = text5,
								["FPrizeName"] = text6,
								["FPrizeGiftAlias"] = text2,
								["FRemainDesc"] = text4,
								["FCompulsory"] = text10,
								["FComplete"] = text9,
								["FDaily"] = text7,
								["FSort"] = text8,
								["FEnable"] = "1"
							};
							jArrayCookieTask.Add(item);
						}
					}
					if (jArrayCookieTask.Count > 0)
					{
						bagCookieTask.Add(jArrayCookieTask);
					}
					Util.WriteLog(jobGroup[1], cookieName, jobName, "【" + areaName + "】更新结束");
					return;
				}
				BusDouYuCookies.AddCookieStatus(bagUser, cookieId, Util.GetJObject(jToken, "msg"));
				throw new Exception(Util.GetJObject(jToken, "msg"));
			}
			catch (Exception ex)
			{
				Util.WriteLog(jobGroup[1], cookieName, jobName, ex.Message, ConsoleColor.Red);
			}
		}
	}

	[DisallowConcurrentExecution]
	public class 领取每日任务 : IJob
	{
		public async Task Execute(IJobExecutionContext context)
		{
			string[] jobGroup = (context.JobDetail.JobDataMap.GetString("jobGroup") ?? "").Split("-");
			string jobName = context.JobDetail.JobDataMap.GetString("jobName") ?? "";
			ConcurrentBag<JToken> bagUser = new ConcurrentBag<JToken>();
			List<Task> taskList = new List<Task>();
			try
			{
				JObject @params = (context.JobDetail.JobDataMap.Get("params") as JObject) ?? new JObject();
				int seconds = context.JobDetail.JobDataMap.GetInt("seconds");
				int userId = context.JobDetail.JobDataMap.GetInt("userId");
				string areaId = Util.GetJObject(@params, "areaId");
				string cookieId = Util.GetJObject(@params, "cookieId");
				string keys = await QzUtil.Validate(userId, BusSysUser.Instance.User.Address, "斗鱼");
				int jObject = Util.GetJObject<int>(@params, "difference");
				if (jObject != 0)
				{
					int num = new Random().Next(0, jObject);
					seconds = ((seconds != 0) ? (seconds + num) : 0);
					DateTime datetime = DateTime.Now.AddSeconds(num);
					Util.WriteLog(jobGroup[1], jobGroup[0], jobName, datetime.ToString("yyyy-MM-dd HH:mm:ss") + "开始执行");
					while (DateTime.Now <= datetime && QzUtil.IsExecute(seconds, context))
					{
						await QzUtil.Delay();
					}
				}
				DataTable cookiesList = DouYuCookies.GetCookiesList(cookieId, keys, userId);
				DataTable receiveTaskList = DouYuCookiesTask.GetReceiveTaskList(areaId, "", TaskType.Daily, userId);
				DataTable areaList = DouYuArea.GetAreaList(areaId, BusSysUser.Instance.User.Organization.斗鱼);
				DouYuAreaTask.GetAreaTaskList(areaId);
				if (areaList.Rows.Count == 0)
				{
					throw new Exception("暂无该分区权限");
				}
				string taskUrl = Util.GetJObject(areaList.Rows[0], "FTaskUrl");
				string referer = Util.GetJObject(areaList.Rows[0], "FReferer");
				string receiveUrl = Util.GetJObject(areaList.Rows[0], "FReceiveUrl");
				string areaName = Util.GetJObject(areaList.Rows[0], "FName");
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, "开始");
				for (int i = 0; i < cookiesList.Rows.Count; i++)
				{
					if (!QzUtil.IsExecute(seconds, context))
					{
						break;
					}
					DataRow drCookie = cookiesList.Rows[i];
					string text = drCookie["Fid"].ToString() ?? "";
					DataRow[] drCookiesTasks = receiveTaskList.Select("FCookieId=" + text);
					if (drCookiesTasks != null && drCookiesTasks.Length != 0)
					{
						taskList.Add(Task.Run(async delegate
						{
							await Receive(jobGroup, jobName, drCookie, drCookiesTasks.CopyToDataTable(), taskUrl, referer, receiveUrl, areaName, 0, seconds, context);
						}));
					}
				}
				foreach (Task item in taskList)
				{
					item.Wait();
				}
				BusDouYuCookies.UpdateCookieStatus(bagUser, userId);
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, QzUtil.WriteStatus(context, seconds));
			}
			catch (Exception ex)
			{
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, ex.Message, ConsoleColor.Red);
			}
		}
	}

	[DisallowConcurrentExecution]
	public class 开启直播 : IJob
	{
		public async Task Execute(IJobExecutionContext context)
		{
			string[] jobGroup = (context.JobDetail.JobDataMap.GetString("jobGroup") ?? "").Split("-");
			string jobName = context.JobDetail.JobDataMap.GetString("jobName") ?? "";
			ConcurrentBag<JToken> bagUser = new ConcurrentBag<JToken>();
			List<Task> taskList = new List<Task>();
			try
			{
				JObject @params = (context.JobDetail.JobDataMap.Get("params") as JObject) ?? new JObject();
				int seconds = context.JobDetail.JobDataMap.GetInt("seconds");
				int userId = context.JobDetail.JobDataMap.GetInt("userId");
				string areaId = Util.GetJObject(@params, "areaId");
				string cookieId = Util.GetJObject(@params, "cookieId");
				string keys = await QzUtil.Validate(userId, BusSysUser.Instance.User.Address, "斗鱼");
				int jObject = Util.GetJObject<int>(@params, "difference");
				if (jObject != 0)
				{
					int num = new Random().Next(0, jObject);
					seconds = ((seconds != 0) ? (seconds + num) : 0);
					DateTime datetime = DateTime.Now.AddSeconds(num);
					Util.WriteLog(jobGroup[1], jobGroup[0], jobName, datetime.ToString("yyyy-MM-dd HH:mm:ss") + "开始执行");
					while (DateTime.Now <= datetime && QzUtil.IsExecute(seconds, context))
					{
						await QzUtil.Delay();
					}
				}
				DataTable cookiesList = DouYuCookies.GetCookiesList(cookieId, keys, userId);
				DataTable dtCookiesLive = DouYuLive.GetLivePathAll(cookieId, areaId, userId);
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, "开始");
				for (int i = 0; i < cookiesList.Rows.Count; i++)
				{
					if (!QzUtil.IsExecute(seconds, context))
					{
						break;
					}
					DataRow drCookie = cookiesList.Rows[i];
					taskList.Add(Task.Run(async delegate
					{
						await Execute2(jobGroup, jobName, areaId, dtCookiesLive, drCookie, bagUser, userId, seconds, context);
					}));
				}
				foreach (Task item in taskList)
				{
					item.Wait();
				}
				BusDouYuCookies.UpdateCookieStatus(bagUser, userId);
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, QzUtil.WriteStatus(context, seconds));
			}
			catch (Exception ex)
			{
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, ex.Message, ConsoleColor.Red);
			}
		}

		public static async Task Execute2(string[] jobGroup, string jobName, string areaId, DataTable dtCookiesLive, DataRow drCookie, ConcurrentBag<JToken> bagUser, int userId, int seconds, IJobExecutionContext context)
		{
			string cookieId = drCookie["Fid"].ToString() ?? "";
			string key = drCookie["FKey"].ToString() ?? "";
			string cookie = drCookie["FCookie"].ToString() ?? "";
			string name = drCookie["FName"].ToString() ?? "";
			string header = drCookie["FHeaders"].ToString() ?? "";
			string text = drCookie["FProxyAddress"].ToString() ?? "";
			string userName = drCookie["FProxyUserName"].ToString() ?? "";
			string password = drCookie["FProxyPassword"].ToString() ?? "";
			try
			{
				Util.WriteLog(jobGroup[1], name, jobName, "开始");
				DataRow[] array = dtCookiesLive.Select("FCookieId=" + cookieId) ?? throw new Exception("请配置直播视频！");
				if (array.Length == 0)
				{
					throw new Exception("请配置直播视频！");
				}
				string livePath = array[0]["FLivePath"].ToString() ?? throw new Exception("请配置直播视频！");
				string text2 = Util.GetJObject(array[0], "FLiveLog");
				if (livePath == "")
				{
					throw new Exception("请配置直播视频！");
				}
				string directoryName = Path.GetDirectoryName(Directory.GetCurrentDirectory() + "\\WWWRoot\\" + livePath);
				if (directoryName == null || directoryName == "")
				{
					throw new Exception("视频路径不正确！");
				}
				livePath = "";
				string[] files = Directory.GetFiles(directoryName);
				string[] array2 = files;
				foreach (string text3 in array2)
				{
					string fileName = Path.GetFileName(text3);
					if (text2 == "")
					{
						text2 = fileName;
						livePath = text3;
						break;
					}
					if (!text2.Contains(fileName))
					{
						text2 = text2 + "," + fileName;
						livePath = text3;
						break;
					}
				}
				if (livePath == "")
				{
					string[] array3 = text2.Split(',');
					for (int j = 0; j < array3.Length; j++)
					{
						string text4 = directoryName + "\\" + array3[j];
						if (File.Exists(text4))
						{
							livePath = text4;
							text2 = string.Join(',', array3.Skip(j + 1)) + "," + array3[j];
							break;
						}
					}
				}
				DouYuLive.GetLivePathAll(cookieId, areaId, userId, text2);
				string cid2 = Util.GetJObject(array[0], "FCid2");
				string cid3 = Util.GetJObject(array[0], "FCid3");
				string areaName = Util.GetJObject(array[0], "FAreaName");
				HttpClientHandler defaultHandler = null;
				if (text != "")
				{
					defaultHandler = new HttpClientHandler
					{
						Proxy = new WebProxy
						{
							Address = new Uri(text),
							Credentials = new NetworkCredential(userName, password)
						}
					};
				}
				Dictionary<string, string> dic = HttpClientFactory.FormataHeader(header, cookie);
				new JObject();
				dic["Referer"] = "https://www.douyu.com/creator/main/live";
				dic["Host"] = "www.douyu.com";
				dic["Origin"] = "https://www.douyu.com";
				HttpClientFactory httpClientFactory = new HttpClientFactory(key, dic, defaultHandler);
				string typeName = jobGroup[1];
				string name2 = name;
				Util.WriteLog(typeName, name2, jobName, await httpClientFactory.GetIp());
				string ctn = Util.GetCookieByKey(cookie, "ccn");
				string ccn = Util.GetCookieByKey(cookie, "ctn");
				if (ctn == "" || ccn == "")
				{
					throw new Exception("Cookie不完整！");
				}
				Process process = null;
				while (QzUtil.IsExecute(seconds, context))
				{
					JObject jObject = await httpClientFactory.Get("https://www.douyu.com/japi/creator/w/apinc/live/room/getRoomInfo");
					if (Util.GetJObject(jObject, "error") == "0")
					{
						string jObject2 = Util.GetJObject(jObject["data"], "showStatus");
						if (jObject2 == "2")
						{
							if (process != null && !process.HasExited)
							{
								process.CloseMainWindow();
							}
							if (cid2 != Util.GetJObject(jObject["data"], "cid2"))
							{
								await Task.Delay(500);
								await httpClientFactory.Post("https://www.douyu.com/japi/creator/w/apinc/live/category/editCategoryByRid", "cid2=" + cid2 + "&cid3=" + cid3 + "&ocrCateSwitch=1&ctn=" + ctn + "&ccn=" + ccn);
								Util.WriteLog(jobGroup[1], name, jobName, "已设置分区【" + areaName + "】");
							}
							await Task.Delay(500);
							jObject = await httpClientFactory.Post("https://www.douyu.com/japi/creator/w/apinc/live/pushflow/openShow", "notshowtip=0&ctn=" + ctn + "&ccn=" + ccn);
							if (Util.GetJObject(jObject, "error") != "0")
							{
								throw new Exception(Util.GetJObject(jObject, "msg"));
							}
						}
						await Task.Delay(500);
						if (process == null || process.HasExited)
						{
							jObject = await httpClientFactory.Get("https://www.douyu.com/japi/creator/w/apinc/live/pushflow/getRtmp");
							if (!(Util.GetJObject(jObject, "error") == "0"))
							{
								throw new Exception(Util.GetJObject(jObject, "msg"));
							}
							string text5 = Util.GetJObject(jObject["data"], "rtmpUrl") + "/" + Util.GetJObject(jObject["data"], "rtmpVal");
							string arguments = "-re -stream_loop -1 -i \"" + livePath + "\" -vcodec copy -acodec aac -b:a 96k -f flv \"" + text5 + "\"";
							Util.WriteLog(jobGroup[1], name, jobName, "开始直播【" + areaName + "】");
							if (process != null && !process.HasExited)
							{
								process.CloseMainWindow();
							}
							process = Util.ProcessStart("\\FFmpeg\\ffmpeg.exe", arguments);
						}
						for (int k = 0; k < 30; k++)
						{
							if (!QzUtil.IsExecute(seconds, context))
							{
								break;
							}
							await QzUtil.Delay(500, 1500);
						}
						continue;
					}
					throw new Exception(Util.GetJObject(jObject, "msg"));
				}
				httpClientFactory = new HttpClientFactory(key, dic, defaultHandler);
				await httpClientFactory.Post("https://www.douyu.com/japi/creator/w/apinc/live/pushflow/closeShow", "ctn=" + ctn + "&ccn=" + ccn);
				Util.WriteLog(jobGroup[1], name, jobName, "结束");
			}
			catch (Exception ex)
			{
				Util.WriteLog(jobGroup[1], name, jobName, ex.Message, ConsoleColor.Red);
				BusDouYuCookies.AddCookieStatus(bagUser, cookieId, ex.Message);
			}
		}
	}

	[DisallowConcurrentExecution]
	public class 发送礼物 : IJob
	{
		public async Task Execute(IJobExecutionContext context)
		{
			string[] jobGroup = (context.JobDetail.JobDataMap.GetString("jobGroup") ?? "").Split("-");
			string jobName = context.JobDetail.JobDataMap.GetString("jobName") ?? "";
			ConcurrentBag<JToken> bagUser = new ConcurrentBag<JToken>();
			List<Task> taskList = new List<Task>();
			try
			{
				JObject @params = (context.JobDetail.JobDataMap.Get("params") as JObject) ?? new JObject();
				int seconds = context.JobDetail.JobDataMap.GetInt("seconds");
				int userId = context.JobDetail.JobDataMap.GetInt("userId");
				string areaId = Util.GetJObject(@params, "areaId");
				string cookieId = Util.GetJObject(@params, "cookieId");
				string keys = await QzUtil.Validate(userId, BusSysUser.Instance.User.Address, "斗鱼");
				int jObject = Util.GetJObject<int>(@params, "difference");
				if (jObject != 0)
				{
					int num = new Random().Next(0, jObject);
					seconds = ((seconds != 0) ? (seconds + num) : 0);
					DateTime datetime = DateTime.Now.AddSeconds(num);
					Util.WriteLog(jobGroup[1], jobGroup[0], jobName, datetime.ToString("yyyy-MM-dd HH:mm:ss") + "开始执行");
					while (DateTime.Now <= datetime && QzUtil.IsExecute(seconds, context))
					{
						await QzUtil.Delay();
					}
				}
				DataTable cookiesList = DouYuCookies.GetCookiesList(cookieId, keys, userId);
				DataTable liveGiftList = DouYuLive.GetLiveGiftList(cookieId, areaId, userId);
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, "开始");
				for (int i = 0; i < cookiesList.Rows.Count; i++)
				{
					if (!QzUtil.IsExecute(seconds, context))
					{
						break;
					}
					DataRow drCookie = cookiesList.Rows[i];
					string text = drCookie["Fid"].ToString() ?? "";
					DataRow[] drGift = liveGiftList.Select("FCookieId=" + text);
					if (drGift != null && drGift.Length != 0)
					{
						taskList.Add(Task.Run(async delegate
						{
							await Execute2(jobGroup, jobName, drGift.CopyToDataTable(), drCookie, bagUser, seconds, context);
						}));
					}
				}
				foreach (Task item in taskList)
				{
					item.Wait();
				}
				BusDouYuCookies.UpdateCookieStatus(bagUser, userId);
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, QzUtil.WriteStatus(context, seconds));
			}
			catch (Exception ex)
			{
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, ex.Message, ConsoleColor.Red);
			}
		}

		public static async Task Execute2(string[] jobGroup, string jobName, DataTable drGift, DataRow drCookie, ConcurrentBag<JToken> bagUser, int seconds, IJobExecutionContext context)
		{
			string cookieId = drCookie["Fid"].ToString() ?? "";
			string name = drCookie["FKey"].ToString() ?? "";
			string cookie = drCookie["FCookie"].ToString() ?? "";
			string name2 = drCookie["FName"].ToString() ?? "";
			string text = drCookie["FHeaders"].ToString() ?? "";
			string text2 = drCookie["FProxyAddress"].ToString() ?? "";
			string userName = drCookie["FProxyUserName"].ToString() ?? "";
			string password = drCookie["FProxyPassword"].ToString() ?? "";
			try
			{
				Util.WriteLog(jobGroup[1], name2, jobName, "开始");
				JObject.Parse(text);
				HttpClientHandler defaultHandler = null;
				if (text2 != "")
				{
					defaultHandler = new HttpClientHandler
					{
						Proxy = new WebProxy
						{
							Address = new Uri(text2),
							Credentials = new NetworkCredential(userName, password)
						}
					};
				}
				Dictionary<string, string> defaultHeaders = HttpClientFactory.FormataHeader(text, cookie);
				HttpClientFactory httpClientFactory = new HttpClientFactory(name, defaultHeaders, defaultHandler);
				string typeName = jobGroup[1];
				string name3 = name2;
				string jobName2 = jobName;
				Util.WriteLog(typeName, name3, jobName2, await httpClientFactory.GetIp());
				for (int i = 0; i < drGift.Rows.Count; i++)
				{
					if (!QzUtil.IsExecute(seconds, context))
					{
						break;
					}
					jobName2 = drGift.Rows[i]["FRoomNo"].ToString() ?? "";
					Dictionary<string, string> dicDynamic = new Dictionary<string, string>
					{
						{ "Origin", "https://www.douyu.com" },
						{ "Referer", "https://www.douyu.com/" }
					};
					name3 = drGift.Rows[i]["FGiftName"].ToString() ?? "";
					typeName = drGift.Rows[i]["FGiftCode"].ToString() ?? "";
					int giftNum = int.Parse(drGift.Rows[i]["FGiftNum"].ToString() ?? "0");
					int.Parse(drGift.Rows[i]["FGiftPrice"].ToString() ?? "0");
					int giftSkinId = int.Parse(drGift.Rows[i]["FGiftSkinId"].ToString() ?? "0");
					for (int j = 0; j < giftNum; j++)
					{
						if (!(typeName != ""))
						{
							break;
						}
						JObject jToken = await httpClientFactory.Post("https://www.douyu.com/japi/gift/donate/mainsite/v3", "giftId=" + typeName + "&giftCount=" + giftNum + "&roomId=" + jobName2 + "&bizExt=%7B%22isMainGift%22%3A1%7D&skinId=" + giftSkinId, dicDynamic);
						if (Util.GetJObject(jToken, "error") == "0")
						{
							Util.WriteLog(jobGroup[1], name2, jobName, "给【房间号" + jobName2 + "】发送一个" + name3);
						}
						else
						{
							Util.WriteLog(jobGroup[1], name2, jobName, Util.GetJObject(jToken, "msg"), ConsoleColor.Red);
						}
						await QzUtil.Delay(1000, 3000);
					}
				}
				Util.WriteLog(jobGroup[1], name2, jobName, "结束");
			}
			catch (Exception ex)
			{
				Util.WriteLog(jobGroup[1], name2, jobName, ex.Message, ConsoleColor.Red);
				BusDouYuCookies.AddCookieStatus(bagUser, cookieId, ex.Message);
			}
		}
	}

	[DisallowConcurrentExecution]
	public class 弹幕观看 : IJob
	{
		public async Task Execute(IJobExecutionContext context)
		{
			string[] jobGroup = (context.JobDetail.JobDataMap.GetString("jobGroup") ?? "").Split("-");
			string jobName = context.JobDetail.JobDataMap.GetString("jobName") ?? "";
			ConcurrentBag<JToken> bagUser = new ConcurrentBag<JToken>();
			List<Task> taskList = new List<Task>();
			try
			{
				JObject @params = (context.JobDetail.JobDataMap.Get("params") as JObject) ?? new JObject();
				int seconds = context.JobDetail.JobDataMap.GetInt("seconds");
				int userId = context.JobDetail.JobDataMap.GetInt("userId");
				string areaId = Util.GetJObject(@params, "areaId");
				string cookieId = Util.GetJObject(@params, "cookieId");
				string keys = await QzUtil.Validate(userId, BusSysUser.Instance.User.Address, "斗鱼");
				int jObject = Util.GetJObject<int>(@params, "difference");
				if (jObject != 0)
				{
					int num = new Random().Next(0, jObject);
					seconds = ((seconds != 0) ? (seconds + num) : 0);
					DateTime datetime = DateTime.Now.AddSeconds(num);
					Util.WriteLog(jobGroup[1], jobGroup[0], jobName, datetime.ToString("yyyy-MM-dd HH:mm:ss") + "开始执行");
					while (DateTime.Now <= datetime && QzUtil.IsExecute(seconds, context))
					{
						await QzUtil.Delay();
					}
				}
				DataTable cookiesList = DouYuCookies.GetCookiesList(cookieId, keys, userId);
				DataTable watchMinutesAll = DouYuLive.GetWatchMinutesAll(cookieId, areaId, userId);
				DataTable liveBulletScreenAll = DouYuLive.GetLiveBulletScreenAll(cookieId, areaId, userId);
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, "开始");
				for (int i = 0; i < cookiesList.Rows.Count; i++)
				{
					if (!QzUtil.IsExecute(seconds, context))
					{
						break;
					}
					DataRow drCookie = cookiesList.Rows[i];
					string text = drCookie["Fid"].ToString() ?? "";
					DataRow[] drWatchMinutes = watchMinutesAll.Select("FCookieId=" + text);
					DataRow[] drBulletScreen = liveBulletScreenAll.Select("FCookieId=" + text);
					if (drWatchMinutes != null && (drWatchMinutes.Length != 0 || drBulletScreen.Length != 0))
					{
						taskList.Add(Task.Run(async delegate
						{
							await Execute2(jobGroup, jobName, (drWatchMinutes.Length != 0) ? drWatchMinutes.CopyToDataTable() : new DataTable(), (drBulletScreen.Length != 0) ? drBulletScreen.CopyToDataTable() : new DataTable(), drCookie, bagUser, seconds, context);
						}));
					}
				}
				foreach (Task item in taskList)
				{
					item.Wait();
				}
				BusDouYuCookies.UpdateCookieStatus(bagUser, userId);
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, QzUtil.WriteStatus(context, seconds));
			}
			catch (Exception ex)
			{
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, ex.Message, ConsoleColor.Red);
			}
		}

		public static async Task Execute2(string[] jobGroup, string jobName, DataTable dtWatchMinutes, DataTable dtBulletScreen, DataRow drCookie, ConcurrentBag<JToken> bagUser, int seconds, IJobExecutionContext context)
		{
			List<Task> taskList = new List<Task>();
			string cookie = drCookie["FCookie"].ToString() ?? "";
			string text = drCookie["FName"].ToString() ?? "";
			string header = drCookie["FHeaders"].ToString() ?? "";
			string text2 = drCookie["FProxyAddress"].ToString() ?? "";
			string userName = drCookie["FProxyUserName"].ToString() ?? "";
			string password = drCookie["FProxyPassword"].ToString() ?? "";
			HttpClientHandler defaultHandler = null;
			if (text2 != "")
			{
				defaultHandler = new HttpClientHandler
				{
					Proxy = new WebProxy
					{
						Address = new Uri(text2),
						Credentials = new NetworkCredential(userName, password)
					}
				};
			}
			Dictionary<string, string> defaultHeaders = HttpClientFactory.FormataHeader(header, cookie);
			HttpClientFactory httpClientFactory = new HttpClientFactory(text, defaultHeaders, defaultHandler);
			string typeName = jobGroup[1];
			string name = text;
			string jobName2 = jobName;
			Util.WriteLog(typeName, name, jobName2, await httpClientFactory.GetIp());
			for (int i = 0; i < dtWatchMinutes.Rows.Count; i++)
			{
				if (!QzUtil.IsExecute(seconds, context))
				{
					break;
				}
				string ruRoomId = dtWatchMinutes.Rows[i]["FRoomNo"].ToString() ?? "";
				int watchMinutes = int.Parse(dtWatchMinutes.Rows[i]["FWatchMinutes"].ToString() ?? "0");
				DataRow[] drBulletScreen = dtBulletScreen.Select("FRoomNo=" + ruRoomId);
				taskList.Add(Task.Run(async delegate
				{
					await Execute3(jobGroup, jobName, ruRoomId, watchMinutes, (drBulletScreen.Length != 0) ? drBulletScreen.CopyToDataTable() : new DataTable(), httpClientFactory, drCookie, bagUser, seconds, context);
				}));
			}
			foreach (Task item in taskList)
			{
				item.Wait();
			}
		}

		public static async Task Execute3(string[] jobGroup, string jobName, string roomId, int watchMinutes, DataTable dtBulletScreen, HttpClientFactory httpClientFactory, DataRow drCookie, ConcurrentBag<JToken> bagUser, int seconds, IJobExecutionContext context)
		{
			string cookieId = drCookie["Fid"].ToString() ?? "";
			string name = drCookie["FName"].ToString() ?? "";
			string cookies = drCookie["FCookie"].ToString() ?? "";
			try
			{
				string cookieByKey = Util.GetCookieByKey(cookies, "acf_username");
				string cookieByKey2 = Util.GetCookieByKey(cookies, "acf_ltkid");
				string cookieByKey3 = Util.GetCookieByKey(cookies, "acf_stk");
				string cookieByKey4 = Util.GetCookieByKey(cookies, "acf_did");
				string cookieByKey5 = Util.GetCookieByKey(cookies, "acf_dmjwt_token");
				string cookieByKey6 = Util.GetCookieByKey(cookies, "acf_uid");
				if (cookieByKey == "" || cookieByKey2 == "" || cookieByKey3 == "" || cookieByKey4 == "" || cookieByKey5 == "" || cookieByKey6 == "")
				{
					throw new Exception("Cookie缺少参数！");
				}
				BusDouYuUtil.DouyuLiveChat douyuLiveChat = new BusDouYuUtil.DouyuLiveChat(roomId, cookieByKey, cookieByKey2, cookieByKey3, cookieByKey4, cookieByKey5, cookieByKey6, httpClientFactory);
				Util.WriteLog(jobGroup[1], name, jobName, "进入直播间【" + roomId + "】");
				for (int i = 0; i < dtBulletScreen.Rows.Count; i++)
				{
					if (!QzUtil.IsExecute(seconds, context))
					{
						break;
					}
					await Task.Delay(6000);
					string msg = Util.GetJObject(dtBulletScreen.Rows[i], "FContent", "秀" + i);
					await douyuLiveChat.SendBarrage(msg);
					Util.WriteLog(jobGroup[1], name, jobName, "给直播间【" + roomId + "】发送弹幕：" + msg);
				}
				for (int i = 0; i < watchMinutes * 60; i++)
				{
					if (!QzUtil.IsExecute(seconds, context))
					{
						break;
					}
					await Task.Delay(1000);
				}
				douyuLiveChat.Dispose();
				Util.WriteLog(jobGroup[1], name, jobName, "结束");
			}
			catch (Exception ex)
			{
				Util.WriteLog(jobGroup[1], name, jobName, ex.Message, ConsoleColor.Red);
				BusDouYuCookies.AddCookieStatus(bagUser, cookieId, ex.Message);
			}
		}
	}

	[DisallowConcurrentExecution]
	public class 领取里程任务 : IJob
	{
		public async Task Execute(IJobExecutionContext context)
		{
			string[] jobGroup = (context.JobDetail.JobDataMap.GetString("jobGroup") ?? "").Split("-");
			string jobName = context.JobDetail.JobDataMap.GetString("jobName") ?? "";
			ConcurrentBag<JToken> bagUser = new ConcurrentBag<JToken>();
			List<Task> taskList = new List<Task>();
			try
			{
				JObject @params = (context.JobDetail.JobDataMap.Get("params") as JObject) ?? new JObject();
				int seconds = context.JobDetail.JobDataMap.GetInt("seconds");
				int userId = context.JobDetail.JobDataMap.GetInt("userId");
				string areaId = Util.GetJObject(@params, "areaId");
				string cookieId = Util.GetJObject(@params, "cookieId");
				string keys = await QzUtil.Validate(userId, BusSysUser.Instance.User.Address, "斗鱼");
				int jObject = Util.GetJObject<int>(@params, "difference");
				string jObject2 = Util.GetJObject(@params, "param");
				int.TryParse(jObject2, out var times);
				if (jObject != 0)
				{
					int num = new Random().Next(0, jObject);
					seconds = ((seconds != 0) ? (seconds + num) : 0);
					DateTime datetime = DateTime.Now.AddSeconds(num);
					Util.WriteLog(jobGroup[1], jobGroup[0], jobName, datetime.ToString("yyyy-MM-dd HH:mm:ss") + "开始执行");
					while (DateTime.Now <= datetime && QzUtil.IsExecute(seconds, context))
					{
						await QzUtil.Delay();
					}
				}
				DataTable dtCookies = DouYuCookies.GetCookiesList(cookieId, keys, userId);
				DataTable areaList = DouYuArea.GetAreaList(areaId, BusSysUser.Instance.User.Organization.斗鱼);
				DouYuAreaTask.GetAreaTaskList(areaId);
				if (areaList.Rows.Count == 0)
				{
					throw new Exception("暂无该分区权限");
				}
				string taskUrl = Util.GetJObject(areaList.Rows[0], "FTaskUrl");
				string referer = Util.GetJObject(areaList.Rows[0], "FReferer");
				string receiveUrl = Util.GetJObject(areaList.Rows[0], "FReceiveUrl");
				string areaName = Util.GetJObject(areaList.Rows[0], "FName");
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, "开始");
				DateTime startDate = DateTime.Parse(DateTime.Now.ToString("yyyy-MM-dd 17:59:58"));
				DataTable dtCookiesTasks = DouYuCookiesTask.GetReceiveTaskList(areaId, "", TaskType.Normal, userId);
				if (DateTime.Now < startDate)
				{
					Util.WriteLog(jobGroup[1], jobGroup[0], jobName, "18点之后才可以领取里程 等待中...", ConsoleColor.Yellow);
					while (DateTime.Now <= startDate && QzUtil.IsExecute(seconds, context))
					{
						await Task.Delay(100);
					}
				}
				for (int i = 0; i < dtCookies.Rows.Count; i++)
				{
					if (!QzUtil.IsExecute(seconds, context))
					{
						break;
					}
					DataRow drCookie = dtCookies.Rows[i];
					string text = drCookie["Fid"].ToString() ?? "";
					DataRow[] drCookiesTasks = dtCookiesTasks.Select("FCookieId=" + text);
					if (drCookiesTasks != null && drCookiesTasks.Length != 0)
					{
						taskList.Add(Task.Run(async delegate
						{
							await Receive(jobGroup, jobName, drCookie, drCookiesTasks.CopyToDataTable(), taskUrl, referer, receiveUrl, areaName, times, seconds, context);
						}));
					}
				}
				foreach (Task item in taskList)
				{
					item.Wait();
				}
				BusDouYuCookies.UpdateCookieStatus(bagUser, userId);
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, QzUtil.WriteStatus(context, seconds));
			}
			catch (Exception ex)
			{
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, ex.Message, ConsoleColor.Red);
			}
		}
	}

	private static async Task Receive(string[] jobGroup, string jobName, DataRow drCookie, DataTable dtCookieTask, string taskUrl, string referer, string receiveUrl, string areaName, int postTimes, int seconds, IJobExecutionContext context)
	{
		string name = drCookie["FKey"].ToString() ?? "";
		string cookie = drCookie["FCookie"].ToString() ?? "";
		string name2 = drCookie["FName"].ToString() ?? "";
		string text = drCookie["FHeaders"].ToString() ?? "";
		string text2 = drCookie["FProxyAddress"].ToString() ?? "";
		string userName = drCookie["FProxyUserName"].ToString() ?? "";
		string password = drCookie["FProxyPassword"].ToString() ?? "";
		try
		{
			Util.WriteLog(jobGroup[1], name2, jobName, "开始");
			JObject.Parse(text);
			HttpClientHandler defaultHandler = null;
			if (text2 != "")
			{
				defaultHandler = new HttpClientHandler
				{
					Proxy = new WebProxy
					{
						Address = new Uri(text2),
						Credentials = new NetworkCredential(userName, password)
					}
				};
			}
			Dictionary<string, string> dictionary = HttpClientFactory.FormataHeader(text, cookie);
			dictionary["Referer"] = referer;
			dictionary["Host"] = "www.douyu.com";
			HttpClientFactory httpClientFactory = new HttpClientFactory(name, dictionary, defaultHandler);
			for (int i = 0; i < dtCookieTask.Rows.Count; i++)
			{
				if (!QzUtil.IsExecute(seconds, context))
				{
					break;
				}
				List<Task> taskList = new List<Task>();
				ConcurrentBag<bool> bag = new ConcurrentBag<bool>();
				string taskId = Util.GetJObject(dtCookieTask.Rows[i], "FTaskId");
				string taskName = Util.GetJObject(dtCookieTask.Rows[i], "FTaskName");
				Util.WriteLog(jobGroup[1], name2, jobName, "正在领取" + taskName);
				int k = 0;
				while (k < 20 && QzUtil.IsExecute(seconds, context) && bag.IsEmpty)
				{
					JObject jToken = await httpClientFactory.Get(taskUrl + taskId);
					if (Util.GetJObject(jToken, "error") == "0")
					{
						JArray jObject = Util.GetJObject<JArray>(jToken, "data");
						if (jObject != null && jObject.Count == 1)
						{
							string jObject2 = Util.GetJObject(jObject[0], "status");
							switch (jObject2)
							{
							case "3":
								break;
							case "4":
								goto IL_04c0;
							case "5":
								goto IL_0559;
							case "1":
								goto IL_05f2;
							default:
								Util.WriteLog(jobGroup[1], name2, jobName, "【" + areaName + "】" + taskName + " 未知状态(" + jObject2 + ")！", ConsoleColor.Red);
								goto IL_07e4;
							case "2":
								goto IL_07e4;
							}
							Util.WriteLog(jobGroup[1], name2, jobName, "【" + areaName + "】" + taskName + " 已领取！", ConsoleColor.Green);
							break;
						}
						Util.WriteLog(jobGroup[1], name2, jobName, "【" + areaName + "】" + taskName + " 未找到任务！", ConsoleColor.Red);
					}
					else
					{
						Util.WriteLog(jobGroup[1], name2, jobName, Util.GetJObject(jToken, "msg"), ConsoleColor.Red);
					}
					goto IL_07e4;
					IL_07e4:
					for (int j = 0; j < postTimes; j++)
					{
						if (!bag.IsEmpty)
						{
							break;
						}
						int index = j + 1;
						await Task.Delay(1000 / postTimes);
						taskList.Add(Task.Run(() => PostUrl(httpClientFactory, receiveUrl, "taskId=" + taskId, jobGroup, name2, jobName, taskName, index, areaName, bag)));
					}
					k++;
					continue;
					IL_04c0:
					Util.WriteLog(jobGroup[1], name2, jobName, "【" + areaName + "】" + taskName + " 已抢光！", ConsoleColor.Red);
					break;
					IL_05f2:
					Util.WriteLog(jobGroup[1], name2, jobName, "【" + areaName + "】" + taskName + " 未完成！", ConsoleColor.Red);
					break;
					IL_0559:
					Util.WriteLog(jobGroup[1], name2, jobName, "【" + areaName + "】" + taskName + " 任务结束！", ConsoleColor.Red);
					break;
				}
			}
			Util.WriteLog(jobGroup[1], name2, jobName, "结束");
		}
		catch (Exception ex)
		{
			Util.WriteLog(jobGroup[1], name2, jobName, ex.Message, ConsoleColor.Red);
		}
	}

	private static async Task PostUrl(HttpClientFactory httpClientFactory, string url, string @params, string[] jobGroup, string name, string jobName, string taskName, int index, string areaName, ConcurrentBag<bool> bag)
	{
		JObject jToken = await httpClientFactory.Post(url, @params);
		if (bag.IsEmpty)
		{
			if (Util.GetJObject(jToken, "error") == "0" || Util.GetJObject(jToken, "error") == "2002")
			{
				Util.WriteLog(jobGroup[1], name, jobName, "【" + areaName + "】" + taskName + " 正在冲击第" + index + "次！" + Util.GetJObject(jToken, "msg"), ConsoleColor.Green);
				bag.Add(item: true);
			}
			else
			{
				Util.WriteLog(jobGroup[1], name, jobName, "【" + areaName + "】" + taskName + " 正在冲击第" + index + "次！" + Util.GetJObject(jToken, "msg"), ConsoleColor.Yellow);
			}
		}
	}
}
