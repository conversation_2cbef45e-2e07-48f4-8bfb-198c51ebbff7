using System.Collections.Generic;
using System.Data;
using System.Linq;
using Newtonsoft.Json.Linq;

namespace API.BusService.System;

public class BusSysMenu
{
	public static JArray GetMenuChildList(DataTable dtAll, long parentId, DataTable? dtApiListAll = null)
	{
		JArray jArray = new JArray();
		IEnumerable<DataRow> source = from Parent in dtAll.AsEnumerable()
			where Parent.Field<long>("FParentId") == parentId
			orderby Parent.Field<long>("FSort")
			select Parent;
		if (source.Any())
		{
			DataTable dataTable = source.CopyToDataTable();
			foreach (DataRow row in dataTable.Rows)
			{
				JObject jObject = new JObject();
				string id = row["Fid"].ToString();
				jObject["id"] = id;
				jObject["parentId"] = row["FParentId"].ToString();
				jObject["name"] = row["FName"].ToString();
				jObject["path"] = row["FPath"].ToString();
				jObject["component"] = row["FComponent"].ToString();
				jObject["redirect"] = row["FFredirect"].ToString();
				jObject["active"] = row["FActive"].ToString();
				jObject["enable"] = row["FEnable"].ToString();
				if (dtApiListAll != null && dtApiListAll.Rows.Count > 0)
				{
					source = from Parent in dtApiListAll.AsEnumerable()
						where Parent.Field<long>("FFid") == long.Parse(id ?? "")
						orderby Parent.Field<int>("Fid")
						select Parent;
					if (source.Any())
					{
						DataTable dataTable2 = source.CopyToDataTable();
						JArray jArray2 = new JArray();
						for (int num = 0; num < dataTable2.Rows.Count; num++)
						{
							JObject item = new JObject
							{
								["name"] = dataTable2.Rows[num]["FName"].ToString(),
								["url"] = dataTable2.Rows[num]["FUrl"].ToString()
							};
							jArray2.Add(item);
						}
						jObject["apiList"] = jArray2;
					}
				}
				JObject value = new JObject
				{
					["title"] = row["FTitle"].ToString(),
					["tag"] = row["FTag"].ToString(),
					["icon"] = row["FIcon"].ToString(),
					["type"] = row["FType"].ToString(),
					["affix"] = bool.Parse(row["FAffix"].ToString() ?? "false"),
					["color"] = row["FColor"].ToString(),
					["hidden"] = bool.Parse(row["FHidden"].ToString() ?? "false"),
					["hiddenBreadcrumb"] = bool.Parse(row["FHiddenBreadcrumb"].ToString() ?? "false"),
					["fullpage"] = bool.Parse(row["FFullpage"].ToString() ?? "false")
				};
				jObject["meta"] = value;
				source = from Parent in dtAll.AsEnumerable()
					where Parent.Field<long>("FParentId") == long.Parse(id ?? "")
					orderby Parent.Field<long>("FSort")
					select Parent;
				if (source.Any())
				{
					DataTable dataTable3 = source.CopyToDataTable();
					if (dataTable3.Rows.Count > 0)
					{
						JArray menuChildList = GetMenuChildList(dtAll, long.Parse(id ?? ""), dtApiListAll);
						jObject["children"] = menuChildList;
					}
				}
				jArray.Add(jObject);
			}
		}
		return jArray;
	}
}
