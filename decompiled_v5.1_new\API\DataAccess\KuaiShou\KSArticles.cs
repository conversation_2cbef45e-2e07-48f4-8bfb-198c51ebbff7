using System;
using System.Data;
using System.IO;
using System.Linq;
using API.Common;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace API.DataAccess.KuaiShou;

public class KSArticles
{
	public static DataTable GetUserList(string search)
	{
		string text = " SELECT FKey AS id,FName AS label FROM TCookies WHERE FExpirationTime>GETDATE()";
		if (search != "")
		{
			text = text + " AND FName LIKE '%" + search + "%'";
		}
		return SQLHelper.KSLocalDB.RunSqlDt(text);
	}

	public static JObject GetFileList(string path, string type, string groupId, string search, int page = 1, int paseSize = 3)
	{
		path = Directory.GetCurrentDirectory() + $"\\wwwroot\\files\\{path}\\{type}\\{groupId}\\";
		if (!Directory.Exists(path))
		{
			Directory.CreateDirectory(path);
		}
		DirectoryInfo directoryInfo = new DirectoryInfo(path);
		FileInfo[] files = directoryInfo.GetFiles();
		int num = files.Length;
		files = files.Skip((page - 1) * paseSize).Take(paseSize).ToArray();
		JArray jArray = new JArray();
		FileInfo[] array = files;
		foreach (FileInfo fileInfo in array)
		{
			if (search == "" || fileInfo.Name.Contains(search))
			{
				string oldValue = (Directory.GetCurrentDirectory() + "\\wwwroot\\").ToLower();
				jArray.Add(new JObject
				{
					["id"] = Guid.NewGuid(),
					["fileName"] = fileInfo.Name,
					["url"] = fileInfo.FullName.ToLower().Replace(oldValue, "")
				});
			}
		}
		return new JObject
		{
			["total"] = num,
			["rows"] = jArray,
			["page"] = page,
			["pageSize"] = paseSize
		};
	}

	public static void SaveConfig(JObject jObject, int userId, string userName, string curTime)
	{
		string jObject2 = Util.GetJObject(jObject, "FCookieId");
		string jObject3 = Util.GetJObject(jObject, "FAreaId");
		string sSql = "SELECT ISNULL((SELECT Fid FROM TArticles WHERE FCookieId =" + jObject2 + "),0)";
		string text = SQLHelper.KSLocalDB.RunSqlStr(sSql);
		sSql = " SELECT FConfig FROM TArticles WHERE FCookieId =" + jObject2;
		string text2 = SQLHelper.KSLocalDB.RunSqlStr(sSql);
		JObject jObject4 = new JObject { [jObject3] = jObject };
		if (text2 != "")
		{
			jObject4 = JObject.Parse(text2);
			jObject4[jObject3] = jObject;
		}
		DataCURD.Save(new JObject
		{
			["Fid"] = text,
			["FCookieId"] = jObject2,
			["FUserId"] = userId,
			["FConfig"] = JsonConvert.SerializeObject(jObject4)
		}, "TArticles", "保存投稿生成视频配置", "Fid", userId, userName, curTime, SQLHelper.KSLocalDB.InitCnn());
	}
}
