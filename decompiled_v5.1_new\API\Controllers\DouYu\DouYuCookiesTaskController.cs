using System;
using System.Data;
using API.BusService.System;
using API.Common;
using API.DataAccess.DouYu;
using API.Models.Comm;
using Microsoft.AspNetCore.Mvc;

namespace API.Controllers.DouYu;

public class DouYuCookiesTaskController : Controller
{
	[HttpPost]
	public Response GetCookiesTaskList([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectSearch, "cookieId");
			string jObject2 = Util.GetJObject(model.jObjectSearch, "areaId");
			DataTable cookiesTaskList = DouYuCookiesTask.GetCookiesTaskList(jObject, jObject2, BusSysUser.Instance.User.Id, model.limit, model.offset, model.prop, model.order);
			response.data = Util.GetTableResponse(cookiesTaskList);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response SetCompulsory([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			DouYuCookiesTask.SetCompulsory(model.jObjectParam, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response SetSort([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			DouYuCookiesTask.SetSort(model.jObjectParam, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response RestoreDefault([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "cookieId");
			string jObject2 = Util.GetJObject(model.jObjectParam, "areaId");
			DouYuCookiesTask.RestoreDefault(jObject, jObject2, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response SyncAll([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "cookieId");
			string jObject2 = Util.GetJObject(model.jObjectParam, "areaId");
			DouYuCookiesTask.SyncAll(jObject, jObject2, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response DelAll([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "cookieId");
			string jObject2 = Util.GetJObject(model.jObjectParam, "areaId");
			DouYuCookiesTask.DelAll(jObject, jObject2, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response EnableSwitch([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			response.data = DouYuCookiesTask.EnableSwitch(model.jObjectParam, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
		}
		catch (Exception ex)
		{
			string jObject = Util.GetJObject(model.jObjectParam, "FEnable");
			response.data = ((!(jObject == "1")) ? 1 : 0);
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}
}
