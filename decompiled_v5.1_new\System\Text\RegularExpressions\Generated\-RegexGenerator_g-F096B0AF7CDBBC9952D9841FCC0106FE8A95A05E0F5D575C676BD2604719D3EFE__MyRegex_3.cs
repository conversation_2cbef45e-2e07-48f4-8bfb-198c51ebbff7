using System.CodeDom.Compiler;
using System.Runtime.CompilerServices;

namespace System.Text.RegularExpressions.Generated;

[GeneratedCode("System.Text.RegularExpressions.Generator", "8.0.10.46610")]
[SkipLocalsInit]
internal sealed class _003CRegexGenerator_g_003EF096B0AF7CDBBC9952D9841FCC0106FE8A95A05E0F5D575C676BD2604719D3EFE__MyRegex_3 : Regex
{
	private sealed class RunnerFactory : RegexRunnerFactory
	{
		private sealed class Runner : RegexRunner
		{
			protected override void Scan(ReadOnlySpan<char> inputSpan)
			{
				if (TryFindNextPossibleStartingPosition(inputSpan) && !TryMatchAtCurrentPosition(inputSpan))
				{
					runtextpos = inputSpan.Length;
				}
			}

			private bool TryFindNextPossibleStartingPosition(ReadOnlySpan<char> inputSpan)
			{
				int num = runtextpos;
				if ((uint)num < (uint)inputSpan.Length && num == 0)
				{
					return true;
				}
				runtextpos = inputSpan.Length;
				return false;
			}

			private bool TryMatchAtCurrentPosition(ReadOnlySpan<char> inputSpan)
			{
				int num = runtextpos;
				int start = num;
				ReadOnlySpan<char> span = inputSpan.Slice(num);
				if (num != 0)
				{
					return false;
				}
				int num2 = span.IndexOfAnyExcept(_003CRegexGenerator_g_003EF096B0AF7CDBBC9952D9841FCC0106FE8A95A05E0F5D575C676BD2604719D3EFE__Utilities.s_asciiLettersAndDigits);
				if (num2 < 0)
				{
					num2 = span.Length;
				}
				if (num2 == 0)
				{
					return false;
				}
				span = span.Slice(num2);
				num += num2;
				if (num < inputSpan.Length - 1 || ((uint)num < (uint)inputSpan.Length && inputSpan[num] != '\n'))
				{
					return false;
				}
				runtextpos = num;
				Capture(0, start, num);
				return true;
			}
		}

		protected override RegexRunner CreateInstance()
		{
			return new Runner();
		}
	}

	internal static readonly _003CRegexGenerator_g_003EF096B0AF7CDBBC9952D9841FCC0106FE8A95A05E0F5D575C676BD2604719D3EFE__MyRegex_3 Instance = new _003CRegexGenerator_g_003EF096B0AF7CDBBC9952D9841FCC0106FE8A95A05E0F5D575C676BD2604719D3EFE__MyRegex_3();

	private _003CRegexGenerator_g_003EF096B0AF7CDBBC9952D9841FCC0106FE8A95A05E0F5D575C676BD2604719D3EFE__MyRegex_3()
	{
		pattern = "^[a-zA-Z0-9]+$";
		roptions = RegexOptions.None;
		Regex.ValidateMatchTimeout(_003CRegexGenerator_g_003EF096B0AF7CDBBC9952D9841FCC0106FE8A95A05E0F5D575C676BD2604719D3EFE__Utilities.s_defaultTimeout);
		internalMatchTimeout = _003CRegexGenerator_g_003EF096B0AF7CDBBC9952D9841FCC0106FE8A95A05E0F5D575C676BD2604719D3EFE__Utilities.s_defaultTimeout;
		factory = new RunnerFactory();
		capsize = 1;
	}
}
