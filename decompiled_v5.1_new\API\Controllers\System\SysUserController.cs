using System;
using System.Data;
using System.Threading.Tasks;
using API.BusService.System;
using API.Common;
using API.DataAccess.System;
using API.Models.Comm;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;

namespace API.Controllers.System;

public class SysUserController : Controller
{
	[HttpPost]
	public async Task<Response> EditPWD([FromBody] Request model)
	{
		Response mRet = new Response();
		try
		{
			model.jObjectParam["userId"] = BusSysUser.Instance.User.Id;
			model.jObjectParam["userName"] = BusSysUser.Instance.User.Name;
			Response response = mRet;
			response.data = await Util.Request("/Common/EditPWD", model, base.HttpContext);
			string jObject = Util.GetJObject(model.jObjectParam, "oldpw1");
			string jObject2 = Util.GetJObject(model.jObjectParam, "newpw1");
			string jObject3 = Util.GetJObject(model.jObjectParam, "newpw2");
			if (mRet.data == null)
			{
				SysUser.EditPWD(jObject, jObject2, jObject3, BusSysUser.Instance.User.Name, BusSysUser.Instance.User.Id, model.curTime);
			}
			mRet.data = "";
			mRet.message = "您的密码是：" + jObject2;
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}

	[HttpPost]
	public Response ResetPWD([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			JArray jArray = (JArray)model.jObjectParam["arr"];
			SysUser.ResetPWD(jArray ?? new JArray(), BusSysUser.Instance.User.Name, BusSysUser.Instance.User.Id, model.curTime);
			response.message = "重置成功！密码与账号一致，请及时修改密码！";
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response GetSysUserList([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectSearch, "search");
			string jObject2 = Util.GetJObject(model.jObjectSearch, "enable", "1");
			string jObject3 = Util.GetJObject(model.jObjectSearch, "roleId");
			string jObject4 = Util.GetJObject(model.jObjectSearch, "provinces");
			string jObject5 = Util.GetJObject(model.jObjectSearch, "lastLoginTime");
			DataTable sysUserList = SysUser.GetSysUserList(jObject, jObject5, jObject4, jObject3, jObject2, model.limit, model.offset, model.prop, model.order);
			response.data = Util.GetTableResponse(sysUserList);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response EnableSysUser([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			SysUser.EditSysUser(model.jObjectParam, BusSysUser.Instance.User.Name, BusSysUser.Instance.User.Id, model.curTime);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response EditSysUser([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			SysUser.EditSysUser(model.jObjectParam, BusSysUser.Instance.User.Name, BusSysUser.Instance.User.Id, model.curTime);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response SaveSysUser([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			response.message = SysUser.SaveSysUser(model.jObjectParam, BusSysUser.Instance.User.Name, BusSysUser.Instance.User.Id, model.curTime);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}
}
