using System;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using API.BusService.System;
using API.Common;
using API.DataAccess.KuaiShou;
using API.Models.Comm;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;

namespace API.Controllers.KuaiShou;

public class KSCookiesController : Controller
{
	[HttpPost]
	public async Task<Response> EditCookie([FromBody] Request model)
	{
		Response mRet = new Response();
		try
		{
			await KSCookies.EditCookie(model.jObjectParam, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
			return mRet;
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}

	[HttpPost]
	public async Task<Response> DelCookie([FromBody] Request model)
	{
		Response mRet = new Response();
		try
		{
			await KSCookies.DelCookie(model.jObjectParam, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
			return mRet;
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}

	[HttpPost]
	public async Task<Response> AddExpiraation([FromBody] Request model)
	{
		Response mRet = new Response();
		try
		{
			JArray jObject = Util.GetJObject<JArray>(model.jObjectParam, "array");
			string text = string.Join(",", (jObject ?? new JArray()).Select((JToken token) => (token["FKey"] ?? ((JToken)"")).ToString()).ToArray());
			string text2 = "快手";
			string jObject2 = Util.GetJObject(model.jObjectParam, "org");
			JObject jObjectParam = new JObject
			{
				["userId"] = BusSysUser.Instance.User.Id,
				["key"] = text,
				["name"] = text2,
				["org"] = jObject2
			};
			model.jObjectParam = jObjectParam;
			Response response = mRet;
			response.data = await Util.Request("/Common/AddExpiraation", model);
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}

	[HttpPost]
	public async Task<Response> GetSysOrganizationList([FromBody] Request model)
	{
		Response mRet = new Response();
		try
		{
			Response response = mRet;
			response.data = await KSCookies.GetSysOrganizationList(model, BusSysUser.Instance.User.Organization.BiliBili);
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}

	[HttpPost]
	public async Task<Response> UpdateCookieExpires([FromBody] Request model)
	{
		Response mRet = new Response();
		try
		{
			await KSCookies.UpdateCookieExpires(BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}

	[HttpPost]
	public async Task<Response> AddCookies([FromBody] Request model)
	{
		Response mRet = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "cookieNum");
			await KSCookies.AddCookies(jObject, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
			return mRet;
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}

	[HttpPost]
	public Response GetCookiesList([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectSearch, "search");
			string jObject2 = Util.GetJObject(model.jObjectSearch, "id");
			DataTable cookiesList = KSCookies.GetCookiesList("", jObject2, jObject, BusSysUser.Instance.User.Id, model.limit, model.offset, model.prop, model.order);
			response.data = Util.GetTableResponse(cookiesList);
			return response;
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
			return response;
		}
	}

	[HttpPost]
	public Response UpdateSystem()
	{
		Response response = new Response();
		try
		{
			string text = " IF NOT EXISTS (SELECT 1 FROM sys.objects WHERE object_id = OBJECT_ID(N'TTaskTag') AND type = N'U')";
			text += " CREATE TABLE [dbo].[TTaskTag]([Fid] [int] IDENTITY(1,1) NOT NULL,[FTask] [nvarchar](200) NOT NULL,[FTag] [nvarchar](200) NOT NULL) ON [PRIMARY]";
			SQLHelper.KSLocalDB.RunSqlText(text);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}
}
