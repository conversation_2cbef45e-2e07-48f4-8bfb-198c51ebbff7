using System;
using System.Collections.Generic;
using System.Data;
using API.Common;
using Microsoft.Data.SqlClient;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace API.DataAccess.System;

public class SysRole
{
	public static DataTable GetSysRoleList(string search, string enable, int limit = 0, int offset = 0, string prop = "", string order = "")
	{
		string text = " SELECT T1.Fid, T1.FName, T1.FDescribe, T1.FEnable, T1.FSort " + SQLHelper.total + " FROM TSysRole T1 WHERE 1=1";
		if (search != "")
		{
			text = text + " AND ( T1.FName LIKE '%" + search + "%' )";
		}
		if (enable != "")
		{
			text = text + " AND T1.FEnable=" + enable;
		}
		if (prop == "")
		{
			prop = " FSort";
			order = "ASC";
		}
		return SQLHelper.LocalDB.RunSqlDt(text, limit, offset, prop, order);
	}

	public static void EnableSysRole(JObject jObject, string userName, int userId, string curTime)
	{
		DataCURD.Save(jObject, "TSysRole", "启用禁用角色", "Fid", userId, userName, curTime, SQLHelper.LocalDB.InitCnn());
	}

	public static void SaveSysRole(JObject jObject, string userName, int userId, string curTime)
	{
		string jObject2 = Util.GetJObject(jObject, "Fid");
		string jObject3 = Util.GetJObject(jObject, "FName");
		if (jObject3 == "")
		{
			throw new Exception("请输入角色名称！");
		}
		string sSql = " SELECT 1 FROM TSysRole WHERE FName='" + jObject3 + "' AND Fid!=" + jObject2;
		string text = SQLHelper.LocalDB.RunSqlStr(sSql);
		if (text == "1")
		{
			throw new Exception("角色名称存在重复！");
		}
		DataCURD.Save(jObject, "TSysRole", "启用禁用角色", "Fid", userId, userName, curTime, SQLHelper.LocalDB.InitCnn());
	}

	public static JObject GetSysMenuTreeByRoleId(string roleId)
	{
		JObject jObject = new JObject();
		JArray jArray = new JArray();
		JArray jArray2 = new JArray();
		string text = "SELECT T1.Fid,T1.FTitle,T1.FParentId,T1.FSort,T1.FEnable,T2.FMenuId AS checked FROM TSysMenu T1 LEFT JOIN ";
		text = text + " (SELECT DISTINCT B1.FMenuId FROM TSysRoleMenu B1 WHERE (SELECT COUNT(*) FROM TSysMenu WHERE FParentId=B1.FMenuId)=0 AND B1.FRoleId=" + roleId + ") T2 ON T2.FMenuId= T1.Fid";
		text += " WHERE T1.FEnable=1 ORDER BY T1.FParentId,T1.FSort";
		DataTable dataTable = SQLHelper.LocalDB.RunSqlDt(text);
		DataRow[] array = dataTable.Select(" FEnable=1 AND FParentId=0", "FSort ASC");
		for (int i = 0; i < array.Length; i++)
		{
			string text2 = array[i]["Fid"].ToString() ?? "";
			string text3 = array[i]["FTitle"].ToString() ?? "";
			string text4 = array[i]["checked"].ToString() ?? "";
			if (text4 != "")
			{
				jArray2.Add(text4);
			}
			JObject item = new JObject
			{
				["id"] = text2,
				["label"] = text3,
				["children"] = GetSysMenuTreeChild(text2, dataTable, jArray2)
			};
			jArray.Add(item);
		}
		jObject["List"] = jArray;
		jObject["checked"] = jArray2;
		return jObject;
	}

	public static JArray GetSysMenuTreeChild(string parentId, DataTable dtAll, JArray jArrayChecked)
	{
		JArray jArray = new JArray();
		DataRow[] array = dtAll.Select(" FEnable=1 AND FParentId=" + parentId, "FSort ASC");
		for (int i = 0; i < array.Length; i++)
		{
			string text = array[i]["Fid"].ToString() ?? "";
			string text2 = array[i]["FTitle"].ToString() ?? "";
			string text3 = array[i]["checked"].ToString() ?? "";
			if (text3 != "")
			{
				jArrayChecked.Add(text3);
			}
			JObject item = new JObject
			{
				["id"] = text,
				["label"] = text2,
				["children"] = GetSysMenuTreeChild(text, dtAll, jArrayChecked)
			};
			jArray.Add(item);
		}
		return jArray;
	}

	public static void SaveRoleMenu(string roleId, string menus, string userName, int userId, string curTime, SqlCommand? pCmd = null)
	{
		SqlConnection sqlConnection = null;
		try
		{
			if (pCmd == null)
			{
				sqlConnection = SQLHelper.LocalDB.InitCnn();
				pCmd = sqlConnection.CreateCommand();
				pCmd.Transaction = sqlConnection.BeginTransaction();
			}
			string sSql = " SELECT * FROM TSysRoleMenu WHERE FRoleId=" + roleId;
			DataTable o = SQLHelper.RunSqlDt(sSql, pCmd);
			sSql = " DELETE TSysRoleMenu WHERE FRoleId=" + roleId;
			SQLHelper.RunSqlText(sSql, pCmd);
			string content = JsonConvert.SerializeObject(new JObject
			{
				["type"] = "删除",
				["data"] = JArray.FromObject(o)
			});
			DataCURD.WriteLog("TSysRoleMenu", "删除角色与权限对照", "FRoleId", roleId, content, userId, userName, curTime, pCmd);
			if (menus == "")
			{
				menus = "0";
			}
			sSql = " INSERT TSysRoleMenu (FRoleId,FMenuId) SELECT " + roleId + ",Fid FROM TSysMenu WHERE Fid IN (" + menus + ")";
			sSql = sSql + " SELECT * FROM TSysRoleMenu WHERE FRoleId=" + roleId;
			DataTable o2 = SQLHelper.RunSqlDt(sSql, pCmd);
			content = JsonConvert.SerializeObject(new JObject
			{
				["type"] = "新增",
				["data"] = JArray.FromObject(o2)
			});
			DataCURD.WriteLog("TSysRoleMenu", "新增角色与权限对照", "FRoleId", roleId, content, userId, userName, curTime, pCmd);
			if (sqlConnection != null)
			{
				pCmd.Transaction.Commit();
			}
		}
		catch (Exception ex)
		{
			if (sqlConnection != null)
			{
				pCmd?.Transaction.Rollback();
			}
			throw new Exception(ex.Message.ToString());
		}
		finally
		{
			if (sqlConnection != null)
			{
				sqlConnection.Close();
				sqlConnection.Dispose();
			}
		}
	}

	public static void DelSysRole(string roleId, string userName, int userId, string curTime, SqlCommand? pCmd = null)
	{
		SqlConnection sqlConnection = null;
		try
		{
			if (pCmd == null)
			{
				sqlConnection = SQLHelper.LocalDB.InitCnn();
				pCmd = sqlConnection.CreateCommand();
				pCmd.Transaction = sqlConnection.BeginTransaction();
			}
			if (roleId == "1")
			{
				throw new Exception("无法删除此角色！");
			}
			DataCURD.Delete("TSysRole", "删除角色", "Fid", roleId, userId, userName, curTime, pCmd);
			DataCURD.Delete("TSysRoleMenu", "删除角色与权限对照", "FRoleId", roleId, userId, userName, curTime, pCmd);
			DataCURD.Delete("TSysRoleUser", "删除角色与人员对照", "FRoleId", roleId, userId, userName, curTime, pCmd);
			if (sqlConnection != null)
			{
				pCmd.Transaction.Commit();
			}
		}
		catch (Exception ex)
		{
			if (sqlConnection != null)
			{
				pCmd?.Transaction.Rollback();
			}
			throw new Exception(ex.Message.ToString());
		}
		finally
		{
			if (sqlConnection != null)
			{
				sqlConnection.Close();
				sqlConnection.Dispose();
			}
		}
	}

	public static JObject GetSysRoleUserList(string roleId)
	{
		JObject jObject = new JObject();
		List<object> list = new List<object>();
		string sSql = "SELECT * FROM TSysUser WHERE FEnable=1";
		DataTable dataTable = SQLHelper.LocalDB.RunSqlDt(sSql);
		DataTable dataTable2 = SQLHelper.LocalDB.RunSqlDt("SELECT FUserId FROM TSysRoleUser WHERE FRoleId=" + roleId);
		if (dataTable != null && dataTable.Rows.Count > 0)
		{
			for (int i = 0; i < dataTable.Rows.Count; i++)
			{
				list.Add(new
				{
					key = dataTable.Rows[i]["Fid"].ToString(),
					label = dataTable.Rows[i]["FUserName"].ToString(),
					disabled = false
				});
			}
		}
		string[] array = new string[dataTable2.Rows.Count];
		if (dataTable2 != null && dataTable2.Rows.Count > 0)
		{
			for (int j = 0; j < dataTable2.Rows.Count; j++)
			{
				array[j] = (dataTable2.Rows[j]["FUserId"].ToString() ?? "").Trim();
			}
		}
		jObject["List"] = JArray.FromObject(list);
		jObject["Value"] = JArray.FromObject(array);
		return jObject;
	}

	public static void SaveSysRoleUser(string roleId, string users, string userName, int userId, string curTime, SqlCommand? pCmd = null)
	{
		SqlConnection sqlConnection = null;
		try
		{
			if (pCmd == null)
			{
				sqlConnection = SQLHelper.LocalDB.InitCnn();
				pCmd = sqlConnection.CreateCommand();
				pCmd.Transaction = sqlConnection.BeginTransaction();
			}
			string sSql = " SELECT * FROM TSysRoleUser WHERE FRoleId=" + roleId;
			DataTable o = SQLHelper.RunSqlDt(sSql, pCmd);
			string content = JsonConvert.SerializeObject(new JObject
			{
				["type"] = "删除",
				["data"] = JArray.FromObject(o)
			});
			DataCURD.WriteLog("TSysRoleUser", "删除角色人员对照", "FRoleId", roleId, content, userId, userName, curTime, pCmd);
			sSql = " DELETE TSysRoleUser WHERE FRoleId=" + roleId;
			SQLHelper.RunSqlText(sSql, pCmd);
			if (users == "")
			{
				users = "0";
			}
			sSql = " INSERT TSysRoleUser (FRoleId,FUserId) SELECT " + roleId + ",Fid FROM TSysUser WHERE Fid IN (" + users + ")";
			sSql = sSql + " SELECT * FROM TSysRoleUser WHERE FRoleId=" + roleId;
			DataTable o2 = SQLHelper.RunSqlDt(sSql, pCmd);
			content = JsonConvert.SerializeObject(new JObject
			{
				["type"] = "新增",
				["data"] = JArray.FromObject(o2)
			});
			DataCURD.WriteLog("TSysRoleUser", "新增角色人员对照", "FRoleId", roleId, content, userId, userName, curTime, pCmd);
			if (sqlConnection != null)
			{
				pCmd.Transaction.Commit();
			}
		}
		catch (Exception ex)
		{
			if (sqlConnection != null)
			{
				pCmd?.Transaction.Rollback();
			}
			throw new Exception(ex.Message.ToString());
		}
		finally
		{
			if (sqlConnection != null)
			{
				sqlConnection.Close();
				sqlConnection.Dispose();
			}
		}
	}
}
