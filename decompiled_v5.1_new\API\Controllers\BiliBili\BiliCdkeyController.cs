using System;
using System.Data;
using API.BusService.System;
using API.Common;
using API.DataAccess.BiliBili;
using API.Models.Comm;
using Microsoft.AspNetCore.Mvc;

namespace API.Controllers.BiliBili;

public class BiliCdkeyController : Controller
{
	[HttpPost]
	public Response GetCdkeyList([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			int id = BusSysUser.Instance.User.Id;
			string jObject = Util.GetJObject(model.jObjectSearch, "areaId");
			string jObject2 = Util.GetJObject(model.jObjectSearch, "cookieId");
			string jObject3 = Util.GetJObject(model.jObjectSearch, "activityId");
			string jObject4 = Util.GetJObject(model.jObjectSearch, "search");
			string jObject5 = Util.GetJObject(model.jObjectSearch, "status");
			string jObject6 = Util.GetJObject(model.jObjectSearch, "type");
			string taskId = string.Join(',', Util.GetJObject<string[]>(model.jObjectSearch, "taskId") ?? Array.Empty<string>());
			string jObject7 = Util.GetJObject(model.jObjectSearch, "num");
			DataTable cdkeyList = BiliCdkey.GetCdkeyList(jObject5, jObject, jObject2, jObject3, jObject4, jObject6, taskId, jObject7, id, model.limit, model.offset, model.prop, model.order);
			response.data = Util.GetTableResponse(cdkeyList);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response SaveCdkey([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			BiliCdkey.SaveCdkey(model.jObjectParam, BusSysUser.Instance.User.Id);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response ExportCdkeyList([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			int id = BusSysUser.Instance.User.Id;
			string jObject = Util.GetJObject(model.jObjectSearch, "id");
			string jObject2 = Util.GetJObject(model.jObjectSearch, "type");
			string jObject3 = Util.GetJObject(model.jObjectSearch, "areaId");
			response.data = BiliCdkey.ExportCdkeyList(jObject, jObject2, jObject3, BusSysUser.Instance.User.Id);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}
}
