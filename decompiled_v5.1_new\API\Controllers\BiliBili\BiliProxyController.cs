using System;
using System.Data;
using System.Threading.Tasks;
using API.BusService.System;
using API.Common;
using API.DataAccess.BiliBili;
using API.Models.Comm;
using Microsoft.AspNetCore.Mvc;

namespace API.Controllers.BiliBili;

public class BiliProxyController : Controller
{
	[HttpPost]
	public Response ExportProxy([FromBody] Request model, User user)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "groupId");
			response.data = BiliProxy.ExportProxy(jObject, user);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public async Task<Response> ImportProxy([FromBody] Request model, User user)
	{
		Response mRet = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "src");
			string jObject2 = Util.GetJObject(model.jObjectParam, "groupId");
			await BiliProxy.ImportProxy(jObject, jObject2, user, model.curTime);
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}

	[HttpPost]
	public Response DelGroup([FromBody] Request model, User user)
	{
		Response response = new Response();
		try
		{
			string selected = ("," + Util.GetJObject(model.jObjectParam, "selected") + ",").Replace(",0,", ",-1,").Trim(',');
			BiliProxy.DelGroup(selected, user.Id, user.Name, model.curTime);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response SaveGroup([FromBody] Request model, User user)
	{
		Response response = new Response();
		try
		{
			response.data = BiliProxy.SaveGroup(model.jObjectParam, user, model.curTime);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response GetProxyGroupList(User user)
	{
		Response response = new Response();
		try
		{
			response.data = BiliProxy.GetProxyGroupList(user.Id);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response GetProxyList([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string groupId = Util.GetJObject(model.jObjectSearch, "groupId", "0").Trim(',');
			string jObject = Util.GetJObject(model.jObjectSearch, "search");
			string jObject2 = Util.GetJObject(model.jObjectSearch, "enable");
			DataTable proxyList = BiliProxy.GetProxyList("", groupId, jObject, jObject2, BusSysUser.Instance.User.Id, model.limit, model.offset, model.prop, model.order);
			response.data = Util.GetTableResponse(proxyList);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public async Task<Response> SaveProxy([FromBody] Request model, User user)
	{
		Response mRet = new Response();
		try
		{
			await BiliProxy.SaveProxy(model.jObjectParam, user, model.curTime);
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}

	[HttpPost]
	public Response DelProxy([FromBody] Request model, User user)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "Fid");
			DataCURD.Delete("TProxy", "删除代理", "Fid", jObject, user.Id, user.Name, model.curTime, SQLHelper.BiliLocalDB.InitCnn());
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public async Task<Response> TestProxy([FromBody] Request model, User user)
	{
		Response mRet = new Response();
		try
		{
			await BiliProxy.SaveProxy(model.jObjectParam, user, model.curTime);
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}

	[HttpPost]
	public async Task<Response> Share([FromBody] Request model, User user)
	{
		Response mRet = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "groupId");
			string jObject2 = Util.GetJObject(model.jObjectParam, "name");
			await BiliProxy.ShareProxy(jObject, jObject2, user.Id);
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}

	[HttpPost]
	public async Task<Response> Cancel(User user)
	{
		Response mRet = new Response();
		try
		{
			await BiliProxy.Cancel(user.Id);
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}
}
