using System;
using System.Diagnostics;
using System.IO;
using System.Threading.Tasks;
using API.BusService.System;
using API.Common;
using API.DataAccess.BiliBili;
using API.Models.Comm;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;

namespace API.Controllers.BiliBili;

public class BiliLiveController : Controller
{
	[HttpPost]
	public Response OpenFolder([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "wwwRootPath");
			jObject = Directory.GetCurrentDirectory() + "\\wwwroot\\files\\" + jObject;
			if (!Directory.Exists(jObject))
			{
				Directory.CreateDirectory(jObject);
			}
			ProcessStartInfo startInfo = new ProcessStartInfo
			{
				Arguments = jObject,
				FileName = "explorer.exe"
			};
			Process.Start(startInfo);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response DelLive([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "url");
			Util.FileDelete(Directory.GetCurrentDirectory() + "\\WWWRoot\\" + jObject);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response SaveLive([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			BiliLive.SaveLive(model.jObjectParam, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response GetLiveConfig([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "cookieId");
			string jObject2 = Util.GetJObject(model.jObjectParam, "areaId");
			response.data = BiliLive.GetLiveConfig(jObject, jObject2, BusSysUser.Instance.User.Id);
			response.message = Directory.GetCurrentDirectory() + "\\Util\\FFmpeg\\ffmpeg.exe -i \"输入路径\" -c:v libx264 -crf 20 -c:a aac -strict experimental -b:a 192k \"输出路径\"";
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response SaveLiveRtmp([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			BiliLive.SaveLiveRtmp(model.jObjectParam, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public async Task<Response> BulletScreen([FromBody] Request model)
	{
		Response mRet = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "roomId");
			string jObject2 = Util.GetJObject(model.jObjectParam, "msg");
			JObject jObjectParam = new JObject
			{
				["userId"] = BusSysUser.Instance.User.Id,
				["msg"] = jObject2,
				["roomId"] = jObject
			};
			Request model2 = new Request
			{
				jObjectParam = jObjectParam
			};
			Response response = mRet;
			response.data = await Util.Request("/Common/BulletScreen", model2);
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}

	[HttpPost]
	public async Task<Response> WatchLive([FromBody] Request model)
	{
		Response mRet = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "roomId");
			string jObject2 = Util.GetJObject(model.jObjectParam, "areaId");
			int jObject3 = Util.GetJObject<int>(model.jObjectParam, "watchMinutes");
			if (jObject3 > 20)
			{
				throw new ArgumentException("观看时间不能超过 20 分钟！");
			}
			JObject jObjectParam = new JObject
			{
				["userId"] = BusSysUser.Instance.User.Id,
				["watchMinutes"] = jObject3,
				["roomId"] = jObject,
				["areaId"] = jObject2
			};
			Request model2 = new Request
			{
				jObjectParam = jObjectParam
			};
			Response response = mRet;
			response.data = await Util.Request("/Common/Watch", model2);
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}

	[HttpPost]
	public async Task<Response> LiveStatus([FromBody] Request model)
	{
		Response mRet = new Response();
		try
		{
			string[] roomId = Util.GetJObject(model.jObjectSearch, "roomId").Split(',');
			HttpClientFactory httpClientFactory = new HttpClientFactory();
			for (int i = 0; i < roomId.Length; i++)
			{
				string roomNo = roomId[i].Trim();
				if (roomNo != "")
				{
					JObject jObject = await httpClientFactory.Get("https://api.live.bilibili.com/xlive/web-room/v1/index/getRoomBaseInfo?room_ids=" + roomNo + "&req_biz=video");
					if (Util.GetJObject(jObject, "code") == "0")
					{
						JToken jObject2 = Util.GetJObject<JToken>(jObject["data"], "by_room_ids");
						JToken jObject3 = Util.GetJObject<JToken>(jObject2, roomNo);
						string jObject4 = Util.GetJObject(jObject3, "uname");
						string jObject5 = Util.GetJObject(jObject3, "title");
						string jObject6 = Util.GetJObject(jObject3, "area_name");
						string text = ((Util.GetJObject(jObject3, "live_status") == "1") ? "开播" : "关播");
						Util.WriteLog("BiliBili", jObject4, "直播状态", "房间号：" + roomNo + " → " + text + " | " + jObject6 + " | " + jObject5);
					}
					else
					{
						Util.WriteLog("BiliBili", roomNo, "直播状态", Util.GetJObject(jObject, "message"), ConsoleColor.Red);
					}
				}
			}
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}
}
