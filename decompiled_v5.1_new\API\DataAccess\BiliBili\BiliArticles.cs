using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using API.BusService.BiliBili;
using API.BusService.System;
using API.Common;
using API.Models.Comm;
using API.Quartz;
using NPOI.Util;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Quartz;

namespace API.DataAccess.BiliBili;

public class BiliArticles
{
	public static async Task UploadAppeal(string cookieId, string src, int userId)
	{
		string filepath = Directory.GetCurrentDirectory() + "\\WWWRoot\\" + src;
		int j = 0;
		DataTable dtAppeal = ExcelHelper.GetDataTable(filepath);
		if (dtAppeal.Rows.Count == 0)
		{
			Util.WriteLog("BiliBili", "稿件管理", "稿件管理", "申诉内容不足！", ConsoleColor.Red);
			return;
		}
		string sSql = " SELECT * FROM TArticles WHERE FCookieId IN (" + cookieId + ")";
		DataTable dtArticles = SQLHelper.BiliLocalDB.RunSqlDt(sSql);
		for (int i = 0; i < dtArticles.Rows.Count; i++)
		{
			string jObject = Util.GetJObject(dtArticles.Rows[i], "FContent");
			string articleId = Util.GetJObject(dtArticles.Rows[i], "Fid");
			if (!(jObject != ""))
			{
				continue;
			}
			JArray jArray = JArray.Parse(jObject);
			foreach (JToken item in jArray)
			{
				string jObject2 = Util.GetJObject(item, "state_desc");
				if (!(jObject2 == "已锁定"))
				{
					continue;
				}
				string aid = Util.GetJObject(item, "aid");
				DataTable cookiesList = BiliCookies.GetCookiesList(Util.GetJObject(dtArticles.Rows[i], "FCookieId"), "", userId);
				if (cookiesList.Rows.Count == 1)
				{
					DataRow dataRow = cookiesList.Rows[0];
					if (dataRow["Fid"].ToString() == null)
					{
					}
					string name = dataRow["FKey"].ToString() ?? "";
					string name2 = dataRow["FName"].ToString() ?? "";
					string csrf = dataRow["FCsrf"].ToString() ?? "";
					string cookie = dataRow["FCookie"].ToString() ?? "";
					string header = dataRow["FHeaders"].ToString() ?? "";
					string text = dataRow["FProxyAddress"].ToString() ?? "";
					string userName = dataRow["FProxyUserName"].ToString() ?? "";
					string password = dataRow["FProxyPassword"].ToString() ?? "";
					Util.WriteLog("BiliBili", name2, "稿件管理", "正在申诉" + aid + "！");
					HttpClientHandler defaultHandler = null;
					if (text != "")
					{
						defaultHandler = new HttpClientHandler
						{
							Proxy = new WebProxy
							{
								Address = new Uri(text),
								Credentials = new NetworkCredential(userName, password)
							}
						};
					}
					Dictionary<string, string> dictionary = HttpClientFactory.FormataHeader(header, cookie);
					dictionary.Remove("Accept-Language");
					HttpClientFactory httpClientFactory = new HttpClientFactory(name, dictionary, defaultHandler);
					await httpClientFactory.Get("https://member.bilibili.com/x/web/appeal/v2/permit?aid=" + aid);
					if (j == dtAppeal.Rows.Count)
					{
						j = 0;
					}
					string desc = Util.GetJObject(dtAppeal.Rows[j], "申述内容");
					string tel = Util.GetJObject(dtAppeal.Rows[j], "电话号码");
					string email = Util.GetJObject(dtAppeal.Rows[j], "邮箱地址");
					j++;
					await Task.Delay(1000);
					string text2 = "aid=" + aid + "&content=" + HttpUtility.UrlEncode(desc) + "&email=" + email + "&phone=" + tel + "&pics=&csrf=" + csrf;
					JObject jObject3 = await httpClientFactory.Post("https://member.bilibili.com/x/web/appeal/v2/add", text2);
					if (Util.GetJObject(jObject3, "code") == "0")
					{
						Util.WriteLog("BiliBili", name2, "稿件管理", "申述成功，回执号：" + Util.GetJObject(jObject3["data"], "appeal_id") + "！", ConsoleColor.Green);
						sSql = " UPDATE TArticles SET FAppeal='正在申诉' WHERE Fid=" + articleId;
						SQLHelper.BiliLocalDB.RunSqlText(sSql);
					}
					else
					{
						Util.WriteLog("BiliBili", name2, "稿件管理", Util.GetJObject(jObject3, "message"), ConsoleColor.Red);
					}
				}
				break;
			}
		}
	}

	public static JObject GetTitleVideo(string areaId, string areaName, int userId)
	{
		string sSql = " SELECT COUNT(*) FROM TArticlesTitle WHERE FUserId=" + userId + " AND FAreaId=" + areaId;
		string text = SQLHelper.BiliLocalDB.RunSqlStr(sSql);
		string path = Directory.GetCurrentDirectory() + "\\WWWRoot\\files\\Bili\\Articles\\" + areaName;
		if (!Directory.Exists(path))
		{
			Directory.CreateDirectory(path);
		}
		DirectoryInfo directoryInfo = new DirectoryInfo(path);
		FileInfo[] files = directoryInfo.GetFiles("*.mp4");
		return new JObject
		{
			["titleNum"] = text,
			["videoNum"] = files.Length
		};
	}

	public static void DelTitle(string id, string areaId, User user, string curTime)
	{
		string sSql = " UPDATE TArticlesTitle SET FDate='" + curTime + "' WHERE Fid IN (" + id + ") AND FAreaId=" + areaId + " AND FUserId=" + user.Id;
		SQLHelper.BiliLocalDB.RunSqlText(sSql);
		DataCURD.Delete("TArticlesTitle", "删除", "FDate", curTime, user.Id, user.Name, curTime, SQLHelper.BiliLocalDB.InitCnn());
	}

	public static DataTable GetArticlesTitleList(string search, string areaId, int userId, int limit, int offset, string prop, string order)
	{
		string text = " DELETE TArticlesTitle WHERE LEN(FCookieId)>3500";
		text = text + " SELECT T2.FName AS FAreaName,T1.*, LEN(T1.FCookieId)-LEN(REPLACE(T1.FCookieId,',',''))-1 AS FTimes" + SQLHelper.total + " FROM TArticlesTitle T1";
		text += " LEFT JOIN TArea T2 ON T2.Fid=T1.FAreaId";
		text = text + " WHERE T1.FUserId=" + userId + " AND T1.FAreaId=" + areaId;
		if (search != "")
		{
			text = text + " AND T1.FContent LIKE '%" + search + "%'";
		}
		return SQLHelper.BiliLocalDB.RunSqlDt(text, limit, offset, prop, order);
	}

	public static JArray GetCookieArticlesInfo(string ids, string title, string areaId, string areaName, int userId)
	{
		string path = Directory.GetCurrentDirectory() + "\\WWWRoot\\files\\Bili\\Articles\\" + areaName;
		if (!Directory.Exists(path))
		{
			Directory.CreateDirectory(path);
		}
		DirectoryInfo directoryInfo = new DirectoryInfo(path);
		FileInfo[] files = directoryInfo.GetFiles("*.mp4");
		string text = " SELECT Fid AS FCookieId,FName AS FCookieName,17 AS FTid,1008 AS FType,'' AS FUrl,'' AS FTitle,'' AS FTag,'' AS FCover";
		text = text + " FROM TCookies WHERE Fid IN (" + ids + ") AND FUserId=" + userId;
		DataTable dataTable = SQLHelper.BiliLocalDB.RunSqlDt(text);
		if (files.Length < dataTable.Rows.Count)
		{
			throw new Exception("视频数量不足！");
		}
		DataTable articlesTitleList = GetArticlesTitleList("", areaId, userId, 0, 0, "NEWID()", "");
		if (articlesTitleList.Rows.Count < dataTable.Rows.Count)
		{
			throw new Exception("标题数量不足！");
		}
		Dictionary<string, string> dictionary = new Dictionary<string, string>();
		string text2 = "0";
		for (int i = 0; i < dataTable.Rows.Count && i < files.Length; i++)
		{
			string text3 = dataTable.Rows[i]["FCookieId"].ToString() ?? "";
			dataTable.Rows[i]["FUrl"] = files[i].FullName.Replace(Directory.GetCurrentDirectory() + "\\WWWRoot\\", "");
			DataRow[] array = articlesTitleList.Select("FCookieId NOT LIKE '%," + text3 + ",%' AND Fid NOT IN (" + text2 + ")");
			if (array != null && array.Length != 0)
			{
				List<DataRow> list = new List<DataRow>();
				list.AddRange(array.OrderBy((DataRow row) => new Random().Next()));
				array = list.ToArray();
				string text4 = array[0]["Fid"]?.ToString() ?? "";
				if (text4 == "")
				{
					throw new Exception("发生了不可能出现的错误！");
				}
				dataTable.Rows[i]["FTitle"] = array[0]["FContent"];
				dictionary.Add(text3, text4);
				text2 = text2 + "," + text4;
				continue;
			}
			throw new Exception("标题数量不足！");
		}
		text = "";
		if (title == "0")
		{
			foreach (KeyValuePair<string, string> item in dictionary)
			{
				text = text + " UPDATE TArticlesTitle SET FCookieId=FCookieId+'" + item.Key + ",' WHERE Fid=" + item.Value;
			}
		}
		else
		{
			if (!(title == "1"))
			{
				throw new Exception("标题使用次数填错啦，赶紧看看！");
			}
			foreach (KeyValuePair<string, string> item2 in dictionary)
			{
				text = text + " Delete TArticlesTitle WHERE Fid=" + item2.Value;
			}
		}
		SQLHelper.BiliLocalDB.RunSqlText(text);
		return JArray.FromObject(dataTable);
	}

	public static void SplitVideo(string path, string areaId, int time)
	{
		try
		{
			string areaName = BiliArea.GetAreaName(areaId);
			string text = Directory.GetCurrentDirectory() + "\\WWWRoot\\" + path;
			Util.WriteLog("BiliBili", "拆分视频", "稿件管理", "正在处理视频！");
			ProcessStartInfo startInfo = new ProcessStartInfo
			{
				FileName = Directory.GetCurrentDirectory() + "\\Util\\FFmpeg\\ffprobe.exe",
				Arguments = "-v quiet -of json -i \"" + text + "\" -show_streams",
				UseShellExecute = false,
				CreateNoWindow = false,
				RedirectStandardError = true,
				RedirectStandardInput = true,
				RedirectStandardOutput = true
			};
			Process process = Process.Start(startInfo);
			string text2 = process?.StandardOutput.ReadToEnd();
			process?.Close();
			JToken jToken = null;
			if (text2 == null || text2 == "")
			{
				throw new Exception("读取视频内部信息错误！");
			}
			JArray jArray = (JArray)JObject.Parse(text2)["streams"];
			foreach (JToken item in jArray ?? new JArray())
			{
				if (item["codec_type"]?.ToString() == "video")
				{
					jToken = item;
				}
			}
			double jObject = Util.GetJObject<double>(jToken, "duration");
			FileInfo fileInfo = new FileInfo(text);
			long length = fileInfo.Length;
			if (jObject == 0.0)
			{
				throw new Exception("读取视频内部信息错误！");
			}
			int num = (int)(10380902.4 / ((double)length / jObject));
			if (time > 0)
			{
				num = time;
			}
			string text3 = Directory.GetCurrentDirectory() + "\\WWWRoot\\Files\\Bili\\Articles\\" + areaName + "\\" + Path.GetFileNameWithoutExtension(text) + "%03d.mp4";
			process = Util.ProcessStart("\\FFmpeg\\ffmpeg.exe", "-i \"" + text + "\" -f segment -segment_time " + num + " -reset_timestamps 1 \"" + text3 + "\" -y");
			while (process != null && !process.HasExited)
			{
				Thread.Sleep(1000);
			}
			Util.WriteLog("BiliBili", "拆分视频", "投稿", "视频拆分完成！");
			File.Delete(text);
		}
		catch (Exception ex)
		{
			Util.WriteLog("BiliBili", "拆分视频", "投稿", ex.Message, ConsoleColor.Red);
		}
	}

	public static JArray AutoVideo(int num, string path)
	{
		string path2 = Directory.GetCurrentDirectory() + "\\WWWRoot\\files\\" + path;
		if (!Directory.Exists(path2))
		{
			Directory.CreateDirectory(path2);
		}
		DirectoryInfo directoryInfo = new DirectoryInfo(path2);
		FileInfo[] files = directoryInfo.GetFiles("*.mp4");
		if (files.Length < num)
		{
			throw new Exception("视频数量不足！");
		}
		JArray jArray = new JArray();
		for (int i = 0; i < num; i++)
		{
			jArray.Add(new JObject
			{
				["url"] = files[i].FullName.Replace(Directory.GetCurrentDirectory() + "\\WWWRoot\\", ""),
				["name"] = files[i].Name.ToUpper().Replace(".MP4", "")
			});
		}
		return jArray;
	}

	public static JArray DoTxt(string src)
	{
		JArray jArray = new JArray();
		string path = Directory.GetCurrentDirectory() + "\\WWWRoot\\" + src;
		if (!File.Exists(path))
		{
			throw new Exception("文件不存在！");
		}
		using (StreamReader streamReader = new StreamReader(path))
		{
			string text;
			while ((text = streamReader.ReadLine()) != null && text != "")
			{
				jArray.Add(text.Replace("-", "").Replace(" ", "").Trim());
			}
		}
		if (File.Exists(path))
		{
			File.Delete(path);
		}
		return jArray;
	}

	public static void UploadTitle(string src, string areaId, User user)
	{
		JArray jArray = new JArray();
		string path = Directory.GetCurrentDirectory() + "\\WWWRoot\\" + src;
		if (!File.Exists(path))
		{
			throw new Exception("文件不存在！");
		}
		using (StreamReader streamReader = new StreamReader(path))
		{
			string text;
			while ((text = streamReader.ReadLine()) != null)
			{
				string text2 = text.Replace("-", "").Replace(" ", "").Replace("》", "")
					.Replace("《", "")
					.Replace("「", "")
					.Replace("」", "")
					.Replace("”", "")
					.Replace("“", "")
					.Replace("：", "")
					.Trim();
				text2 = text2.Replace("-", "").Replace(" ", "").Replace("》", "")
					.Replace("《", "")
					.Replace("「", "")
					.Replace("」", "")
					.Replace("”", "")
					.Replace("“", "")
					.Replace("：", "")
					.Trim();
				if (text2 != "")
				{
					jArray.Add(new JObject
					{
						["Fid"] = 0,
						["FUserId"] = user.Id,
						["FAreaId"] = areaId,
						["FCookieId"] = ",",
						["FContent"] = text2
					});
				}
			}
		}
		if (File.Exists(path))
		{
			File.Delete(path);
		}
		DataTable dataTable = jArray.ToObject<DataTable>();
		if (dataTable != null)
		{
			SQLHelper.BiliLocalDB.SqlBulkCopyByDataTable("TArticlesTitle", dataTable);
			string text3 = " DELETE  TArticlesTitle WHERE FContent IN (SELECT FContent FROM TArticlesTitle WHERE FUserId=" + user.Id + " AND FAreaId=" + areaId + " GROUP BY FContent HAVING COUNT(*)!=1)";
			text3 += " AND Fid NOT IN ( SELECT MIN(Fid) FROM  TArticlesTitle WHERE FContent IN";
			text3 = text3 + " (SELECT FContent FROM TArticlesTitle WHERE FUserId=" + user.Id + " AND FAreaId=" + areaId + " GROUP BY FContent HAVING COUNT(*)!=1) GROUP BY FContent)";
			SQLHelper.BiliLocalDB.RunSqlText(text3);
		}
	}

	public static JArray DoVideo(JArray jArray, string path)
	{
		string text = Directory.GetCurrentDirectory() + "\\WWWRoot\\files\\" + path;
		if (!Directory.Exists(text))
		{
			Directory.CreateDirectory(text);
		}
		foreach (JToken item in jArray)
		{
			string jObject = Util.GetJObject(item, "url");
			string jObject2 = Util.GetJObject(item, "name");
			string jObject3 = Util.GetJObject(item, "coverUrl");
			item["name"] = ((jObject2.Split('#')[0] == "") ? jObject2 : jObject2.Split('#')[0]);
			string text2 = text + "\\" + Guid.NewGuid().ToString() + ".mp4";
			using HttpClient httpClient = new HttpClient();
			using HttpResponseMessage httpResponseMessage = httpClient.GetAsync(jObject).Result;
			using Stream stream = httpResponseMessage.Content.ReadAsStreamAsync().Result;
			using FileStream destination = new FileStream(text2, FileMode.OpenOrCreate, FileAccess.Write);
			stream.CopyTo(destination);
			item["url"] = text2.Replace(Directory.GetCurrentDirectory() + "\\WWWRoot\\", "");
			string text3 = text + "\\" + Guid.NewGuid().ToString() + ".jpg";
			using HttpClient httpClient2 = new HttpClient();
			using HttpResponseMessage httpResponseMessage2 = httpClient2.GetAsync(jObject3).Result;
			using Stream stream2 = httpResponseMessage2.Content.ReadAsStreamAsync().Result;
			using FileStream destination2 = new FileStream(text3, FileMode.OpenOrCreate, FileAccess.Write);
			stream2.CopyTo(destination2);
			item["cover"] = text3;
		}
		return jArray;
	}

	public static async Task Submit(JArray jArray, string areaId, int time, int num, int userId, string jobName = "稿件管理", int seconds = 0, IJobExecutionContext? context = null)
	{
		try
		{
			Util.ProcessExist("\\Geetest\\app.exe");
			List<Task> tasks = new List<Task>();
			DataTable areaList = BiliArea.GetAreaList(areaId, BusSysUser.Instance.User.Organization.BiliBili);
			string articlesMissionId = Util.GetJObject(areaList.Rows[0], "FArticlesMissionId");
			string articlesTag = Util.GetJObject(areaList.Rows[0], "FArticlesTag");
			DataTable cookiesList = BiliCookies.GetCookiesList("", await QzUtil.Validate(userId, BusSysUser.Instance.User.Address, "bilibili"), userId, "1", areaId);
			DataRow[] array = cookiesList.Select("FAricles=1");
			if (array != null && array.Length != 0)
			{
				cookiesList = array.CopyToDataTable();
				DataTable dataTable = jArray.ToObject<DataTable>();
				if (dataTable == null || dataTable.Rows.Count == 0)
				{
					throw new Exception("至少一个账号添加视频！");
				}
				var source = from T1 in cookiesList.AsEnumerable()
					join T2 in dataTable.AsEnumerable() on T1.Field<object>("Fid")?.ToString() equals T2.Field<object>("FCookieId")?.ToString()
					select new
					{
						Fid = T1.Field<object>("Fid"),
						FKey = T1.Field<object>("FKey"),
						FName = T1.Field<object>("FName"),
						FRoomId = T1.Field<object>("FRoomId"),
						FIdentifying = T1.Field<object>("FIdentifying"),
						FCsrf = T1.Field<object>("FCsrf"),
						FCookie = T1.Field<object>("FCookie"),
						FHeaders = T1.Field<object>("FHeaders"),
						FCountryId = T1.Field<object>("FCountryId"),
						FTel = T1.Field<object>("FTel"),
						FSMSUrl = T1.Field<object>("FSMSUrl"),
						FProxyId = T1.Field<object>("FProxyId"),
						FProxyAddress = T1.Field<object>("FProxyAddress"),
						FProxyUserName = T1.Field<object>("FProxyUserName"),
						FProxyPassword = T1.Field<object>("FProxyPassword"),
						FRoomTitle = T1.Field<object>("FRoomTitle"),
						FRoomRTMP = T1.Field<object>("FRoomRTMP"),
						FBrowserStatus = T1.Field<object>("FBrowserStatus"),
						FMileage = T1.Field<object>("FMileage"),
						FAricles = T1.Field<object>("FAricles"),
						FCookieId = T2?.Field<object>("FCookieId"),
						FCookieName = T2?.Field<object>("FCookieName"),
						FTid = T2?.Field<object>("FTid"),
						FType = T2?.Field<object>("FType"),
						FUrl = T2?.Field<object>("FUrl"),
						FTitle = T2?.Field<object>("FTitle"),
						FTag = T2?.Field<object>("FTag"),
						FCover = T2?.Field<object>("FCover")
					};
				jArray = JArray.FromObject(source.ToList());
				for (int i = 0; i < jArray.Count; i++)
				{
					if (!QzUtil.IsExecute(seconds, context))
					{
						break;
					}
					_ = i;
					JToken jToken = jArray[i];
					string name = Util.GetJObject(jToken, "FName");
					Util.WriteLog("BiliBili", name, jobName, "正在投稿！");
					if (time >= 0)
					{
						await Task.Run(() => Submit4(jToken, articlesMissionId, articlesTag, jobName));
						if (time > 0 && i < jArray.Count - 1 && QzUtil.IsExecute(seconds, context))
						{
							Util.WriteLog("BiliBili", name, jobName, "等待" + time + "秒后继续投下一个！");
							await Task.Delay(time * 1000);
						}
						continue;
					}
					tasks.Add(Task.Run(() => Submit4(jToken, articlesMissionId, articlesTag, jobName)));
					await Task.Delay(500);
					while (num > 0 && tasks.Count >= num && QzUtil.IsExecute(seconds, context))
					{
						await Task.Delay(1000);
						tasks.RemoveAll((Task t) => t.IsCompleted);
					}
				}
				return;
			}
			throw new Exception("暂无有权限的账号！");
		}
		catch (Exception ex)
		{
			Util.WriteLog("BiliBili", "错误信息", jobName, ex.Message, ConsoleColor.Red);
		}
	}

	public static async Task Submit4(JToken jToken, string articlesMissionId, string articlesTag, string jobName = "稿件管理")
	{
		List<string> list = new List<string>();
		string name = Util.GetJObject(jToken, "FName");
		string identifying = Util.GetJObject(jToken, "FIdentifying");
		string key = Util.GetJObject(jToken, "FKey");
		string jObject = Util.GetJObject(jToken, "FCookie");
		string jObject2 = Util.GetJObject(jToken, "FHeaders");
		int tid = Util.GetJObject<int>(jToken, "FTid");
		int type = Util.GetJObject<int>(jToken, "FType");
		string title = Util.GetJObject(jToken, "FTitle");
		string tag = Util.GetJObject(jToken, "FTag");
		string desc = Util.GetJObject(jToken, "FDesc");
		string jObject3 = Util.GetJObject(jToken, "FUrl");
		string proxyAddress = Util.GetJObject(jToken, "FProxyAddress");
		string proxyUserName = Util.GetJObject(jToken, "FProxyUserName");
		string proxyPassword = Util.GetJObject(jToken, "FProxyPassword");
		string jObject4 = Util.GetJObject(jToken, "FCover");
		try
		{
			string csrf = Util.GetCookieByKey(jObject, "bili_jct");
			HttpClientHandler defaultHandler = null;
			if (proxyAddress != "")
			{
				defaultHandler = new HttpClientHandler
				{
					Proxy = new WebProxy
					{
						Address = new Uri(proxyAddress),
						Credentials = new NetworkCredential(proxyUserName, proxyPassword)
					}
				};
			}
			Dictionary<string, string> dic = HttpClientFactory.FormataHeader(jObject2, jObject);
			dic.Remove("Accept-Language");
			dic.TryAdd("referer", "https://member.bilibili.com/platform/upload/video/frame?page_from=creative_home_top_upload");
			string videoPath = Directory.GetCurrentDirectory() + "\\WWWRoot\\" + jObject3;
			list.Add(videoPath);
			string imagePath = ((jObject4 != "") ? jObject4 : (Path.GetDirectoryName(videoPath) + "\\" + Guid.NewGuid().ToString() + ".jpeg"));
			Util.WriteLog("BiliBili", name, jobName, "正在读取视频内部信息！");
			ProcessStartInfo startInfo = new ProcessStartInfo
			{
				FileName = Directory.GetCurrentDirectory() + "\\Util\\FFmpeg\\ffprobe.exe",
				Arguments = "-v quiet -of json -i \"" + videoPath + "\" -show_streams",
				UseShellExecute = false,
				CreateNoWindow = false,
				RedirectStandardError = true,
				RedirectStandardInput = true,
				RedirectStandardOutput = true
			};
			Process process = Process.Start(startInfo);
			string text = process?.StandardOutput.ReadToEnd();
			process?.Close();
			JToken jObjectVideo = null;
			JToken jObjectAudio = null;
			if (text == null || text == "")
			{
				throw new Exception("读取视频内部信息错误！");
			}
			JArray jArray = (JArray)JObject.Parse(text)["streams"];
			foreach (JToken item2 in jArray ?? new JArray())
			{
				if (item2["codec_type"]?.ToString() == "video")
				{
					jObjectVideo = item2;
				}
				else if (item2["codec_type"]?.ToString() == "audio")
				{
					jObjectAudio = item2;
				}
			}
			Util.WriteLog("BiliBili", name, jobName, "正在获取视频封面图！");
			if (!File.Exists(imagePath))
			{
				Process process2 = Util.ProcessStart("\\FFmpeg\\ffmpeg.exe", "-i \"" + videoPath + "\" -vf \"select = eq(n\\, 0)\" -q:v 2 \"" + imagePath + "\" -y", WindowStyle: false);
				for (int i = 0; i < 60; i++)
				{
					if (File.Exists(imagePath))
					{
						break;
					}
					await Task.Delay(1000);
				}
				Process process3 = process2;
				if (process3 != null && !process3.HasExited)
				{
					process2?.CloseMainWindow();
				}
				if (!File.Exists(imagePath))
				{
					throw new Exception("视频封面图获取失败！");
				}
			}
			FileInfo fileInfo = new FileInfo(videoPath);
			long fileSize = fileInfo.Length;
			if ((double)(fileSize / 1024 / 1024) > 9.9)
			{
				Util.WriteLog("BiliBili", name, jobName, "正在压缩视频！");
				string videoPath2 = Path.GetDirectoryName(videoPath) + "\\" + Guid.NewGuid().ToString() + ".mp4";
				list.Add(videoPath2);
				Process process2 = Util.ProcessStart("\\FFmpeg\\ffmpeg.exe", "-i \"" + videoPath + "\" -fs 9M \"" + videoPath2 + "\" -y", WindowStyle: false);
				int i = 0;
				while (process2 != null && !process2.HasExited)
				{
					await Task.Delay(3000);
					i++;
					if (i > 120)
					{
						process2.CloseMainWindow();
						throw new Exception("转码时间过长！");
					}
				}
				if (!File.Exists(videoPath2))
				{
					throw new Exception("压缩失败！");
				}
				videoPath = videoPath2;
				fileInfo = new FileInfo(videoPath);
				fileSize = fileInfo.Length;
			}
			string fileName = Uri.EscapeDataString(fileInfo.Name);
			string content = Uri.EscapeDataString(fileInfo.Name.Split('.')[0]);
			string upload_id = identifying + "_" + DateTimeOffset.UtcNow.ToUnixTimeMilliseconds() + "_" + new Random().Next(1000, 9999);
			string track_id = identifying + "_" + DateTimeOffset.UtcNow.ToUnixTimeMilliseconds() + "_" + new Random().Next(10000, 99999);
			HttpClientFactory httpClientFactory = new HttpClientFactory(key, dic, defaultHandler);
			Dictionary<string, string> dictionary = dic.Copy();
			dictionary.Remove("Cookie");
			HttpClientFactory httpClientFactory2 = new HttpClientFactory(key, dic, defaultHandler);
			JObject jObject5 = new JObject();
			new JObject();
			string from_topic_id = "";
			string filename = "";
			string cover_url = "";
			for (int i = 0; i < 3; i++)
			{
				try
				{
					jObject5 = await httpClientFactory.Get("https://member.bilibili.com/preupload?probe_version=20221109&upcdn=bldsa&zone=cs&name=" + fileName + "&r=upos&profile=ugcfx%2Fbup&ssl=0&version=2.14.0.0&build=2140000&size=" + fileSize + "&webVersion=2.14.0");
					if (Util.GetJObject(jObject5, "OK") != "1")
					{
						throw new Exception("1:" + JsonConvert.SerializeObject(jObject5));
					}
					long jObject6 = Util.GetJObject<long>(jObject5, "chunk_size");
					if (fileSize > jObject6)
					{
						throw new Exception("已超出上传文件大小（10M）！");
					}
					filename = new Uri(Util.GetJObject(jObject5, "upos_uri")).Segments[1].Split('.')[0];
					await httpClientFactory.Get("https://member.bilibili.com/x/vupre/web/archive/precheck?content=" + content + "&check_item=1&t=" + DateTimeOffset.UtcNow.ToUnixTimeMilliseconds());
					await httpClientFactory.Get("https://member.bilibili.com/x/vupre/web/archive/precheck?content=" + content + "&check_item=2&t=" + DateTimeOffset.UtcNow.ToUnixTimeMilliseconds());
					await httpClientFactory.Post("https://member.bilibili.com/x/vupre/web/archive/types/predict?t=" + DateTimeOffset.UtcNow.ToUnixTimeMilliseconds() + "&csrf=" + csrf, "title=" + content + "&upload_id=" + upload_id + "&filename=");
					await httpClientFactory.Get("https://member.bilibili.com/x/vupre/web/topic/type/v2?pn=0&ps=6&title=" + content + "&track_id=" + track_id + "&upload_id=" + upload_id + "&zip_url=&platform=pc&t=" + DateTimeOffset.UtcNow.ToUnixTimeMilliseconds());
					JObject jToken2;
					if (tag == "")
					{
						jToken2 = await httpClientFactory.Get("https://member.bilibili.com/x/vupre/web/tag/recommend?upload_id=" + upload_id + "&subtype_id=" + tid + "&title=" + content + "&filename=" + filename + "&description=&cover_url=&t=" + DateTimeOffset.UtcNow.ToUnixTimeMilliseconds());
						if (Util.GetJObject(jToken2, "code") == "0")
						{
							JArray jArray2 = Util.GetJObject<JArray>(jToken2, "data") ?? new JArray();
							if (jArray2.Count == 0)
							{
								tag = articlesTag;
							}
							else
							{
								tag = string.Join(",", jArray2.Select((JToken token) => token["tag"]?.ToString()).ToArray());
								string[] array = tag.Split(',');
								if (array.Length > 5)
								{
									tag = string.Join(",", array.Take(5));
								}
							}
						}
						else
						{
							tag = articlesTag;
						}
					}
					if (!tag.Contains(articlesTag))
					{
						tag = articlesTag + "," + tag;
					}
					tag = tag.Replace("，", ",").Trim(',');
					Util.WriteLog("BiliBili", name, jobName, "当前tag：" + tag);
					if (from_topic_id == "")
					{
						jToken2 = await httpClientFactory.Get("https://member.bilibili.com/x/vupre/web/topic/search?keywords=" + Uri.EscapeDataString(articlesTag) + "&page_size=50&offset=0&t=" + DateTimeOffset.UtcNow.ToUnixTimeMilliseconds());
						if (!(Util.GetJObject(jToken2, "code") == "0"))
						{
							throw new Exception("获取活动错误:" + Util.GetJObject(jToken2, "message"));
						}
						IEnumerable<JToken> source = Util.GetJObject<JArray>(jToken2["data"]?["result"], "topics") ?? new JArray();
						IEnumerable<JToken> source2 = source.Where((JToken token) => token["name"]?.ToString() == articlesTag);
						if (source2.Any())
						{
							from_topic_id = source2.First()["id"]?.ToString() ?? "";
						}
						else
						{
							source.Where((JToken token) => bool.Parse(token["show_activity_icon"]?.ToString() ?? "false"));
						}
					}
					JObject jObject7 = await httpClientFactory.Get("https://member.bilibili.com/preupload?name=file_meta.txt&size=2000&r=upos&profile=fxmeta%2Fbup&ssl=0&version=2.14.0.0&build=2140000&webVersion=2.14.0");
					if (Util.GetJObject(jObject7, "OK") != "1")
					{
						throw new Exception(JsonConvert.SerializeObject("2:" + jObject7));
					}
					string url = "https:" + Util.GetJObject(jObject5, "endpoint") + Util.GetJObject(jObject5, "upos_uri").Replace("upos:/", "") + "?uploads&output=json&profile=ugcfx/bup&filesize=" + fileSize + "&partsize=" + Util.GetJObject(jObject5, "chunk_size") + "&meta_upos_uri=" + Util.GetJObject(jObject7, "upos_uri") + "&biz_id=" + Util.GetJObject(jObject5, "biz_id");
					JObject jObject8 = await httpClientFactory2.Post(url, "", new Dictionary<string, string> { 
					{
						"x-upos-auth",
						Util.GetJObject(jObject5, "auth")
					} });
					if (Util.GetJObject(jObject8, "OK") != "1")
					{
						throw new Exception("3:" + JsonConvert.SerializeObject(jObject8));
					}
					url = "https:" + Util.GetJObject(jObject5, "endpoint") + Util.GetJObject(jObject5, "upos_uri").Replace("upos:/", "") + "?partNumber=1&uploadId=" + Util.GetJObject(jObject8, "upload_id") + "&chunk=0&chunks=1&size=" + fileSize + "&start=0&end=" + fileSize + "&total=" + fileSize;
					jToken2 = await httpClientFactory2.Upload(url, "PUT", videoPath, 0, -1, null, defaultHandler);
					if (Util.GetJObject(jToken2, "code") != "0")
					{
						throw new Exception("PutMp4:" + JsonConvert.SerializeObject(jToken2));
					}
					url = "https:" + Util.GetJObject(jObject7, "endpoint") + Util.GetJObject(jObject7, "upos_uri").Replace("upos:/", "") + "?uploads&output=json&";
					JObject jObject9 = await httpClientFactory2.Post(url, "", new Dictionary<string, string> { 
					{
						"x-upos-auth",
						Util.GetJObject(jObject7, "auth")
					} });
					if (Util.GetJObject(jObject9, "OK") != "1")
					{
						throw new Exception("4:" + JsonConvert.SerializeObject(jObject9));
					}
					url = "https:" + Util.GetJObject(jObject5, "endpoint") + Util.GetJObject(jObject5, "upos_uri").Replace("upos:/", "") + "?output=json&name=" + fileName + "&profile=ugcfx%2Fbup&uploadId=" + Util.GetJObject(jObject8, "upload_id") + "&biz_id=" + Util.GetJObject(jObject5, "biz_id");
					JObject jObject10 = await httpClientFactory2.Post(url, "{parts: [{partNumber: 1, eTag: \"etag\"}]}", new Dictionary<string, string> { 
					{
						"x-upos-auth",
						Util.GetJObject(jObject5, "auth")
					} });
					if (Util.GetJObject(jObject10, "OK") != "1")
					{
						throw new Exception("5:" + JsonConvert.SerializeObject(jObject10));
					}
					url = "https:" + Util.GetJObject(jObject7, "endpoint") + Util.GetJObject(jObject7, "upos_uri").Replace("upos:/", "") + "?partNumber=1&uploadId=" + Util.GetJObject(jObject9, "upload_id") + "&chunk=0&chunks=1&size=1787&start=0&end=1787&total=1787";
					string jObject11 = Util.GetJObject(jObjectVideo, "duration");
					string jObject12 = Util.GetJObject(jObjectVideo, "avg_frame_rate");
					string jObject13 = Util.GetJObject(jObjectVideo, "display_aspect_ratio");
					JObject value = new JObject
					{
						["code"] = 0,
						["filename"] = filename,
						["meta"] = new JObject
						{
							["container_meta"] = new JObject
							{
								["format_name"] = "mov,mp4,m4a,3gp,3g2,mj2",
								["size"] = fileSize,
								["container_duration"] = jObject11.Replace(".", ""),
								["duration"] = Util.GetJObject(jObjectAudio, "duration"),
								["start_time"] = "0",
								["av_delta"] = ""
							},
							["audio_meta"] = new JObject
							{
								["bit_rate"] = Util.GetJObject(jObjectAudio, "bit_rate"),
								["channels"] = Util.GetJObject(jObjectAudio, "channels"),
								["codec_name"] = Util.GetJObject(jObjectAudio, "codec_name"),
								["duration"] = Util.GetJObject(jObjectAudio, "duration"),
								["frame_count"] = "",
								["profile"] = Util.GetJObject(jObjectAudio, "profile"),
								["r_frame_rate"] = "",
								["sample_rate"] = Util.GetJObject(jObjectAudio, "sample_rate"),
								["start_time"] = "0",
								["stream_size"] = ""
							},
							["video_meta"] = new JObject
							{
								["avg_frame_rate"] = jObject12,
								["avg_frame_rate_num"] = jObject12.Split('/')[0],
								["avg_frame_rate_den"] = ((jObject12.Split('/').Length > 1) ? jObject12.Split('/')[1] : ""),
								["bit_rate"] = Util.GetJObject(jObjectVideo, "bit_rate"),
								["bits_per_raw_sample"] = Util.GetJObject(jObjectVideo, "bits_per_raw_sample"),
								["codec_name"] = Util.GetJObject(jObjectVideo, "codec_name"),
								["codec_tag_string"] = Util.GetJObject(jObjectVideo, "codec_tag_string"),
								["color_primaries"] = Util.GetJObject(jObjectVideo, "color_primaries"),
								["color_range"] = Util.GetJObject(jObjectVideo, "color_range"),
								["color_space"] = Util.GetJObject(jObjectVideo, "color_space"),
								["color_transfer"] = Util.GetJObject(jObjectVideo, "color_transfer"),
								["display_aspect_ratio"] = jObject13,
								["display_aspect_ratio_num"] = ((jObject13 == "") ? "0" : jObject13.Split(':')[0]),
								["display_aspect_ratio_den"] = ((jObject13 == "") ? "0" : jObject13.Split(':')[1]),
								["duration"] = jObject11,
								["frame_count"] = "",
								["field_order"] = "",
								["has_b_frames"] = Util.GetJObject(jObjectVideo, "has_b_frames"),
								["hdr_format"] = "",
								["hdr_format_compatibility"] = "",
								["hdr_format_level"] = "",
								["hdr_format_profile"] = "",
								["hdr_format_settings"] = "",
								["hdr_format_version"] = "",
								["height"] = Util.GetJObject(jObjectVideo, "height"),
								["interlaced"] = "0",
								["is_dts_cfr"] = "",
								["is_pts_cfr"] = "",
								["is_spherical"] = "",
								["key_count"] = "",
								["lack_intra"] = "",
								["level"] = (double.Parse(Util.GetJObject(jObjectVideo, "level")) / 10.0).ToString("f6"),
								["max_dts_gop_size"] = "",
								["max_pts_gop_size"] = "",
								["max_rate"] = "",
								["pass_video_decoding_trial"] = "",
								["pix_fmt"] = Util.GetJObject(jObjectVideo, "pix_fmt"),
								["profile"] = Util.GetJObject(jObjectVideo, "profile"),
								["pts_disorder"] = "",
								["r_frame_rate"] = Util.GetJObject(jObjectVideo, "r_frame_rate"),
								["rotate"] = "0",
								["rotation"] = "0",
								["scan_order"] = "",
								["scan_type"] = "",
								["start_time"] = "0",
								["stream_size"] = "",
								["width"] = Util.GetJObject(jObjectVideo, "width"),
								["spherical_meta"] = JObject.Parse("{\"spherical_mapping\": \"\",\"stereo_3D\": \"\"}")
							},
							["check_result"] = JObject.Parse("{\"timestamp_jump\":{\"video\":{\"from\":\"\",\"to\":\"\"},\"audio\":{\"from\":\"\",\"to\":\"\"}},\"timestamp_stretch\":{\"audio\":\"\",\"video\":\"\"},\"is_close_gop\":\"\"}"),
							["version"] = "v1.0.6",
							["ijkffmpeg_version"] = "n4.3.1",
							["defender_code"] = 0
						},
						["key_frames"] = new JArray(),
						["filesize"] = fileSize,
						["version"] = "v1.0.6",
						["ijkffmpeg_version"] = "n4.3.1",
						["webVersion"] = "3.0.0"
					};
					string text2 = JsonConvert.SerializeObject(value);
					jToken2 = await httpClientFactory2.Put(url, text2, new Dictionary<string, string> { 
					{
						"x-upos-auth",
						Util.GetJObject(jObject7, "auth")
					} });
					if (Util.GetJObject(jToken2, "code") != "0")
					{
						throw new Exception("PutTxt:" + JsonConvert.SerializeObject(jToken2));
					}
					string text3 = "BUploader_4_oodgc_" + DateTimeOffset.UtcNow.ToUnixTimeSeconds() + "_meta.txt";
					url = "https:" + Util.GetJObject(jObject7, "endpoint") + Util.GetJObject(jObject7, "upos_uri").Replace("upos:/", "") + "?output=json&name=" + text3 + "&profile=&uploadId=" + Util.GetJObject(jObject9, "upload_id") + "&biz_id=";
					JObject jObject14 = await httpClientFactory2.Post(url, "{\"parts\":[{\"partNumber\":1,\"eTag\":\"etag\"}]}", new Dictionary<string, string> { 
					{
						"x-upos-auth",
						Util.GetJObject(jObject7, "auth")
					} });
					if (Util.GetJObject(jObject14, "OK") != "1")
					{
						throw new Exception("6:" + JsonConvert.SerializeObject(jObject14));
					}
					jToken2 = await httpClientFactory2.Post("https://member.bilibili.com/x/vu/web/cover/up", "cover=" + Util.ImageToBase64(imagePath) + "&csrf=" + csrf);
					if (Util.GetJObject(jToken2, "code") != "0")
					{
						throw new Exception("cover:" + JsonConvert.SerializeObject(jToken2));
					}
					cover_url = Util.GetJObject(jToken2["data"], "url");
					if (File.Exists(imagePath))
					{
						File.Delete(imagePath);
					}
					i = 4;
				}
				catch (Exception ex)
				{
					Util.WriteLog("BiliBili", name, jobName, "正常重试，错误原因：" + ex.Message, ConsoleColor.Red);
				}
			}
			JObject value2 = new JObject
			{
				["from_topic_id"] = long.Parse(from_topic_id),
				["from_source"] = "arc.web.search"
			};
			JArray jArray3 = new JArray();
			JObject item = new JObject
			{
				["filename"] = filename,
				["title"] = title,
				["desc"] = desc,
				["cid"] = long.Parse(Util.GetJObject(jObject5, "biz_id"))
			};
			jArray3.Add(item);
			JObject value3 = new JObject
			{
				["open"] = 0,
				["lan"] = ""
			};
			JObject add_v3 = new JObject
			{
				["act_reserve_create"] = 0,
				["ai_cover"] = 0,
				["copyright"] = 1,
				["cover"] = cover_url,
				["cover43"] = cover_url,
				["csrf"] = csrf,
				["desc"] = "",
				["dolby"] = 0,
				["dynamic"] = "",
				["human_type2"] = type,
				["interactive"] = 0,
				["is_only_self"] = 0,
				["mission_id"] = long.Parse(articlesMissionId),
				["lossless_music"] = 0,
				["neutral_mark"] = "",
				["no_disturbance"] = 0,
				["no_reprint"] = 1,
				["recreate"] = -1,
				["subtitle"] = value3,
				["tag"] = tag,
				["tid"] = tid,
				["title"] = title,
				["topic_detail"] = value2,
				["topic_id"] = long.Parse(from_topic_id),
				["up_selection_reply"] = false,
				["up_close_reply"] = false,
				["up_close_danmu"] = false,
				["videos"] = jArray3,
				["watermark"] = new JObject { ["state"] = 1 },
				["web_os"] = 1
			};
			int times;
			for (times = 0; times < 10; times++)
			{
				string text4 = JsonConvert.SerializeObject(add_v3);
				JObject jToken2 = await httpClientFactory.Post("https://member.bilibili.com/x/vu/web/add/v3?t=" + DateTimeOffset.UtcNow.ToUnixTimeMilliseconds() + "&csrf=" + csrf, text4, new Dictionary<string, string> { { "Content-Type", "application/json;charset=UTF-8" } });
				if (Util.GetJObject(jToken2, "code") == "0")
				{
					Util.WriteLog("BiliBili", name, jobName, "投稿成功，aid：" + Util.GetJObject(jToken2["data"], "aid") + "，bvid：" + Util.GetJObject(jToken2["data"], "bvid"), ConsoleColor.Green);
					break;
				}
				if (Util.GetJObject(jToken2, "code") == "601")
				{
					Util.WriteLog("BiliBili", name, jobName, Util.GetJObject(jToken2, "message") + " 正在验证！", ConsoleColor.Yellow);
					string videoPath2 = Util.GetJObject(jToken2["data"], "v_voucher");
					jObject5 = await httpClientFactory.Post("https://api.bilibili.com/x/gaia-vgate/v2/register", "csrf=" + csrf + "&v_voucher=" + videoPath2, new Dictionary<string, string>
					{
						{ "Content-Type", "application/x-www-form-urlencoded" },
						{ "referer", "https://member.bilibili.com/" }
					});
					if (Util.GetJObject(jObject5, "code") == "0")
					{
						string jObject15 = Util.GetJObject(jObject5["data"], "content");
						jObject5 = await BiliGeetest.Geetest4(videoPath2, jObject15, proxyAddress, proxyUserName, proxyPassword);
						if (Util.GetJObject(jObject5, "code") == "0")
						{
							string text5 = HttpUtility.UrlEncode(Util.GetJObject(jObject5["data"], "token"));
							content = HttpUtility.UrlEncode(Util.GetJObject(jObject5["data"], "content"));
							string text6 = HttpUtility.UrlEncode(Util.GetJObject(jObject5["data"], "dm_track"));
							string text7 = "token=" + text5 + "&content=" + content + "&dm_track=" + text6 + "&csrf=" + csrf;
							JObject jObject16 = await httpClientFactory.Post("https://api.bilibili.com/x/gaia-vgate/v2/validate", text7, dic);
							if (Util.GetJObject(jObject16, "code") == "0")
							{
								if (Util.GetJObject(jObject16["data"], "is_valid") == "1")
								{
									Util.WriteLog("BiliBili", name, jobName, "验证成功！", ConsoleColor.Green);
									continue;
								}
								Util.WriteLog("BiliBili", name, jobName, "验证失败，正在重试！", ConsoleColor.Red);
								times += 4;
							}
							else
							{
								Util.WriteLog("BiliBili", name, jobName, Util.GetJObject(jObject16, "message"), ConsoleColor.Red);
								times += 4;
							}
						}
						else
						{
							Util.WriteLog("BiliBili", name, jobName, "验证失败|" + JsonConvert.SerializeObject(jObject5), ConsoleColor.Red);
							times += 4;
						}
					}
					else
					{
						Util.WriteLog("BiliBili", name, jobName, Util.GetJObject(jObject5, "message"), ConsoleColor.Red);
					}
					continue;
				}
				throw new Exception(Util.GetJObject(jToken2, "message"));
			}
			if (times >= 10)
			{
				throw new Exception("验证次数太多，请手动处理！");
			}
			foreach (string item3 in list)
			{
				if (File.Exists(item3))
				{
					File.Delete(item3);
				}
			}
		}
		catch (Exception ex2)
		{
			Util.WriteLog("BiliBili", name, jobName, ex2.Message, ConsoleColor.Red);
		}
	}

	public static DataTable GetUserList(string search)
	{
		string text = "SELECT 'public' AS id,'公共文件' AS label UNION ALL";
		text += " SELECT FKey AS id,FName AS label FROM TCookies WHERE FExpirationTime>GETDATE()";
		if (search != "")
		{
			text = text + " AND FName LIKE '%" + search + "%'";
		}
		return SQLHelper.BiliLocalDB.RunSqlDt(text);
	}

	public static JArray GetArticlesList(string areaId, string search, string date, int times, string type, string taskId, string taskStatus, string cookieId, int userId)
	{
		string text;
		if (type == "10" || type == "9")
		{
			type = ((type == "9") ? "已锁定" : "正在申诉");
			text = " SELECT T1.Fid AS FCookieId,T1.FIdentifying,T1.FKey,T1.FName AS FCookieName,T1.FHeaders,T2.Fid,T2.FAppeal,CAST (T2.FDate AS NVARCHAR(100)) AS FDate FROM TCookies T1 LEFT JOIN TArticles T2 ON T2.FCookieId=T1.Fid";
			text = text + " LEFT JOIN TCookieArea T3 ON T3.FKey=T1.FKey AND T3.FAreaId=" + areaId + " AND T3.FUserId=" + userId;
			text = text + " WHERE T1.FUserId=" + userId + " AND T2.FAppeal='" + type + "' AND ISNULL(T3.FExpirationTime,T1.FExpirationTime)>GETDATE() AND T1.FEnable=1";
			if (search != "")
			{
				text = text + " AND T1.FName LIKE '%" + search + "%'";
			}
			text += " ORDER BY FSort";
			DataTable o = SQLHelper.BiliLocalDB.RunSqlDt(text);
			return JArray.FromObject(o);
		}
		text = " SELECT FArticlesDate,FArticlesMissionId,FArticlesTag FROM TArea WHERE Fid=" + areaId;
		DataTable dataTable = SQLHelper.BiliLocalDB.RunSqlDt(text);
		DateTime result = DateTime.Now;
		if (DateTime.TryParse(search, out result))
		{
			search = "";
		}
		else if (!DateTime.TryParse(date, out result))
		{
			result = DateTime.Parse(Util.GetJObject(dataTable.Rows[0], "FArticlesDate"));
		}
		string articlesMissionId = Util.GetJObject(dataTable.Rows[0], "FArticlesMissionId");
		text = " SELECT T1.Fid AS FCookieId,T1.FIdentifying,T1.FKey,T1.FName AS FCookieName,T1.FHeaders,T2.* FROM TCookies T1 LEFT JOIN TArticles T2 ON T2.FCookieId=T1.Fid";
		text = text + " LEFT JOIN TCookieArea T3 ON T3.FKey=T1.FKey AND T3.FAreaId=" + areaId + " AND T3.FUserId=" + userId;
		text = text + " WHERE T1.FUserId=" + userId + " AND ISNULL(T3.FExpirationTime,T1.FExpirationTime)>GETDATE() AND T1.FEnable=1";
		if (taskId != "" && taskStatus != "")
		{
			text = text + " AND T1.Fid IN (SELECT FCookieId FROM TCookiesTask WHERE FReceiveStatus=" + taskStatus + " AND FAreaTaskId=" + taskId + ")";
		}
		if (cookieId != "")
		{
			text = text + " AND T1.Fid IN (" + cookieId + ")";
		}
		text = text + " AND (T1.FKey IN (SELECT FKey FROM TCookieArea WHERE FAreaId=" + areaId + " AND FUserId=" + userId + " AND FAricles=1) ";
		text = text + "      OR T1.FKey NOT IN (SELECT FKey FROM TCookieArea WHERE FUserId=" + userId + "))";
		if (search != "")
		{
			text = text + " AND T1.FName LIKE '%" + search + "%'";
		}
		text += " ORDER BY FSort";
		DataTable dataTable2 = SQLHelper.BiliLocalDB.RunSqlDt(text);
		JArray jArray = new JArray();
		foreach (DataRow row in dataTable2.Rows)
		{
			string jObject = Util.GetJObject(row, "FContent");
			JObject jObject2 = new JObject
			{
				["FCookieId"] = Util.GetJObject(row, "FCookieId"),
				["FKey"] = Util.GetJObject(row, "FKey"),
				["FCookieName"] = Util.GetJObject(row, "FCookieName"),
				["FIdentifying"] = Util.GetJObject(row, "FIdentifying"),
				["FHeaders"] = Util.GetJObject(row, "FHeaders"),
				["Fid"] = Util.GetJObject(row, "Fid"),
				["FDate"] = ((Util.GetJObject(row, "FDate") != "") ? DateTime.Parse(Util.GetJObject(row, "FDate")).ToString("yyyy-MM-dd") : "")
			};
			int result2;
			if (jObject != "")
			{
				JArray jArray2 = JArray.Parse(jObject);
				int num = 0;
				DateTime dateTime = DateTime.Now;
				while (dateTime > result)
				{
					string ptime = dateTime.ToString("yyyy-MM-dd");
					if (jArray2.Count > 0)
					{
						IEnumerable<JToken> enumerable = jArray2.Where((JToken item) => item["ptime"]?.ToString() == ptime && item["mission_id"]?.ToString() == articlesMissionId);
						if (enumerable.Any())
						{
							IEnumerable<JToken> enumerable2 = enumerable.Where((JToken item) => item["state_desc"]?.ToString() == "开放浏览");
							if (enumerable2.Any())
							{
								jObject2[dateTime.Month + "." + dateTime.Day] = enumerable2.Sum((JToken item) => Convert.ToDecimal(item["view"]?.ToString() ?? "0"));
								int num2 = num;
								string text2 = dateTime.Month.ToString();
								result2 = dateTime.Day;
								num = num2 + int.Parse(Util.GetJObject(jObject2, text2 + "." + result2));
								if (type == "2")
								{
									string text3 = "";
									foreach (JToken item in enumerable2)
									{
										text3 = ((text3 == "") ? (item["bvid"]?.ToString() ?? "") : (text3 + " " + item["bvid"]));
										result2 = dateTime.Month;
										string text4 = result2.ToString();
										result2 = dateTime.Day;
										jObject2[text4 + "." + result2] = text3;
									}
								}
								else if (type == "4")
								{
									string text5 = "";
									string text6 = dateTime.Month.ToString();
									result2 = dateTime.Day;
									jObject2[text6 + "." + result2] = "";
									foreach (JToken item2 in enumerable2)
									{
										int jObject3 = Util.GetJObject<int>(item2, "view");
										if (jObject3 < times || times == 0)
										{
											text5 = ((text5 == "") ? (item2["bvid"]?.ToString() + "," + jObject3) : (text5 + "|" + item2["bvid"]?.ToString() + "," + jObject3));
											result2 = dateTime.Month;
											string text7 = result2.ToString();
											result2 = dateTime.Day;
											jObject2[text7 + "." + result2] = text5;
										}
									}
								}
							}
							else
							{
								using IEnumerator<JToken> enumerator4 = enumerable.GetEnumerator();
								if (enumerator4.MoveNext())
								{
									JToken current3 = enumerator4.Current;
									string text8 = Util.GetJObject(current3, "state_desc");
									string jObject4 = Util.GetJObject(current3, "bvid");
									if (!(text8 == "稿件流量受影响"))
									{
										if (text8 == "已锁定")
										{
											text8 = "X";
										}
									}
									else
									{
										text8 = "▲";
									}
									if (type == "1")
									{
										result2 = dateTime.Month;
										string text9 = result2.ToString();
										result2 = dateTime.Day;
										jObject2[text9 + "." + result2] = text8;
									}
									else
									{
										result2 = dateTime.Month;
										string text10 = result2.ToString();
										result2 = dateTime.Day;
										if (Util.GetJObject(jObject2, text10 + "." + result2) == "")
										{
											result2 = dateTime.Month;
											string text11 = result2.ToString();
											result2 = dateTime.Day;
											jObject2[text11 + "." + result2] = jObject4 + "-" + text8;
										}
										else
										{
											result2 = dateTime.Month;
											string text12 = result2.ToString();
											result2 = dateTime.Day;
											string propertyName = text12 + "." + result2;
											string[] array = new string[5];
											result2 = dateTime.Month;
											string text13 = result2.ToString();
											result2 = dateTime.Day;
											array[0] = jObject2[text13 + "." + result2]?.ToString();
											array[1] = " ";
											array[2] = jObject4;
											array[3] = "-";
											array[4] = text8;
											jObject2[propertyName] = string.Concat(array);
										}
									}
								}
							}
						}
						else
						{
							string text14 = dateTime.Month.ToString();
							result2 = dateTime.Day;
							jObject2[text14 + "." + result2] = "";
						}
					}
					else
					{
						string text15 = dateTime.Month.ToString();
						result2 = dateTime.Day;
						jObject2[text15 + "." + result2] = "";
					}
					dateTime = dateTime.AddDays(-1.0);
				}
				jObject2["FTotal"] = num;
			}
			if (type == "3" && !int.TryParse(jObject2[DateTime.Now.Month + "." + DateTime.Now.Day]?.ToString(), out result2))
			{
				jArray.Add(jObject2);
				continue;
			}
			switch (type)
			{
			case "1":
			case "2":
			case "4":
				jArray.Add(jObject2);
				break;
			}
		}
		return jArray;
	}

	public static void SaveArticles(JObject jObject, int userId, string userName, string curTime)
	{
		string jObject2 = Util.GetJObject(jObject, "FCookieId");
		jObject["Fid"] = 0;
		jObject["FUserId"] = userId;
		string text = "SELECT FContent FROM TArticles WHERE FUserId=" + userId + " AND FCookieId=" + jObject2;
		string text2 = SQLHelper.BiliLocalDB.RunSqlStr(text.ToString());
		text = "SELECT FConfig FROM TArticles WHERE FUserId=" + userId + " AND FCookieId=" + jObject2;
		string text3 = SQLHelper.BiliLocalDB.RunSqlStr(text.ToString());
		if (text2 != "")
		{
			JArray jArray = JArray.Parse(text2);
			JArray jArray2 = JArray.Parse(Util.GetJObject(jObject, "FContent"));
			foreach (JToken item in jArray2)
			{
				string bvid = Util.GetJObject(item, "bvid");
				if (!jArray.Any((JToken token) => token["bvid"]?.ToString() == bvid))
				{
					jArray.Add(item);
					continue;
				}
				for (int num = 0; num < jArray.Count; num++)
				{
					if (Util.GetJObject(jArray[num], "bvid") == bvid)
					{
						jArray[num]["state_desc"] = Util.GetJObject(item, "state_desc");
						jArray[num]["view"] = Util.GetJObject(item, "view");
						break;
					}
				}
			}
			jObject["FContent"] = JsonConvert.SerializeObject(jArray);
		}
		jObject["FConfig"] = text3;
		DataCURD.Delete("TArticles", "删除投稿数据", "FCookieId", jObject2, userId, userName, curTime, SQLHelper.BiliLocalDB.InitCnn());
		DataCURD.Save(jObject, "TArticles", "保存投稿数据", "Fid", userId, userName, curTime, SQLHelper.BiliLocalDB.InitCnn());
	}

	public static void SaveVideo(string str, int userId, string userName, string curTime)
	{
		string sSql = "SELECT ISNULL((SELECT Fid FROM TArticles WHERE FUserId=0 AND FCookieId=0),0)";
		string text = SQLHelper.BiliLocalDB.RunSqlStr(sSql);
		JObject jObject = new JObject
		{
			["Fid"] = text,
			["FUserId"] = 0,
			["FCookieId"] = 0,
			["FContent"] = str
		};
		DataCURD.Save(jObject, "TArticles", "保存投稿数据", "Fid", userId, userName, curTime, SQLHelper.BiliLocalDB.InitCnn());
	}

	public static string GetVideo()
	{
		string sSql = "SELECT FContent FROM TArticles WHERE FUserId=0 AND FCookieId=0";
		string text = SQLHelper.BiliLocalDB.RunSqlStr(sSql);
		if (!(text == ""))
		{
			return text;
		}
		return "[]";
	}

	public static async Task UpdateArticles(JArray jArray, int userId, string userName, string curTime)
	{
		string cookieId = string.Join(",", (jArray ?? new JArray()).Select((JToken token) => (token["FCookieId"] ?? ((JToken)"")).ToString()).ToArray());
		DataTable cookiesList = BiliCookies.GetCookiesList(cookieId, await QzUtil.Validate(userId, BusSysUser.Instance.User.Address, "bilibili"), userId);
		List<int> list = (from row in cookiesList.AsEnumerable()
			select row.Field<int>("FProxyId")).Distinct().ToList();
		ConcurrentBag<JObject> bag = new ConcurrentBag<JObject>();
		List<Task> list2 = new List<Task>();
		foreach (int item in list)
		{
			DataTable dtGroup = cookiesList.Select($"FProxyId = {item}").CopyToDataTable();
			list2.Add(Task.Run(async delegate
			{
				await UpdateArticles2(userId, userName, curTime, bag, dtGroup);
			}));
		}
		foreach (Task item2 in list2)
		{
			item2.Wait();
		}
		DataTable dataTable = JArray.FromObject(bag).ToObject<DataTable>();
		if (dataTable != null)
		{
			SQLHelper.BiliLocalDB.SqlBulkCopyByDataTable("TArticles", dataTable);
		}
	}

	private static async Task UpdateArticles2(int userId, string userName, string curTime, ConcurrentBag<JObject> bag, DataTable dtCookies)
	{
		for (int i = 0; i < dtCookies.Rows.Count; i++)
		{
			DataRow dataRow = dtCookies.Rows[i];
			string id = dataRow["Fid"].ToString() ?? "";
			string name = dataRow["FKey"].ToString() ?? "";
			string cookie = dataRow["FCookie"].ToString() ?? "";
			string header = dataRow["FHeaders"].ToString() ?? "";
			string text = dataRow["FProxyAddress"].ToString() ?? "";
			string userName2 = dataRow["FProxyUserName"].ToString() ?? "";
			string password = dataRow["FProxyPassword"].ToString() ?? "";
			JArray jArray = new JArray();
			try
			{
				HttpClientHandler defaultHandler = null;
				if (text != "")
				{
					defaultHandler = new HttpClientHandler
					{
						Proxy = new WebProxy
						{
							Address = new Uri(text),
							Credentials = new NetworkCredential(userName2, password)
						}
					};
				}
				Dictionary<string, string> dictionary = HttpClientFactory.FormataHeader(header, cookie);
				dictionary.Remove("Accept-Language");
				HttpClientFactory httpClientFactory = new HttpClientFactory(name, dictionary, defaultHandler);
				string tempDesc = "已锁定";
				for (int j = 1; j <= 2; j++)
				{
					int num = j;
					JObject jObject = await httpClientFactory.Get("https://member.bilibili.com/x/web/archives?status=is_pubing,pubed,not_pubed&pn=" + num + "&ps=50&coop=1&interactive=1", null, 5.0);
					if (!(Util.GetJObject(jObject, "code") == "0"))
					{
						continue;
					}
					JArray jArray2 = Util.GetJObject<JArray>(Util.GetJObject<JObject>(jObject, "data"), "arc_audits") ?? new JArray();
					foreach (JToken jToken in jArray2)
					{
						if (!jArray.Any((JToken token) => token["bvid"]?.ToString() == Util.GetJObject(Util.GetJObject<JToken>(jToken, "Archive"), "bvid")))
						{
							string jObject2 = Util.GetJObject(Util.GetJObject<JToken>(jToken, "Archive"), "state_desc");
							if (jObject2 != "已锁定" && tempDesc == "已锁定")
							{
								tempDesc = "";
							}
							jArray.Add(new JObject
							{
								["aid"] = Util.GetJObject(Util.GetJObject<JToken>(jToken, "Archive"), "aid"),
								["bvid"] = Util.GetJObject(Util.GetJObject<JToken>(jToken, "Archive"), "bvid"),
								["mission_id"] = Util.GetJObject(Util.GetJObject<JToken>(jToken, "Archive"), "mission_id"),
								["state_desc"] = jObject2,
								["ptime"] = DateTimeOffset.FromUnixTimeSeconds(Util.GetJObject<long>(Util.GetJObject<JToken>(jToken, "Archive"), "ptime")).LocalDateTime.ToString("yyyy-MM-dd"),
								["view"] = Util.GetJObject(Util.GetJObject<JToken>(jToken, "stat"), "view")
							});
						}
					}
					if (Util.GetJObject<int>(Util.GetJObject<JToken>(jObject["data"], "page"), "count") <= 50)
					{
						break;
					}
				}
				if (tempDesc == "已锁定")
				{
					JArray jArray3 = Util.GetJObject<JArray>(await httpClientFactory.Get("https://member.bilibili.com/x/web/appeal/v2/list?ps=10&state=all&pn=1", null, 5.0), "appeals") ?? new JArray();
					if (jArray3.Count > 0)
					{
						string jObject3 = Util.GetJObject(Util.GetJObject<JToken>(jArray3[0], "archive"), "state");
						if (jObject3 == "-4")
						{
							tempDesc = "正在申诉";
						}
					}
				}
				DataCURD.Delete("TArticles", "删除投稿信息", "FCookieId", id, userId, userName, curTime, SQLHelper.BiliLocalDB.InitCnn());
				bag.Add(new JObject
				{
					["Fid"] = 0,
					["FUserId"] = userId,
					["FCookieId"] = id,
					["FContent"] = JsonConvert.SerializeObject(jArray),
					["FDate"] = DateTime.Now,
					["FAppeal"] = tempDesc
				});
			}
			catch (Exception ex)
			{
				throw new Exception(ex.Message);
			}
		}
	}

	public static async Task DelArticles(string val, string cookieId, int userId)
	{
		string bvid = val.Split('-')[0];
		string text = " SELECT T1.Fid AS FCookieId,T1.FKey,T1.FName AS FCookieName,T1.FHeaders,T1.FCookie";
		text += " ,T3.FAddress AS FProxyAddress,T3.FUserName AS FProxyUserName,T3.FPassword AS FProxyPassword";
		text += " ,T2.* FROM TCookies T1 LEFT JOIN TArticles T2 ON T2.FCookieId=T1.Fid";
		text = text + " LEFT JOIN TProxy T3 ON T3.FEnable=1 AND T3.Fid=T1.FProxyId AND T2.FUserId=" + userId;
		text = text + " WHERE T1.FUserId=" + userId + " AND T1.FExpirationTime>GETDATE() AND T1.FEnable=1";
		DataTable dataTable = SQLHelper.BiliLocalDB.RunSqlDt(text);
		DataRow[] array = dataTable.Select("FCookieId=" + cookieId);
		if (array == null || array.Length == 0)
		{
			return;
		}
		string jObject = Util.GetJObject(array[0], "FContent");
		JArray articles = JArray.Parse(jObject);
		IEnumerable<JToken> query = articles.Where((JToken item) => item["bvid"]?.ToString() == bvid);
		if (!query.Any())
		{
			return;
		}
		string aid = Util.GetJObject(query.First(), "aid");
		string name = Util.GetJObject(array[0], "FCookieName");
		Util.WriteLog("BiliBili", name, "稿件管理", "正在删除稿件 " + bvid);
		string jObject2 = Util.GetJObject(array[0], "FHeaders");
		string cookie = Util.GetJObject(array[0], "FCookie");
		string jObject3 = Util.GetJObject(array[0], "FProxyAddress");
		string jObject4 = Util.GetJObject(array[0], "FProxyUserName");
		string jObject5 = Util.GetJObject(array[0], "FProxyPassword");
		string jObject6 = Util.GetJObject(array[0], "FKey");
		HttpClientHandler defaultHandler = null;
		if (jObject3 != "")
		{
			defaultHandler = new HttpClientHandler
			{
				Proxy = new WebProxy
				{
					Address = new Uri(jObject3),
					Credentials = new NetworkCredential(jObject4, jObject5)
				}
			};
		}
		Dictionary<string, string> dictionary = HttpClientFactory.FormataHeader(jObject2, cookie);
		dictionary.Remove("Accept-Language");
		HttpClientFactory httpClientFactory = new HttpClientFactory(jObject6, dictionary, defaultHandler);
		JObject jToken = await httpClientFactory.Get("https://member.bilibili.com/x/risk/archive/del?platform=web&bvid=" + bvid);
		if (Util.GetJObject(jToken, "code") == "0")
		{
			jToken = await httpClientFactory.Get("https://member.bilibili.com/x/geetest/pre?t=" + DateTimeOffset.UtcNow.ToUnixTimeMilliseconds());
			if (Util.GetJObject(jToken, "code") == "0")
			{
				string jObject7 = Util.GetJObject(jToken["data"], "gt");
				string challenge = Util.GetJObject(jToken["data"], "challenge");
				JObject jObjectGeetest = await BusBiliUtil.GetValidate(BiliGeetest.GetGeetestList("稿件删除"), jObject7, challenge, "https://member.bilibili.com/platform/upload-manager/article");
				if (Util.GetJObject(jObjectGeetest, "status") == "1")
				{
					string resultid = Util.GetJObject(jObjectGeetest, "resultid");
					bool b = true;
					string validate = "";
					while (b)
					{
						await Task.Delay(5000);
						jToken = await httpClientFactory.Post("http://api.ttocr.com/api/results", jObjectGeetest["KeyField"]?.ToString() + "=" + jObjectGeetest["KeyValue"]?.ToString() + "&resultid=" + resultid);
						if (!(Util.GetJObject(jToken, "status") == "2"))
						{
							if (!(Util.GetJObject(jToken, "status") == "1"))
							{
								throw new Exception("打码失败:" + Util.GetJObject(jToken, "msg"));
							}
							b = false;
							validate = Util.GetJObject(jToken["data"], "validate");
						}
					}
					jToken = await httpClientFactory.Post("https://member.bilibili.com/x/web/archive/delete", "aid=" + aid + "&geetest_challenge=" + challenge + "&geetest_validate=" + validate + "&geetest_seccode=" + validate + "%7Cjordan&success=1&csrf=" + Util.GetCookieByKey(cookie, "bili_jct"));
					if (Util.GetJObject(jToken, "code") == "0")
					{
						articles.Remove(query.First());
						Util.WriteLog("BiliBili", name, "稿件管理", "已成功删除稿件 " + bvid);
						return;
					}
					throw new Exception("删除失败:" + Util.GetJObject(jToken, "message"));
				}
				throw new Exception("打码失败:" + JsonConvert.SerializeObject(jObjectGeetest));
			}
			throw new Exception("删除失败:" + Util.GetJObject(jToken, "message"));
		}
		throw new Exception("删除失败:" + Util.GetJObject(jToken, "message"));
	}

	public static JObject GetFileList(string path, string type, string groupId, string search, int page = 1, int paseSize = 3)
	{
		path = Directory.GetCurrentDirectory() + $"\\wwwroot\\files\\{path}\\{type}\\{groupId}\\";
		if (!Directory.Exists(path))
		{
			Directory.CreateDirectory(path);
		}
		DirectoryInfo directoryInfo = new DirectoryInfo(path);
		FileInfo[] files = directoryInfo.GetFiles();
		int num = files.Length;
		files = files.Skip((page - 1) * paseSize).Take(paseSize).ToArray();
		JArray jArray = new JArray();
		FileInfo[] array = files;
		foreach (FileInfo fileInfo in array)
		{
			if (search == "" || fileInfo.Name.Contains(search))
			{
				string oldValue = (Directory.GetCurrentDirectory() + "\\wwwroot\\").ToLower();
				jArray.Add(new JObject
				{
					["id"] = Guid.NewGuid(),
					["fileName"] = fileInfo.Name,
					["url"] = fileInfo.FullName.ToLower().Replace(oldValue, "")
				});
			}
		}
		return new JObject
		{
			["total"] = num,
			["rows"] = jArray,
			["page"] = page,
			["pageSize"] = paseSize
		};
	}

	public static JToken GetConfig(string key, string areaId, int userId)
	{
		string sSql = " SELECT Fid FROM TCookies WHERE FKey='" + key + "'";
		string text = SQLHelper.BiliLocalDB.RunSqlStr(sSql);
		sSql = " SELECT FConfig FROM TArticles WHERE FUserId=" + userId + " AND FCookieId =" + text;
		string text2 = SQLHelper.BiliLocalDB.RunSqlStr(sSql);
		JObject jObject = new JObject
		{
			["FPrivate"] = 0,
			["FPublic"] = 0,
			["FInterval"] = 5,
			["FMp3"] = "随机",
			["FFfpmeg"] = ""
		};
		if (text2 != "")
		{
			JObject jToken = JObject.Parse(text2);
			if (Util.GetJObject(jToken, areaId) != "")
			{
				jObject = Util.GetJObject<JObject>(jToken, areaId) ?? new JObject();
			}
		}
		jObject["FCookieId"] = text;
		jObject["FAreaId"] = areaId;
		return jObject;
	}

	public static void SaveConfig(JObject jObject, int userId, string userName, string curTime)
	{
		string jObject2 = Util.GetJObject(jObject, "FCookieId");
		string jObject3 = Util.GetJObject(jObject, "FAreaId");
		string sSql = "SELECT ISNULL((SELECT Fid FROM TArticles WHERE FCookieId =" + jObject2 + "),0)";
		string text = SQLHelper.BiliLocalDB.RunSqlStr(sSql);
		sSql = " SELECT FConfig FROM TArticles WHERE FCookieId =" + jObject2;
		string text2 = SQLHelper.BiliLocalDB.RunSqlStr(sSql);
		JObject jObject4 = new JObject { [jObject3] = jObject };
		if (text2 != "")
		{
			jObject4 = JObject.Parse(text2);
			jObject4[jObject3] = jObject;
		}
		DataCURD.Save(new JObject
		{
			["Fid"] = text,
			["FCookieId"] = jObject2,
			["FUserId"] = userId,
			["FConfig"] = JsonConvert.SerializeObject(jObject4)
		}, "TArticles", "保存投稿生成视频配置", "Fid", userId, userName, curTime, SQLHelper.BiliLocalDB.InitCnn());
	}
}
