using System;
using System.Data;
using System.Threading.Tasks;
using API.BusService.DouYu;
using API.BusService.System;
using API.Common;
using API.DataAccess.DouYu;
using API.Models.Comm;
using Microsoft.AspNetCore.Mvc;

namespace API.Controllers.DouYu;

public class DouYuCookiesController : Controller
{
	[HttpPost]
	public async Task<Response> AddCookies([FromBody] Request model)
	{
		Response mRet = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "cookieNum");
			await DouYuCookies.AddCookies(jObject, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
			return mRet;
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}

	[HttpPost]
	public Response GetCookiesList([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectSearch, "search");
			string jObject2 = Util.GetJObject(model.jObjectSearch, "enable");
			string jObject3 = Util.GetJObject(model.jObjectSearch, "id");
			string jObject4 = Util.GetJObject(model.jObjectSearch, "areaId");
			DataTable cookiesList = DouYuCookies.GetCookiesList(jObject3, jObject4, jObject, jObject2, BusSysUser.Instance.User.Id, model.limit, model.offset, model.prop, model.order);
			response.data = Util.GetTableResponse(cookiesList);
			return response;
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
			return response;
		}
	}

	[HttpPost]
	public async Task<Response> ValidateCookie([FromBody] Request model)
	{
		Response mRet = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "FCookie");
			string jObject2 = Util.GetJObject(model.jObjectParam, "FHeaders");
			string jObject3 = Util.GetJObject(model.jObjectParam, "FProxyId");
			Response response = mRet;
			response.data = await BusDouYuCookies.ValidateCookie(jObject, jObject2, jObject3);
			return mRet;
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}

	[HttpPost]
	public async Task<Response> EditCookie([FromBody] Request model)
	{
		Response mRet = new Response();
		try
		{
			await DouYuCookies.EditCookie(model.jObjectParam, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
			return mRet;
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}

	[HttpPost]
	public async Task<Response> DelCookie([FromBody] Request model)
	{
		Response mRet = new Response();
		try
		{
			await DouYuCookies.DelCookie(model.jObjectParam, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
			return mRet;
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}

	[HttpPost]
	public async Task<Response> EmptyCookie([FromBody] Request model)
	{
		Response mRet = new Response();
		try
		{
			await DouYuCookies.EmptyCookie(model.jObjectParam, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
			return mRet;
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}

	[HttpPost]
	public Response Login([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "Fid");
			string jObject2 = Util.GetJObject(model.jObjectParam, "FKey");
			string jObject3 = Util.GetJObject(model.jObjectParam, "UserAgent");
			BusDouYuCookies.LoginDouYu(jObject, jObject2, base.HttpContext.Request.Headers.Cookie.ToString(), jObject3);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	public Response OpenChromium([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "Fid");
			string jObject2 = Util.GetJObject(model.jObjectParam, "FKey");
			string jObject3 = Util.GetJObject(model.jObjectParam, "UserAgent");
			BusDouYuCookies.OpenChromium(jObject, jObject2, base.HttpContext.Request.Headers.Cookie.ToString(), jObject3);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public async Task<Response> UpdateCookieExpires([FromBody] Request model)
	{
		Response mRet = new Response();
		try
		{
			await DouYuCookies.UpdateCookieExpires(BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}
}
