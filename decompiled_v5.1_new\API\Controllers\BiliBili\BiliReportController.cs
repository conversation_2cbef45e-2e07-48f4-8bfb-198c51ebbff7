using System;
using System.Data;
using API.BusService.System;
using API.Common;
using API.DataAccess.BiliBili;
using API.Models.Comm;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;

namespace API.Controllers.BiliBili;

public class BiliReportController : Controller
{
	[HttpPost]
	public Response GetReportList([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectSearch, "search");
			string jObject2 = Util.GetJObject(model.jObjectSearch, "areaId");
			string jObject3 = Util.GetJObject(model.jObjectSearch, "type");
			string jObject4 = Util.GetJObject(model.jObjectSearch, "export");
			JObject reportList = BiliReport.GetReportList(jObject, jObject2, jObject3, BusSysUser.Instance.User.Id, model.limit, model.offset, model.prop, model.order);
			if (jObject4 == "1")
			{
				DataTable dataTable = Util.GetJObject<JArray>(reportList, "rows")?.ToObject<DataTable>();
				if (dataTable != null)
				{
					Util.FileDownload(out string absolute, out string relative);
					ExcelHelper.X2003.TableToExcelForXLS(dataTable, absolute);
					response.data = relative;
				}
			}
			else
			{
				response.data = BiliReport.GetReportList(jObject, jObject2, jObject3, BusSysUser.Instance.User.Id, model.limit, model.offset, model.prop, model.order);
			}
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}
}
