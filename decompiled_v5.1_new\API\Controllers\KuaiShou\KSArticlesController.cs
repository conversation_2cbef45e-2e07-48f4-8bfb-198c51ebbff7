using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using API.Common;
using API.DataAccess.KuaiShou;
using API.Models.Comm;
using Microsoft.AspNetCore.Mvc;

namespace API.Controllers.KuaiShou;

public class KSArticlesController : Controller
{
	[HttpPost]
	public Response GetUserList([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectSearch, "search");
			response.data = KSArticles.GetUserList(jObject);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response GetFileList([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectSearch, "groupId");
			int jObject2 = Util.GetJObject<int>(model.jObjectSearch, "page");
			int jObject3 = Util.GetJObject<int>(model.jObjectSearch, "pageSize");
			string jObject4 = Util.GetJObject(model.jObjectSearch, "type");
			string jObject5 = Util.GetJObject(model.jObjectSearch, "keyword");
			string jObject6 = Util.GetJObject(model.jObjectSearch, "path");
			response.data = KSArticles.GetFileList(jObject6, jObject4, jObject, jObject5, jObject2, jObject3);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response DelFile([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			List<string> list = Util.GetJObject<List<string>>(model.jObjectParam, "array") ?? new List<string>();
			foreach (string item in list)
			{
				Util.FileDelete(Directory.GetCurrentDirectory() + "\\WWWRoot\\" + item);
			}
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response OpenFolder([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "path");
			string text = Directory.GetCurrentDirectory() + "\\wwwroot\\files\\" + jObject;
			if (!Directory.Exists(text))
			{
				Directory.CreateDirectory(text);
			}
			ProcessStartInfo startInfo = new ProcessStartInfo
			{
				Arguments = text,
				FileName = "explorer.exe"
			};
			Process.Start(startInfo);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}
}
