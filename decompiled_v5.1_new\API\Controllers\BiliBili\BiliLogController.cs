using System;
using API.BusService.System;
using API.Common;
using API.DataAccess.BiliBili;
using API.Models.Comm;
using Microsoft.AspNetCore.Mvc;

namespace API.Controllers.BiliBili;

public class BiliLogController : Controller
{
	[HttpPost]
	public Response WriteLog([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			BiliLog.WriteLog(model, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response GetPathList([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectSearch, "date");
			string jObject2 = Util.GetJObject(model.jObjectSearch, "path");
			response.data = BiliLog.GetPathList(jObject, jObject2);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response GetLogList([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectSearch, "path");
			response.data = BiliLog.GetLogList(jObject);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}
}
