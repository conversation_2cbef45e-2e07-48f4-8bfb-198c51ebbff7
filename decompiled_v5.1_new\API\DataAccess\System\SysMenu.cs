using System;
using System.Data;
using System.Linq;
using API.BusService.System;
using API.Common;
using Microsoft.Data.SqlClient;
using Newtonsoft.Json.Linq;

namespace API.DataAccess.System;

public class SysMenu
{
	public static JObject GetMenuList(string userId, out string rights)
	{
		rights = GetMenuString(userId).Trim(',');
		string text = " SELECT T1.Fid,T1.FParentId,T1.FName,T1.FPath,T1.FComponent,T1.FFredirect,T1.FActive";
		text += " ,T1.FTitle,T1.FTag,T1.FIcon,T1.FType,T1.FAffix,T1.FColor,T1.FHidden,T1.FHiddenBreadcrumb,T1.FFullpage";
		text += " ,T1.FEnable,T1.FSort";
		text += " FROM TSysMenu T1 WHERE T1.FEnable=1 AND T1.FType IN ('menu','iframe','link','button')";
		text = text + " AND T1.Fid IN (" + rights + ")";
		DataTable dataTable = SQLHelper.LocalDB.RunSqlDt(text);
		JArray menuChildList = BusSysMenu.GetMenuChildList(dataTable, 0L);
		DataRow[] array = dataTable.Select("FType='button'");
		string[] o = Array.Empty<string>();
		if (array.Length != 0)
		{
			o = (from row in array.CopyToDataTable().Rows.OfType<DataRow>()
				select Convert.ToString(row["FName"])).ToArray();
		}
		JArray value = new JArray { "welcome", "ver", "time", "progress", "echarts", "about", "localdb", "calendar", "notice" };
		return new JObject
		{
			["menu"] = menuChildList,
			["permissions"] = JArray.FromObject(o),
			["dashboardGrid"] = value
		};
	}

	public static JArray GetMenuList()
	{
		string text = " SELECT Fid, FParentId, FName, FTitle,FTag, FPath, FComponent, FFredirect, FHidden, FAffix, FIcon, FType";
		text += " ,FFullpage,FHiddenBreadcrumb, FActive, FColor, FDescribe, FSort, FEnable";
		text += "  FROM TSysMenu ";
		DataTable dtAll = SQLHelper.LocalDB.RunSqlDt(text);
		text = " SELECT * FROM TSysMenuApi";
		DataTable dtApiListAll = SQLHelper.LocalDB.RunSqlDt(text);
		return BusSysMenu.GetMenuChildList(dtAll, 0L, dtApiListAll);
	}

	public static string GetMenuString(string userId)
	{
		string sSql = " SELECT '110210,'+ FMenuId AS FMenuId FROM TSysUserGroup WHERE CHARINDEX('," + userId + ",',','+FUserId+',')>0 AND FMenuId!=''";
		DataTable dataTable = SQLHelper.LocalDB.RunSqlDt(sSql);
		sSql = "  SELECT STUFF((SELECT ','+ CAST(T1.Fid AS VARCHAR(100)) FROM TSysMenu T1 WHERE 1=1 AND T1.FEnable=1 ";
		sSql = sSql + " AND T1.Fid IN (SELECT T2.FMenuId FROM TSysRoleMenu T2 WHERE T2.FRoleId IN(SELECT T3.FRoleId FROM TSysRoleUser T3 WHERE T3.FUserId=" + userId + ")";
		if (dataTable.Rows.Count == 1)
		{
			sSql = sSql + " OR T1.Fid IN (" + Util.GetJObject(dataTable.Rows[0], "FMenuId") + ")";
		}
		sSql += " ) FOR XML PATH('')),1,1,'')";
		string text = SQLHelper.LocalDB.RunSqlStr(sSql);
		if (text == "")
		{
			throw new Exception("当前用户无任何菜单权限，请联系系统管理员！");
		}
		return SQLHelper.LocalDB.RunSqlStr(sSql);
	}

	public static void SaveMenu(JObject jObjectParam, string userName, int userId, string curTime, SqlCommand? pCmd = null)
	{
		SqlConnection sqlConnection = null;
		try
		{
			if (pCmd == null)
			{
				sqlConnection = SQLHelper.LocalDB.InitCnn();
				pCmd = sqlConnection.CreateCommand();
				pCmd.Transaction = sqlConnection.BeginTransaction();
			}
			JObject jObject = new JObject
			{
				["Fid"] = Util.GetJObject(jObjectParam, "id"),
				["FParentId"] = Util.GetJObject(jObjectParam, "parentId", "0"),
				["FName"] = Util.GetJObject(jObjectParam, "name"),
				["FPath"] = Util.GetJObject(jObjectParam, "path"),
				["FComponent"] = Util.GetJObject(jObjectParam, "component"),
				["FFredirect"] = Util.GetJObject(jObjectParam, "redirect"),
				["FActive"] = Util.GetJObject(jObjectParam, "active"),
				["FEnable"] = Util.GetJObject(jObjectParam, "enable"),
				["FTitle"] = Util.GetJObject(jObjectParam["meta"], "title"),
				["FTag"] = Util.GetJObject(jObjectParam["meta"], "tag"),
				["FIcon"] = Util.GetJObject(jObjectParam["meta"], "icon"),
				["FType"] = Util.GetJObject(jObjectParam["meta"], "type"),
				["FAffix"] = Util.GetJObject(jObjectParam["meta"], "affix"),
				["FHidden"] = Util.GetJObject(jObjectParam["meta"], "hidden"),
				["FHiddenBreadcrumb"] = Util.GetJObject(jObjectParam["meta"], "hiddenBreadcrumb"),
				["FColor"] = Util.GetJObject(jObjectParam["meta"], "color"),
				["FFullpage"] = Util.GetJObject(jObjectParam["meta"], "fullpage")
			};
			string jObject2 = Util.GetJObject(jObjectParam, "operateType");
			string jObject3 = Util.GetJObject(jObjectParam, "id");
			if (jObject2 == "0")
			{
				jObject["FSort"] = jObject3;
			}
			JArray jArray = (JArray)(jObjectParam["apiList"] ?? new JArray());
			string sSql = "SELECT 1 FROM TSysMenu WHERE Fid=" + jObject3;
			string text = SQLHelper.RunSqlStr(sSql, pCmd);
			if (text == "1" && jObject2 == "0")
			{
				throw new Exception("当前权限已存在！");
			}
			if (text == "")
			{
				jObject["Fid"] = "0";
				DataCURD.Save(jObject, "TSysMenu", "新增权限", "Fid", userId, userName, curTime, pCmd);
				sSql = " UPDATE TSysMenu SET Fid=" + jObject3 + " WHERE Fid=0; ";
				sSql = sSql + " INSERT INTO TSysRoleMenu (FRoleId,FMenuId) VALUES (1,'" + jObject3 + "')";
				SQLHelper.RunSqlText(sSql, pCmd);
			}
			else
			{
				DataCURD.Save(jObject, "TSysMenu", "编辑权限", "Fid", userId, userName, curTime, pCmd);
			}
			sSql = " DELETE TSysMenuApi WHERE FFid=" + jObject3;
			SQLHelper.RunSqlText(sSql, pCmd);
			for (int i = 0; i < jArray.Count; i++)
			{
				JObject jObject4 = jArray[i].ToObject<JObject>();
				if (jObject4 != null)
				{
					jObject4["Fid"] = 0;
					jObject4["FFid"] = jObject3;
					jObject4["FName"] = jObject4["name"];
					jObject4["FUrl"] = jObject4["url"];
					DataCURD.Save(jObject4, "TSysMenuApi", "保存控制器对照", "Fid", userId, userName, null, pCmd);
				}
			}
			if (sqlConnection != null)
			{
				pCmd.Transaction.Commit();
			}
		}
		catch (Exception ex)
		{
			if (sqlConnection != null)
			{
				pCmd?.Transaction.Rollback();
			}
			throw new Exception(ex.Message.ToString());
		}
		finally
		{
			if (sqlConnection != null)
			{
				sqlConnection.Close();
				sqlConnection.Dispose();
			}
		}
	}

	public static string GetMenuId(string parentId)
	{
		if (parentId == "0")
		{
			parentId = "";
		}
		string sSql = "  SELECT MAX(Fid)+10 FROM TSysMenu WHERE CAST( Fid AS NVARCHAR(100)) LIKE '" + parentId + "%' AND LEN(Fid)=" + (parentId.Length + 3) + " AND Fid NOT IN (990,999)";
		string text = SQLHelper.LocalDB.RunSqlStr(sSql);
		if (text == "")
		{
			text = parentId + "100";
		}
		return text;
	}

	public static void DeleteMenu(string id, string userName, int userId, string curTime, SqlCommand? pCmd = null)
	{
		SqlConnection sqlConnection = null;
		try
		{
			if (pCmd == null)
			{
				sqlConnection = SQLHelper.LocalDB.InitCnn();
				pCmd = sqlConnection.CreateCommand();
				pCmd.Transaction = sqlConnection.BeginTransaction();
			}
			string sSql = " SELECT COUNT(*) FROM TSysMenu WHERE FParentId IN (" + id + ")";
			string text = SQLHelper.RunSqlStr(sSql, pCmd);
			if (text != "0")
			{
				throw new Exception("子节点存在数据无法删除！");
			}
			string[] array = id.Split(',');
			for (int i = 0; i < array.Length; i++)
			{
				DataCURD.Delete("TSysMenu", "删除权限", "Fid", array[i], userId, userName, curTime, pCmd);
				DataCURD.Delete("TSysRoleMenu", "删除权限与角色对照", "FMenuId", array[i], userId, userName, curTime, pCmd);
				DataCURD.Delete("TSysMenuApi", "删除控制器对照", "FFid", array[i], userId, userName, curTime, pCmd);
			}
			if (sqlConnection != null)
			{
				pCmd.Transaction.Commit();
			}
		}
		catch (Exception ex)
		{
			if (sqlConnection != null)
			{
				pCmd?.Transaction.Rollback();
			}
			throw new Exception(ex.Message.ToString());
		}
		finally
		{
			if (sqlConnection != null)
			{
				sqlConnection.Close();
				sqlConnection.Dispose();
			}
		}
	}

	public static void SortMenu(string draggingNodeId, string dropNodeId, string dropType, string userName, int userId, string curTime)
	{
		string sSql = " SELECT FParentId FROM TSysMenu WHERE Fid=" + dropNodeId;
		string text = SQLHelper.LocalDB.RunSqlStr(sSql);
		sSql = " UPDATE TSysMenu SET FSort=TT.FSort FROM";
		sSql = sSql + " (SELECT Fid,ROW_NUMBER () OVER (ORDER BY FSort ASC )*20 AS FSort FROM TSysMenu WHERE FParentId=" + text + ") TT";
		sSql += " WHERE TSysMenu.Fid=TT.Fid";
		SQLHelper.LocalDB.RunSqlText(sSql);
		sSql = " SELECT FSort FROM TSysMenu WHERE Fid=" + dropNodeId;
		string s = SQLHelper.LocalDB.RunSqlStr(sSql);
		JObject jObject = new JObject { ["Fid"] = draggingNodeId };
		switch (dropType)
		{
		case "after":
			jObject["FSort"] = int.Parse(s) + 10;
			jObject["FParentId"] = text;
			break;
		case "before":
			jObject["FSort"] = int.Parse(s) - 10;
			jObject["FParentId"] = text;
			break;
		case "inner":
			jObject["FSort"] = 0;
			jObject["FParentId"] = dropNodeId;
			break;
		}
		DataCURD.Save(jObject, "TSysMenu", "保存排序", "Fid", userId, userName, curTime, SQLHelper.LocalDB.InitCnn());
	}

	public static bool IsExistsMenu(string controller, string action)
	{
		string sSql = "SELECT 1 FROM TSysMenuApi WHERE FUrl='/" + controller + "/" + action + "' AND FFid IN (0" + BusSysUser.Instance.User.Rights + "0)";
		return !(SQLHelper.LocalDB.RunSqlStr(sSql) == "");
	}
}
