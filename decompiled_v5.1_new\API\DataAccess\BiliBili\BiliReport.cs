using System;
using System.Collections.Generic;
using System.Data;
using System.Net;
using System.Net.Http;
using API.Common;
using Newtonsoft.Json.Linq;

namespace API.DataAccess.BiliBili;

public class BiliReport
{
	public static JObject GetReportList(string search, string areaId, string type, int userId, int limit = 0, int offset = 0, string prop = "", string order = "")
	{
		JArray jArray = new JArray();
		DataTable dataTable = new DataTable();
		switch (type)
		{
		case "receive":
		{
			string text = " SELECT FTaskName FROM TAreaTask WHERE FAreaId=" + areaId + " AND FDaily=0 ORDER BY FSort ";
			DataTable dataTable3 = SQLHelper.BiliLocalDB.RunSqlDt(text);
			text = " SELECT FName AS [账号名称]";
			jArray.Add("账号名称");
			for (int k = 0; k < dataTable3.Rows.Count; k++)
			{
				string jObject6 = Util.GetJObject(dataTable3.Rows[k], "FTaskName");
				text = text + " ,ISNULL(MAX(CASE FTaskName WHEN '" + jObject6 + "' THEN FCdkey END),'未更新') AS [" + jObject6 + "]";
				jArray.Add(jObject6);
			}
			text += " FROM (SELECT T2.FSort,T2.FName,T1.FTaskName,CASE WHEN T1.FReceiveStatus=3 THEN T3.FCdkey WHEN T1.FReceiveStatus=1 THEN '已完成' ELSE '' END AS FCdkey ";
			text += " FROM TCookiesTask T1";
			text += " LEFT JOIN TCookies T2 ON T2.Fid=T1.FCookieId AND T2.FUserId=T1.FUserId";
			text += " LEFT JOIN TCdkey T3 ON T3.FAreaId=T1.FAreaId AND T3.FAwardId=T1.FAwardId AND T3.FCookieId=T1.FCookieId AND T3.FUserId=T1.FUserId";
			text = text + " WHERE T1.FAreaId=" + areaId + " AND T1.FUserId=" + userId + " ) TT GROUP BY FName,FSort";
			if (prop == "")
			{
				prop = "FSort";
				order = "ASC";
			}
			dataTable = SQLHelper.BiliLocalDB.RunSqlDt(text, limit, offset, prop, order);
			if (search != "")
			{
				DataRow[] array = dataTable.Select("账号名称 LIKE '%" + search + "%'");
				if (array != null && array.Length != 0)
				{
					dataTable = array.CopyToDataTable();
				}
			}
			break;
		}
		case "date":
		{
			string text = " SELECT CAST(MONTH(T1.FDate) AS NVARCHAR(100))+'.'+CAST(DAY(T1.FDate) AS NVARCHAR(100)) AS FDate,COUNT(*) AS FCount";
			text = text + " FROM TCdkey T1 LEFT JOIN TAreaTask T2 ON T2.FAwardId=T1.FAwardId AND T2.FAreaId=T1.FAreaId WHERE T1.FAreaId=" + areaId + " AND T2.FDaily=0";
			text = text + " AND T1.FCookieId IN (SELECT Fid FROM TCookies WHERE FExpirationTime>GETDATE() AND FUserId=" + userId;
			if (search != "")
			{
				text = text + " AND FName LIKE '%" + search + "%'";
			}
			text += " )";
			text += " GROUP BY CONVERT(NVARCHAR(100),T1.FDate,112),CAST(MONTH(T1.FDate) AS NVARCHAR(100))+'.'+CAST(DAY(T1.FDate) AS NVARCHAR(100))";
			text += " ORDER BY CONVERT(NVARCHAR(100),T1.FDate,112)";
			dataTable = SQLHelper.BiliLocalDB.RunSqlDt(text);
			jArray.Add("任务名称");
			text = " SELECT TT.FSort,TT.FTaskId,TT.FTaskName AS [任务名称]";
			for (int num = dataTable.Rows.Count - 1; num >= 0; num--)
			{
				string jObject7 = Util.GetJObject(dataTable.Rows[num], "FDate");
				text = text + " ,SUM(CASE TT.FDate WHEN '" + jObject7 + "' THEN FCount ELSE 0 END) AS [" + jObject7 + "]";
				jArray.Add(jObject7);
			}
			text += " FROM ( SELECT T2.FSort,T2.FTaskId,T2.FTaskName,CAST(MONTH(T1.FDate) AS NVARCHAR(100))+'.'+CAST(DAY(T1.FDate) AS NVARCHAR(100)) AS FDate,COUNT(*) AS FCount";
			text = text + " FROM TCdkey T1 LEFT JOIN TAreaTask T2 ON T2.FAwardId=T1.FAwardId AND T2.FAreaId=T1.FAreaId WHERE T1.FAreaId=" + areaId + " AND T2.FDaily=0";
			text = text + " AND T1.FCookieId IN (SELECT Fid FROM TCookies WHERE FExpirationTime>GETDATE() AND FUserId=" + userId;
			if (search != "")
			{
				text = text + " AND FName LIKE '%" + search + "%'";
			}
			text += " )";
			text += " GROUP BY T2.FTaskId,T2.FTaskName,T2.FSort,CAST(MONTH(T1.FDate) AS NVARCHAR(100))+'.'+CAST(DAY(T1.FDate) AS NVARCHAR(100))";
			text += " ) TT GROUP BY TT.FSort,TT.FTaskId,TT.FTaskName ORDER BY TT.FSort";
			dataTable = SQLHelper.BiliLocalDB.RunSqlDt(text);
			break;
		}
		case "daily":
		{
			string text = " SELECT T1.Fid AS FCookieId,T1.FKey,T1.FName AS FCookieName,T1.FHeaders";
			text += " ,T2.FAddress AS FProxyAddress,T2.FUserName AS FProxyUserName,T2.FPassword AS FProxyPassword";
			text += " ,T1.*FROM TCookies T1";
			text = text + " LEFT JOIN TProxy T2 ON T2.FEnable=1 AND T2.Fid=T1.FProxyId AND T2.FUserId=" + userId;
			text = text + " LEFT JOIN TCookieArea T3 ON T3.FKey=T1.FKey AND T3.FAreaId=" + areaId + " AND T3.FUserId=" + userId;
			text = text + " WHERE T1.FUserId=" + userId + " AND ISNULL(T3.FExpirationTime,T1.FExpirationTime)>GETDATE() AND T1.FEnable=1";
			text = text + " AND (T1.FKey IN (SELECT FKey FROM TCookieArea WHERE FAreaId=" + areaId + " AND FUserId=" + userId + " AND FMileage=1) ";
			text = text + "      OR T1.FKey NOT IN (SELECT FKey FROM TCookieArea WHERE FUserId=" + userId + "))";
			if (search != "")
			{
				text = text + " AND T1.FName LIKE '%" + search + "%'";
			}
			text += " ORDER BY FSort";
			DataTable dataTable2 = SQLHelper.BiliLocalDB.RunSqlDt(text);
			text = " SELECT STUFF((SELECT ','+FTaskKey FROM TAreaTask WHERE FDaily=1 AND FAreaId=" + areaId + " FOR XML PATH('')),1,1,'')";
			string text2 = SQLHelper.BiliLocalDB.RunSqlStr(text);
			JArray jArray2 = new JArray();
			for (int i = 0; i < dataTable2.Rows.Count; i++)
			{
				DataRow dataRow = dataTable2.Rows[i];
				string name = dataRow["FKey"].ToString() ?? "";
				string cookie = dataRow["FCookie"].ToString() ?? "";
				string text3 = dataRow["FName"].ToString() ?? "";
				string header = dataRow["FHeaders"].ToString() ?? "";
				string text4 = dataRow["FCsrf"].ToString() ?? "";
				string text5 = dataRow["FProxyAddress"].ToString() ?? "";
				string userName = dataRow["FProxyUserName"].ToString() ?? "";
				string password = dataRow["FProxyPassword"].ToString() ?? "";
				HttpClientHandler defaultHandler = null;
				if (text5 != "")
				{
					defaultHandler = new HttpClientHandler
					{
						Proxy = new WebProxy
						{
							Address = new Uri(text5),
							Credentials = new NetworkCredential(userName, password)
						}
					};
				}
				Dictionary<string, string> dictionary = HttpClientFactory.FormataHeader(header, cookie);
				dictionary.Remove("Accept-Language");
				HttpClientFactory httpClientFactory = new HttpClientFactory(name, dictionary, defaultHandler);
				JObject result = httpClientFactory.Get("https://api.bilibili.com/x/task/totalv2?csrf=" + text4 + "&task_ids=" + text2 + "&web_location=888.117171").Result;
				if (!(Util.GetJObject(result, "code") == "0"))
				{
					continue;
				}
				JObject jObject = new JObject();
				jObject["账号名称"] = text3;
				JArray jArray3 = Util.GetJObject<JArray>(result["data"], "list") ?? new JArray();
				foreach (JToken item in jArray3)
				{
					string text6 = Util.GetJObject(item, "task_name").Replace("\t ", "");
					string jObject2 = Util.GetJObject(item, "task_status");
					JArray jObject3 = Util.GetJObject<JArray>(item, "indicators");
					if (jObject2 == "3")
					{
						jObject[text6] = "已领取";
					}
					else
					{
						if (jObject3 == null)
						{
							continue;
						}
						foreach (JToken item2 in jObject3)
						{
							int jObject4 = Util.GetJObject<int>(item2, "cur_value");
							limit = Util.GetJObject<int>(item2, "limit");
							string jObject5 = Util.GetJObject(jObject, text6);
							jObject5 = ((jObject5 == "") ? "" : (jObject5 + "，"));
							jObject[text6] = jObject5 + " " + ((jObject4 / limit == 1) ? "已完成" : (jObject4 + " / " + limit));
						}
					}
				}
				jArray2.Add(jObject);
			}
			dataTable = jArray2.ToObject<DataTable>() ?? new DataTable();
			for (int j = 0; j < dataTable.Columns.Count; j++)
			{
				string columnName = dataTable.Columns[j].ColumnName;
				jArray.Add(columnName);
			}
			break;
		}
		}
		return new JObject
		{
			["rows"] = JArray.FromObject(dataTable),
			["columns"] = jArray
		};
	}
}
