using System;
using System.Collections.Generic;
using System.Data;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using API.Common;
using API.Models.Comm;
using Microsoft.Data.SqlClient;
using Newtonsoft.Json.Linq;

namespace API.DataAccess.DouYu;

public class DouYuArea
{
	public static DataTable GetAreaList(string id, string organization, string search = "", int limit = 0, int offset = 0, string prop = "", string order = "")
	{
		string text = " SELECT *" + SQLHelper.total + " FROM TArea WHERE 1=1 ";
		if (organization != "")
		{
			text = text + " AND Fid IN (" + organization + ")";
		}
		if (id != "")
		{
			text = text + " AND Fid IN(" + id + ")";
		}
		if (search != "")
		{
			text = text + " AND FName LIKE '%" + search + "%'";
		}
		if (prop == "")
		{
			prop = "FSort";
			order = "ASC";
		}
		return SQLHelper.DouYuLocalDB.RunSqlDt(text, limit, offset, prop, order);
	}

	public static DataTable GetAreaActivityIdList(string areaId)
	{
		string text = " SELECT FActivityId1 AS value,'里程' AS label FROM TArea WHERE Fid=" + areaId + " AND FActivityId1!='' UNION ALL";
		text = text + " SELECT FActivityId2 AS value,'抽奖' AS label  FROM TArea WHERE Fid=" + areaId + " AND FActivityId2!='' UNION ALL";
		text = text + " SELECT FActivityId3 AS value,'活动' AS label  FROM TArea WHERE Fid=" + areaId + " AND FActivityId3!=''";
		return SQLHelper.DouYuLocalDB.RunSqlDt(text);
	}

	public static async Task SaveArea(JObject jObject, int userId, string userName, string curTime)
	{
		string jObject2 = Util.GetJObject(jObject, "mode");
		string id = Util.GetJObject(jObject, "Fid");
		jObject["FUpdateTime"] = curTime;
		string sSql;
		if (jObject2 == "add")
		{
			sSql = " SELECT 1 FROM TArea WHERE Fid=" + id;
			string text = SQLHelper.DouYuLocalDB.RunSqlStr(sSql);
			if (text == "1")
			{
				throw new Exception("当前分区已存在！");
			}
			jObject["Fid"] = 0;
			DataCURD.Save(jObject, "TArea", "新增分区", "Fid", userId, userName, curTime, SQLHelper.DouYuLocalDB.InitCnn());
			sSql = " UPDATE TArea SET Fid=" + id + " WHERE Fid=0";
			SQLHelper.DouYuLocalDB.RunSqlText(sSql);
		}
		else
		{
			DataCURD.Save(jObject, "TArea", "编辑分区", "Fid", userId, userName, curTime, SQLHelper.DouYuLocalDB.InitCnn());
		}
		string jObject3 = Util.GetJObject(jObject, "FActAlias");
		sSql = " DELETE TCdkey WHERE FAreaId=" + id + " AND FAlias NOT IN ('" + jObject3 + "')";
		SQLHelper.DouYuLocalDB.RunSqlText(sSql);
		string taskUrl = Util.GetJObject(jObject, "FTaskUrl");
		string jObject4 = Util.GetJObject(jObject, "FTaskId");
		DataTable cookiesList = DouYuCookies.GetCookiesList("", "", userId);
		if (cookiesList.Rows.Count == 0)
		{
			throw new Exception("请先添加账号！");
		}
		string jObject5 = Util.GetJObject(jObject, "FReferer");
		string jObject6 = Util.GetJObject(cookiesList.Rows[0], "FKey");
		string jObject7 = Util.GetJObject(cookiesList.Rows[0], "FCookie");
		string jObject8 = Util.GetJObject(cookiesList.Rows[0], "FHeaders");
		string jObject9 = Util.GetJObject(cookiesList.Rows[0], "FProxyAddress");
		string jObject10 = Util.GetJObject(cookiesList.Rows[0], "FProxyUserName");
		string jObject11 = Util.GetJObject(cookiesList.Rows[0], "FProxyPassword");
		HttpClientHandler defaultHandler = null;
		if (jObject9 != "")
		{
			defaultHandler = new HttpClientHandler
			{
				Proxy = new WebProxy
				{
					Address = new Uri(jObject9),
					Credentials = new NetworkCredential(jObject10, jObject11)
				}
			};
		}
		Dictionary<string, string> dictionary = HttpClientFactory.FormataHeader(jObject8, jObject7);
		dictionary["Referer"] = jObject5;
		dictionary["Host"] = "www.douyu.com";
		HttpClientFactory httpClientFactory = new HttpClientFactory(jObject6, dictionary, defaultHandler);
		JObject jToken = await httpClientFactory.Get(taskUrl + jObject4);
		if (Util.GetJObject(jToken, "error") == "0")
		{
			JArray jObject12 = Util.GetJObject<JArray>(jToken, "data");
			JArray jArray = new JArray();
			if (jObject12 != null)
			{
				foreach (JToken item2 in jObject12)
				{
					string text2 = "";
					string text3 = "";
					string text4 = "";
					string text5 = "";
					string text6 = "";
					JArray jArray2 = Util.GetJObject<JArray>(item2, "condCompleteList") ?? new JArray();
					if (jArray2.Count > 0)
					{
						text3 = Util.GetJObject(jArray2[0], "name");
					}
					JArray jArray3 = Util.GetJObject<JArray>(item2, "prizeInfo") ?? new JArray();
					if (jArray3.Count > 0)
					{
						text2 = Util.GetJObject(jArray3[0], "giftAlias");
						if (text3 == "")
						{
							text3 = Util.GetJObject(jArray3[0], "name");
						}
						text4 = Util.GetJObject(Util.GetJObject<JToken>(jArray3[0], "remain"), "remainDesc");
						JArray jArray4 = Util.GetJObject<JArray>(jArray3[0], "prizeInBag") ?? new JArray();
						if (jArray4.Count > 0)
						{
							text5 = Util.GetJObject(jArray4[0], "prizeId");
							text6 = Util.GetJObject(jArray4[0], "prizeName");
						}
					}
					text3 = text3.Replace(text6, "").Trim('-');
					JObject item = new JObject
					{
						["Fid"] = "0",
						["FAreaId"] = id,
						["FTaskUrl"] = taskUrl,
						["FTaskId"] = Util.GetJObject(item2, "taskId"),
						["FTaskName"] = text3,
						["FPrizeId"] = text5,
						["FPrizeName"] = text6,
						["FPrizeGiftAlias"] = text2,
						["FRemainDesc"] = text4,
						["FCompulsory"] = "0",
						["FComplete"] = "0",
						["FDaily"] = "0",
						["FSort"] = "0",
						["FEnable"] = "1"
					};
					jArray.Add(item);
				}
			}
			sSql = "DELETE TAreaTask WHERE FAreaId=" + id;
			SQLHelper.DouYuLocalDB.RunSqlText(sSql);
			DataTable dataTable = jArray.ToObject<DataTable>();
			if (dataTable != null && dataTable.Rows.Count > 0)
			{
				SQLHelper.DouYuLocalDB.SqlBulkCopyByDataTable("TAreaTask", dataTable);
			}
			return;
		}
		throw new Exception(Util.GetJObject(jToken, "msg"));
	}

	public static void DelArea(string id, int userId, string userName, string curTime)
	{
		SqlConnection sqlConnection = SQLHelper.DouYuLocalDB.InitCnn();
		SqlCommand sqlCommand = sqlConnection.CreateCommand();
		sqlCommand.Transaction = sqlConnection.BeginTransaction();
		try
		{
			DataCURD.Delete("TArea", "删除分区", "Fid", id, userId, userName, curTime, sqlCommand);
			DataCURD.Delete("TAreaTask", "删除分区任务", "FAreaId", id, userId, userName, curTime, sqlCommand);
			DataCURD.Delete("TCookiesTask", "删除Cookie与分区任务对照", "FAreaId", id, userId, userName, curTime, sqlCommand);
			DataCURD.Delete("TCdkey", "删除Cdkey", "FAreaId", id, userId, userName, curTime, sqlCommand);
			sqlCommand.Transaction.Commit();
		}
		catch (Exception ex)
		{
			sqlCommand.Transaction.Rollback();
			throw new Exception(ex.Message);
		}
		finally
		{
			sqlConnection.Close();
			sqlConnection.Dispose();
		}
	}

	public static JObject GetAreaInfo(Request request)
	{
		string jObject = Util.GetJObject(request.jObjectParam, "areaId");
		string sSql = " SELECT * FROM TSysOrganization WHERE Fid=3 OR FParentId=3 AND CHARINDEX(','+CAST(Fid AS NVARCHAR(100))+',',','+(SELECT FOrganizationId FROM TSysUser WHERE Fid=" + Util.GetJObject<int>(request.jObjectParam, "userId") + ")+',')>0";
		DataTable dataTable = SQLHelper.LocalDB.RunSqlDt(sSql);
		if (dataTable.Rows.Count != 1)
		{
			DataRow[] array = dataTable.Select("Fid=" + jObject);
			if (array.Length == 0)
			{
				throw new Exception("无权限！");
			}
		}
		sSql = " SELECT * FROM TArea WHERE Fid=" + jObject;
		DataTable dataTable2 = SQLHelper.DouYuLocalDB.RunSqlDt(sSql);
		if (dataTable2.Rows.Count == 0)
		{
			throw new Exception("当前游戏无信息，请自己维护！");
		}
		sSql = " SELECT * FROM TAreaTask WHERE FAreaId=" + jObject;
		DataTable o = SQLHelper.DouYuLocalDB.RunSqlDt(sSql);
		JArray jArray = JArray.FromObject(dataTable2);
		JObject jObject2 = (JObject)jArray[0];
		jObject2["Child"] = JArray.FromObject(o);
		return jObject2;
	}

	public static async Task UpdateAreaInfo(string areaId, string name, int userId, string userName, string curTime)
	{
		SqlConnection conn = SQLHelper.DouYuLocalDB.InitCnn();
		SqlCommand pCmd = conn.CreateCommand();
		pCmd.Transaction = conn.BeginTransaction();
		try
		{
			JObject jObjectParam = new JObject
			{
				["userId"] = userId,
				["areaId"] = areaId
			};
			Request request = new Request
			{
				jObjectParam = jObjectParam,
				curTime = curTime
			};
			object obj = await Util.Request("/Common/DouYuUpateAreaInfo", request);
			if (obj == null)
			{
				obj = GetAreaInfo(request);
			}
			jObjectParam = JObject.FromObject(obj);
			JArray jArray = (JArray)(jObjectParam["Child"] ?? new JArray());
			jObjectParam.Remove("Child");
			string text = jObjectParam["FUpdateTime"]?.ToString() ?? "";
			string sSql = " SELECT CASE CAST(FUpdateTime AS datetime) WHEN '" + text + "' THEN 1 ELSE 0 END FROM TArea WHERE Fid=" + areaId;
			string text2 = SQLHelper.RunSqlStr(sSql, pCmd);
			if (text2 == "1")
			{
				throw new Exception("已是最新版本！");
			}
			sSql = " IF( (SELECT 1 FROM TArea WHERE Fid=" + areaId + ") IS NULL) INSERT TArea (Fid,FName) VALUES (" + areaId + ",'" + name + "')";
			SQLHelper.RunSqlText(sSql, pCmd);
			DataCURD.Save(jObjectParam, "TArea", "同步分区", "Fid", userId, userName, curTime, pCmd);
			DataCURD.Delete("TAreaTask", "同步删除分区任务", "FAreaId", areaId, userId, userName, curTime, pCmd);
			for (int i = 0; i < jArray.Count; i++)
			{
				JObject jObject = (JObject)jArray[i];
				jObject["Fid"] = 0;
				DataCURD.Save(jObject, "TAreaTask", "同步分区任务", "Fid", userId, userName, curTime, pCmd);
			}
			DataCURD.Delete("TCdkey", "同步删除Cdkey信息", "FAreaId", areaId, userId, userName, curTime, pCmd);
			DataCURD.Delete("TCookiesTask", "同步删除Cookie任务信息", "FAreaId", areaId, userId, userName, curTime, pCmd);
			pCmd.Transaction.Commit();
		}
		catch (Exception ex)
		{
			pCmd.Transaction.Rollback();
			throw new Exception(ex.Message);
		}
		finally
		{
			conn.Close();
			conn.Dispose();
		}
	}
}
