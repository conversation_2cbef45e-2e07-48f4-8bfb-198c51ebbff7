using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;

namespace API.Common;

public class ExcelHelper
{
	public class X2003
	{
		public static DataTable ExcelToTableForXLS(string file)
		{
			DataTable dataTable = new DataTable();
			using FileStream s = new FileStream(file, FileMode.Open, FileAccess.Read);
			HSSFWorkbook hSSFWorkbook = new HSSFWorkbook(s);
			ISheet sheetAt = hSSFWorkbook.GetSheetAt(0);
			IRow row = sheetAt.GetRow(sheetAt.FirstRowNum);
			List<int> list = new List<int>();
			for (int i = 0; i < row.LastCellNum; i++)
			{
				object valueTypeForXLS = GetValueTypeForXLS(row.GetCell(i) as HSSFCell);
				if (valueTypeForXLS == null || valueTypeForXLS.ToString() == string.Empty)
				{
					dataTable.Columns.Add(new DataColumn("Columns" + i));
				}
				else
				{
					dataTable.Columns.Add(new DataColumn(valueTypeForXLS.ToString()));
				}
				list.Add(i);
			}
			for (int j = sheetAt.FirstRowNum + 1; j <= sheetAt.LastRowNum; j++)
			{
				DataRow dataRow = dataTable.NewRow();
				bool flag = false;
				foreach (int item in list)
				{
					dataRow[item] = GetValueTypeForXLS(sheetAt.GetRow(j).GetCell(item) as HSSFCell);
					if (dataRow[item] != null && dataRow[item].ToString() != string.Empty)
					{
						flag = true;
					}
				}
				if (flag)
				{
					dataTable.Rows.Add(dataRow);
				}
			}
			return dataTable;
		}

		public static DataTable ExcelToTableForXLS2(MemoryStream fs)
		{
			DataTable dataTable = new DataTable();
			HSSFWorkbook hSSFWorkbook = new HSSFWorkbook(fs);
			ISheet sheetAt = hSSFWorkbook.GetSheetAt(0);
			IRow row = sheetAt.GetRow(sheetAt.FirstRowNum);
			List<int> list = new List<int>();
			for (int i = 0; i < row.LastCellNum; i++)
			{
				object valueTypeForXLS = GetValueTypeForXLS(row.GetCell(i) as HSSFCell);
				if (valueTypeForXLS == null || valueTypeForXLS.ToString() == string.Empty)
				{
					dataTable.Columns.Add(new DataColumn("Columns" + i));
				}
				else
				{
					dataTable.Columns.Add(new DataColumn(valueTypeForXLS.ToString()));
				}
				list.Add(i);
			}
			for (int j = sheetAt.FirstRowNum + 1; j <= sheetAt.LastRowNum; j++)
			{
				DataRow dataRow = dataTable.NewRow();
				bool flag = false;
				foreach (int item in list)
				{
					dataRow[item] = GetValueTypeForXLS(sheetAt.GetRow(j).GetCell(item) as HSSFCell);
					if (dataRow[item] != null && dataRow[item].ToString() != string.Empty)
					{
						flag = true;
					}
				}
				if (flag)
				{
					dataTable.Rows.Add(dataRow);
				}
			}
			return dataTable;
		}

		public static void TableToExcelForXLS(DataTable dt, string file)
		{
			HSSFWorkbook hSSFWorkbook = new HSSFWorkbook();
			ISheet sheet = hSSFWorkbook.CreateSheet("Test");
			IRow row = sheet.CreateRow(0);
			for (int i = 0; i < dt.Columns.Count; i++)
			{
				sheet.SetColumnWidth(i, 5120.0);
				ICell cell = row.CreateCell(i);
				cell.SetCellValue(dt.Columns[i].ColumnName);
			}
			for (int j = 0; j < dt.Rows.Count; j++)
			{
				IRow row2 = sheet.CreateRow(j + 1);
				for (int k = 0; k < dt.Columns.Count; k++)
				{
					ICell cell2 = row2.CreateCell(k);
					cell2.SetCellValue(dt.Rows[j][k].ToString());
				}
			}
			MemoryStream memoryStream = new MemoryStream();
			hSSFWorkbook.Write(memoryStream);
			byte[] array = memoryStream.ToArray();
			using FileStream fileStream = new FileStream(file, FileMode.Create, FileAccess.Write);
			fileStream.Write(array, 0, array.Length);
			fileStream.Flush();
		}

		private static object GetValueTypeForXLS(HSSFCell? cell)
		{
			if (cell == null)
			{
				return "";
			}
			return cell.CellType switch
			{
				CellType.Blank => "", 
				CellType.Boolean => cell.BooleanCellValue, 
				CellType.Numeric => cell.NumericCellValue, 
				CellType.String => cell.StringCellValue, 
				CellType.Error => cell.ErrorCellValue, 
				_ => "=" + cell.CellFormula, 
			};
		}
	}

	public class X2007
	{
		public static DataTable ExcelToTableForXLSX(string file)
		{
			DataTable dataTable = new DataTable();
			using FileStream fileStream = new FileStream(file, FileMode.Open, FileAccess.Read);
			XSSFWorkbook xSSFWorkbook = new XSSFWorkbook(fileStream, readOnly: false);
			ISheet sheetAt = xSSFWorkbook.GetSheetAt(0);
			IRow row = sheetAt.GetRow(sheetAt.FirstRowNum);
			List<int> list = new List<int>();
			for (int i = 0; i < row.LastCellNum; i++)
			{
				object valueTypeForXLSX = GetValueTypeForXLSX(row.GetCell(i) as XSSFCell);
				if (valueTypeForXLSX == null || valueTypeForXLSX.ToString() == string.Empty)
				{
					dataTable.Columns.Add(new DataColumn("Columns" + i));
				}
				else
				{
					dataTable.Columns.Add(new DataColumn(valueTypeForXLSX.ToString()));
				}
				list.Add(i);
			}
			for (int j = sheetAt.FirstRowNum + 1; j <= sheetAt.LastRowNum; j++)
			{
				DataRow dataRow = dataTable.NewRow();
				bool flag = false;
				foreach (int item in list)
				{
					dataRow[item] = GetValueTypeForXLSX(sheetAt.GetRow(j).GetCell(item) as XSSFCell);
					if (dataRow[item] != null && dataRow[item].ToString() != string.Empty)
					{
						flag = true;
					}
				}
				if (flag)
				{
					dataTable.Rows.Add(dataRow);
				}
			}
			return dataTable;
		}

		public static DataTable ExcelToTableForXLSX2(MemoryStream fs)
		{
			DataTable dataTable = new DataTable();
			XSSFWorkbook xSSFWorkbook = new XSSFWorkbook(fs, readOnly: false);
			ISheet sheetAt = xSSFWorkbook.GetSheetAt(0);
			IRow row = sheetAt.GetRow(sheetAt.FirstRowNum);
			List<int> list = new List<int>();
			for (int i = 0; i < row.LastCellNum; i++)
			{
				object valueTypeForXLSX = GetValueTypeForXLSX(row.GetCell(i) as XSSFCell);
				if (valueTypeForXLSX == null || valueTypeForXLSX.ToString() == string.Empty)
				{
					dataTable.Columns.Add(new DataColumn("Columns" + i));
				}
				else
				{
					dataTable.Columns.Add(new DataColumn(valueTypeForXLSX.ToString()));
				}
				list.Add(i);
			}
			for (int j = sheetAt.FirstRowNum + 1; j <= sheetAt.LastRowNum; j++)
			{
				DataRow dataRow = dataTable.NewRow();
				bool flag = false;
				foreach (int item in list)
				{
					dataRow[item] = GetValueTypeForXLSX(sheetAt.GetRow(j).GetCell(item) as XSSFCell);
					if (dataRow[item] != null && dataRow[item].ToString() != string.Empty)
					{
						flag = true;
					}
				}
				if (flag)
				{
					dataTable.Rows.Add(dataRow);
				}
			}
			return dataTable;
		}

		public static void TableToExcelForXLSX(DataTable dt, string file)
		{
			XSSFWorkbook xSSFWorkbook = new XSSFWorkbook();
			ISheet sheet = xSSFWorkbook.CreateSheet("Sheet1");
			IRow row = sheet.CreateRow(0);
			for (int i = 0; i < dt.Columns.Count; i++)
			{
				ICell cell = row.CreateCell(i);
				cell.SetCellValue(dt.Columns[i].ColumnName);
			}
			for (int j = 0; j < dt.Rows.Count; j++)
			{
				IRow row2 = sheet.CreateRow(j + 1);
				for (int k = 0; k < dt.Columns.Count; k++)
				{
					ICell cell2 = row2.CreateCell(k);
					cell2.SetCellValue(dt.Rows[j][k].ToString());
				}
			}
			MemoryStream memoryStream = new MemoryStream();
			xSSFWorkbook.Write(memoryStream);
			byte[] array = memoryStream.ToArray();
			using FileStream fileStream = new FileStream(file, FileMode.Create, FileAccess.Write);
			fileStream.Write(array, 0, array.Length);
			fileStream.Flush();
		}

		private static object GetValueTypeForXLSX(XSSFCell? cell)
		{
			if (cell == null)
			{
				return "";
			}
			return cell.CellType switch
			{
				CellType.Blank => "", 
				CellType.Boolean => cell.BooleanCellValue, 
				CellType.Numeric => cell.NumericCellValue, 
				CellType.String => cell.StringCellValue, 
				CellType.Error => cell.ErrorCellValue, 
				_ => "=" + cell.CellFormula, 
			};
		}
	}

	public static DataTable GetDataTable(string filepath)
	{
		if (filepath.Last() == 's')
		{
			return X2003.ExcelToTableForXLS(filepath);
		}
		return X2007.ExcelToTableForXLSX(filepath);
	}
}
