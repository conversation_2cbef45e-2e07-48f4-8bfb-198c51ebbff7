using System;
using System.Diagnostics;
using System.IO;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Hosting;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Quartz;

namespace API.Common;

public class HostedService(IHostApplicationLifetime appLifetime, ISchedulerFactory schedulerFactory) : IHostedService
{
	private readonly IHostApplicationLifetime _appLifetime = appLifetime;

	private readonly ISchedulerFactory _schedulerFactory = schedulerFactory;

	public Task StartAsync(CancellationToken cancellationToken)
	{
		appLifetime.ApplicationStarted.Register(async delegate
		{
			await (await schedulerFactory.GetScheduler()).Start();
			ThreadPool.GetMaxThreads(out var workerThreads, out var completionPortThreads);
			ThreadPool.SetMaxThreads(workerThreads, completionPortThreads * 2);
			string val = AppSettings.GetVal("ApiUrl", "VueConfig");
			val = ((val == "") ? AppSettings.GetVal("Url", "Kestrel:Endpoints:Http") : val);
			string text = JsonConvert.SerializeObject(new JObject
			{
				["APP_NAME"] = AppSettings.GetVal("AppName", "VueConfig"),
				["API_URL"] = val,
				["APP_VER"] = AppSettings.GetVal("AppVer", "VueConfig"),
				["LS_ENCRYPTION_key"] = AppSettings.GetVal("AppKey", "VueConfig")
			});
			File.WriteAllText(Directory.GetCurrentDirectory() + "\\wwwroot\\config.js", "const APP_CONFIG = " + text, Encoding.UTF8);
			if (AppSettings.GetVal("ServerUrl") != "" && AppSettings.GetVal("Start") == "")
			{
				try
				{
					Process.Start(new ProcessStartInfo(val + "/#/login")
					{
						UseShellExecute = true
					});
				}
				catch (Exception ex)
				{
					Console.WriteLine(ex.Message);
				}
			}
		});
		appLifetime.ApplicationStopping.Register(delegate
		{
		});
		appLifetime.ApplicationStopped.Register(delegate
		{
		});
		return Task.CompletedTask;
	}

	public Task StopAsync(CancellationToken cancellationToken)
	{
		return Task.CompletedTask;
	}
}
