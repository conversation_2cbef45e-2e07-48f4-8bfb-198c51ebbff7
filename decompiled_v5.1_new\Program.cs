using System;
using System.Runtime.CompilerServices;
using System.Text.Encodings.Web;
using System.Text.Unicode;
using System.Threading.Tasks;
using API.CommClass;
using API.Common;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.StaticFiles;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json.Serialization;
using Quartz;
using Quartz.Impl;

[CompilerGenerated]
internal class Program
{
	private static void _003CMain_003E_0024(string[] args)
	{
		CustomConsole.Initialize();
		WebApplicationBuilder webApplicationBuilder = WebApplication.CreateBuilder(args);
		webApplicationBuilder.Services.AddControllers().AddNewtonsoftJson(delegate(MvcNewtonsoftJsonOptions options)
		{
			options.SerializerSettings.ContractResolver = new DefaultContractResolver();
			options.SerializerSettings.DateFormatString = "yyyy-MM-dd HH:mm:ss";
		});
		webApplicationBuilder.Services.AddEndpointsApiExplorer();
		webApplicationBuilder.Services.AddControllersWithViews().AddJsonOptions(delegate(JsonOptions options)
		{
			options.JsonSerializerOptions.Encoder = JavaScriptEncoder.Create(UnicodeRanges.All);
		});
		webApplicationBuilder.Services.AddSession(delegate(SessionOptions options)
		{
			options.Cookie.Name = "token";
			options.IdleTimeout = TimeSpan.FromSeconds(int.Parse(AppSettings.GetVal("IdleTimeout")));
			options.Cookie.HttpOnly = false;
		});
		webApplicationBuilder.Services.AddMvc(delegate(MvcOptions options)
		{
			options.Filters.Add<SecurityFilter>();
		});
		webApplicationBuilder.Services.AddHostedService<HostedService>();
		webApplicationBuilder.Services.AddSingleton<ISchedulerFactory, StdSchedulerFactory>();
		webApplicationBuilder.Services.Configure(delegate(FormOptions x)
		{
			x.MultipartBodyLengthLimit = 4294967296L;
		});
		WebApplication webApplication = webApplicationBuilder.Build();
		webApplication.UseHttpsRedirection();
		webApplication.UseAuthorization();
		webApplication.UseSession();
		webApplication.MapControllerRoute("default", "{controller}/{action}/{id?}");
		webApplication.UseDefaultFiles();
		webApplication.Use(async delegate(HttpContext context, Func<Task> next)
		{
			if (context.Request.Headers.ContainsKey("X-Forwarded-For"))
			{
				context.Request.Headers.Remove("X-Forwarded-For");
			}
			if (context.Request.Headers.ContainsKey("X-Forwarded-Proto"))
			{
				context.Request.Headers.Remove("X-Forwarded-Proto");
			}
			if (context.Request.Headers.ContainsKey("X-Forwarded-Host"))
			{
				context.Request.Headers.Remove("X-Forwarded-Host");
			}
			if (context.Request.Headers.ContainsKey("X-Forwarded-Port"))
			{
				context.Request.Headers.Remove("X-Forwarded-Port");
			}
			if (context.Request.Headers.ContainsKey("X-Original-Host"))
			{
				context.Request.Headers.Remove("X-Original-Host");
			}
			if (context.Request.Headers.ContainsKey("X-Real-Ip"))
			{
				context.Request.Headers.Remove("X-Real-Ip");
			}
			await next();
		});
		StaticFileOptions staticFileOptions = new StaticFileOptions();
		FileExtensionContentTypeProvider fileExtensionContentTypeProvider = new FileExtensionContentTypeProvider();
		fileExtensionContentTypeProvider.Mappings[".xls"] = "application/vnd.ms-excel";
		fileExtensionContentTypeProvider.Mappings[".xlsx"] = "application/vnd.ms-excel";
		staticFileOptions.ContentTypeProvider = fileExtensionContentTypeProvider;
		webApplication.UseStaticFiles(staticFileOptions);
		webApplication.Run();
	}
}
