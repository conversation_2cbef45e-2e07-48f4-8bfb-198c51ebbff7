using System;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using API.DataAccess.System;
using Microsoft.Extensions.Caching.Memory;

namespace API.Common;

public class Token
{
	public static string GetTokenValue(string userId)
	{
		string val = AppSettings.GetVal("TokenTimeOut");
		string text = Guid.NewGuid().ToString().ToUpper()
			.Replace("-", "");
		string dateStr = GetDateStr(DateTime.Now.AddSeconds(double.Parse(val)).ToString("yyyyMMddHHmmss"));
		userId = GetDateStr(userId);
		return string.Concat(text.AsSpan(0, 12), dateStr, text.AsSpan(12, text.Length - 12), userId);
	}

	public static double GetUTC_TimeLen()
	{
		return DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
	}

	public static bool DoValidateNonce(string token, string nonce, MemoryCache memoryCache)
	{
		string s = "300";
		string text = token + nonce;
		object obj = memoryCache.Get(text);
		if (obj == null)
		{
			memoryCache.Set(text, text, TimeSpan.FromSeconds(double.Parse(s)));
			return true;
		}
		return false;
	}

	public static bool DoValidateSign(string timestamp, string token, string nonce, string signature, string @params)
	{
		string val = AppSettings.GetVal("TokenKey");
		string source = timestamp + token + val + nonce + @params;
		string s = string.Concat(source.OrderBy((char c) => c));
		byte[] bytes = Encoding.UTF8.GetBytes(s);
		byte[] array = MD5.HashData(bytes);
		StringBuilder stringBuilder = new StringBuilder();
		byte[] array2 = array;
		foreach (byte b in array2)
		{
			stringBuilder.Append(b.ToString("X2"));
		}
		return stringBuilder.ToString().Equals(signature, StringComparison.CurrentCultureIgnoreCase);
	}

	public static string CreateValidateSign(string timestamp, string token, string nonce, string @params)
	{
		string val = AppSettings.GetVal("TokenKey");
		string source = timestamp + token + val + nonce + @params;
		string s = string.Concat(source.OrderBy((char c) => c));
		byte[] bytes = Encoding.UTF8.GetBytes(s);
		byte[] array = MD5.HashData(bytes);
		StringBuilder stringBuilder = new StringBuilder();
		byte[] array2 = array;
		foreach (byte b in array2)
		{
			stringBuilder.Append(b.ToString("X2"));
		}
		return stringBuilder.ToString();
	}

	public static bool DoValidateEnable(string token, string tokenPrefix)
	{
		if (token.Length > 14)
		{
			string date = token.Replace(tokenPrefix, "").Substring(12, 14);
			DateTime? strDate = GetStrDate(date);
			if (strDate.HasValue)
			{
				if (strDate < DateTime.Now)
				{
					return false;
				}
				return true;
			}
			return false;
		}
		return false;
	}

	public static bool DoValidateUserRight(string controller, string action)
	{
		return SysMenu.IsExistsMenu(controller, action);
	}

	public static string GetUserId(string token, string tokenPrefix)
	{
		string text = token.Replace(tokenPrefix, "");
		string str = text.Substring(46, text.Length - 46);
		return GetStrDate1(str);
	}

	public static string GetDateStr(string date)
	{
		date = date.Replace("0", "H").Replace("1", "@").Replace("2", "G")
			.Replace("3", "!")
			.Replace("4", "(")
			.Replace("5", "]");
		date = date.Replace("6", "A").Replace("7", "U").Replace("8", "P")
			.Replace("9", "R");
		return date;
	}

	public static DateTime? GetStrDate(string date)
	{
		date = date.Replace("H", "0").Replace("@", "1").Replace("G", "2")
			.Replace("!", "3")
			.Replace("(", "4")
			.Replace("]", "5");
		date = date.Replace("A", "6").Replace("U", "7").Replace("P", "8")
			.Replace("R", "9");
		string text = date.Substring(0, 4);
		string text2 = date.Substring(4, 2);
		string text3 = date.Substring(6, 2);
		string text4 = date.Substring(8, 2);
		string text5 = date.Substring(10, 2);
		string text6 = date.Substring(12, 2);
		string s = text + "-" + text2 + "-" + text3 + " " + text4 + ":" + text5 + ":" + text6;
		if (DateTime.TryParse(s, out var result))
		{
			return result;
		}
		return null;
	}

	public static string GetStrDate1(string str)
	{
		str = str.Replace("H", "0").Replace("@", "1").Replace("G", "2")
			.Replace("!", "3")
			.Replace("(", "4")
			.Replace("]", "5");
		str = str.Replace("A", "6").Replace("U", "7").Replace("P", "8")
			.Replace("R", "9");
		return str;
	}
}
