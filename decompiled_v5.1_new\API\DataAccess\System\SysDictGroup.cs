using System;
using System.Data;
using API.Common;
using Microsoft.Data.SqlClient;
using Newtonsoft.Json.Linq;

namespace API.DataAccess.System;

public class SysDictGroup
{
	public static DataTable GetSysDictGroupList(string search, string enable, string isSys, string userRight, int limit = 0, int offset = 0, string prop = "", string order = "")
	{
		string text = " SELECT Fid, FName, FEnable, FSort, FSys " + SQLHelper.total + " FROM TSysDictGroup";
		text += " WHERE 1=1";
		if (search != "")
		{
			text = text + " AND FName LIKE '%" + search + "%'";
		}
		if (enable != "")
		{
			text = text + " AND FEnable=" + enable;
		}
		if (isSys != "" && !userRight.Contains(",990140130,"))
		{
			text += " AND FSys=0";
		}
		if (prop == "")
		{
			prop = "FSort";
			order = "ASC";
		}
		return SQLHelper.LocalDB.RunSqlDt(text, limit, offset, prop, order);
	}

	public static void DoDictGroupSave(JObject jObject, string userNmae, int userId, string userRight, string curTime, SqlCommand? pCmd = null)
	{
		SqlConnection sqlConnection = null;
		try
		{
			if (pCmd == null)
			{
				sqlConnection = SQLHelper.LocalDB.InitCnn();
				pCmd = sqlConnection.CreateCommand();
				pCmd.Transaction = sqlConnection.BeginTransaction();
			}
			string jObject2 = Util.GetJObject(jObject, "Fid");
			string jObject3 = Util.GetJObject(jObject, "FName");
			string sSql = " SELECT 1 FROM TSysDictGroup WHERE FName ='" + jObject3 + "' AND Fid!=" + jObject2;
			string text = SQLHelper.RunSqlStr(sSql, pCmd);
			if (text == "1")
			{
				throw new Exception("字典组名称重复");
			}
			string jObject4 = Util.GetJObject(jObject, "FSys");
			if (jObject4 == "1" && !userRight.Contains(",990140130,"))
			{
				throw new Exception("暂无权限");
			}
			DataCURD.Save(jObject, "TSysDictGroup", "保存字典分组", "Fid", userId, userNmae, curTime, pCmd);
			if (sqlConnection != null)
			{
				pCmd.Transaction.Commit();
			}
		}
		catch (Exception ex)
		{
			if (sqlConnection != null)
			{
				pCmd?.Transaction.Rollback();
			}
			throw new Exception(ex.Message.ToString());
		}
		finally
		{
			if (sqlConnection != null)
			{
				sqlConnection.Close();
				sqlConnection.Dispose();
			}
		}
	}

	public static string DoDelDictGroup(string id, string userName, int userId, string userRight, string curTime, SqlCommand? pCmd = null)
	{
		SqlConnection sqlConnection = null;
		try
		{
			if (pCmd == null)
			{
				sqlConnection = SQLHelper.LocalDB.InitCnn();
				pCmd = sqlConnection.CreateCommand();
				pCmd.Transaction = sqlConnection.BeginTransaction();
			}
			string sSql = "SELECT Fid,FSys FROM TSysDictGroup WHERE Fid=" + id;
			DataTable dataTable = SQLHelper.RunSqlDt(sSql, pCmd);
			if (dataTable.Rows.Count == 1)
			{
				if (dataTable.Rows[0]["FSys"].ToString() == "1" && !userRight.Contains(",990140130,"))
				{
					throw new Exception("暂无权限");
				}
				sSql = " SELECT COUNT(*) FROM TSysDictionary WHERE FGroupId=" + id;
				string text = SQLHelper.RunSqlStr(sSql, pCmd);
				string result;
				if (text == "0")
				{
					DataCURD.Delete("TSysDictGroup", "删除字典分组", "Fid", id, userId, userName, curTime, pCmd);
					result = "字典组已删除！";
				}
				else
				{
					JArray jArray = JArray.FromObject(dataTable);
					JObject jObject = (JObject)jArray[0];
					jObject["FEnable"] = 0;
					DataCURD.Save(jObject, "TSysDictGroup", "禁用字典分组", "Fid", userId, userName, curTime, pCmd);
					result = "字典组已禁用！";
				}
				if (sqlConnection != null)
				{
					pCmd.Transaction.Commit();
				}
				return result;
			}
			throw new Exception("未找到记录！");
		}
		catch (Exception ex)
		{
			if (sqlConnection != null)
			{
				pCmd?.Transaction.Rollback();
			}
			throw new Exception(ex.Message.ToString());
		}
		finally
		{
			if (sqlConnection != null)
			{
				sqlConnection.Close();
				sqlConnection.Dispose();
			}
		}
	}
}
