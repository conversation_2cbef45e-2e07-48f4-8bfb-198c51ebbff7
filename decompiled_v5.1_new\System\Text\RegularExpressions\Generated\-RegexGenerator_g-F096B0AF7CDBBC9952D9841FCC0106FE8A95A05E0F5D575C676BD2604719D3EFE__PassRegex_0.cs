using System.CodeDom.Compiler;
using System.Runtime.CompilerServices;

namespace System.Text.RegularExpressions.Generated;

[GeneratedCode("System.Text.RegularExpressions.Generator", "8.0.10.46610")]
[SkipLocalsInit]
internal sealed class _003CRegexGenerator_g_003EF096B0AF7CDBBC9952D9841FCC0106FE8A95A05E0F5D575C676BD2604719D3EFE__PassRegex_0 : Regex
{
	private sealed class RunnerFactory : RegexRunnerFactory
	{
		private sealed class Runner : RegexRunner
		{
			protected override void Scan(ReadOnlySpan<char> inputSpan)
			{
				while (TryFindNextPossibleStartingPosition(inputSpan) && !TryMatchAtCurrentPosition(inputSpan) && runtextpos != inputSpan.Length)
				{
					runtextpos++;
					if (_003CRegexGenerator_g_003EF096B0AF7CDBBC9952D9841FCC0106FE8A95A05E0F5D575C676BD2604719D3EFE__Utilities.s_hasTimeout)
					{
						CheckTimeout();
					}
				}
			}

			private bool TryFindNextPossibleStartingPosition(ReadOnlySpan<char> inputSpan)
			{
				int num = runtextpos;
				if (num <= inputSpan.Length - 8)
				{
					ReadOnlySpan<char> readOnlySpan = inputSpan.Slice(num);
					for (int i = 0; i < readOnlySpan.Length; i++)
					{
						if (readOnlySpan[i] != '\n')
						{
							runtextpos = num + i;
							return true;
						}
					}
				}
				runtextpos = inputSpan.Length;
				return false;
			}

			private bool TryMatchAtCurrentPosition(ReadOnlySpan<char> inputSpan)
			{
				int num = runtextpos;
				int start = num;
				int num2 = 0;
				int num3 = 0;
				int num4 = 0;
				int num5 = 0;
				int num6 = 0;
				int num7 = 0;
				int num8 = 0;
				int num9 = 0;
				int num10 = 0;
				int num11 = 0;
				int num12 = 0;
				int num13 = 0;
				int num14 = 0;
				int num15 = 0;
				ReadOnlySpan<char> span = inputSpan.Slice(num);
				int num16 = num;
				if (_003CRegexGenerator_g_003EF096B0AF7CDBBC9952D9841FCC0106FE8A95A05E0F5D575C676BD2604719D3EFE__Utilities.s_hasTimeout)
				{
					CheckTimeout();
				}
				int num17 = num15;
				num7 = num;
				int num18 = span.IndexOf('\n');
				if (num18 < 0)
				{
					num18 = span.Length;
				}
				span = span.Slice(num18);
				num += num18;
				num8 = num;
				while (true)
				{
					num3 = Crawlpos();
					if (!span.IsEmpty && char.IsAsciiDigit(span[0]))
					{
						break;
					}
					UncaptureUntil(num3);
					if (_003CRegexGenerator_g_003EF096B0AF7CDBBC9952D9841FCC0106FE8A95A05E0F5D575C676BD2604719D3EFE__Utilities.s_hasTimeout)
					{
						CheckTimeout();
					}
					if (num7 >= num8 || (num8 = inputSpan.Slice(num7, num8 - num7).LastIndexOfAnyInRange('0', '9')) < 0)
					{
						UncaptureUntil(0);
						return false;
					}
					num8 += num7;
					num = num8;
					span = inputSpan.Slice(num);
				}
				num15 = num17;
				num = num16;
				span = inputSpan.Slice(num);
				int num19 = num;
				if (_003CRegexGenerator_g_003EF096B0AF7CDBBC9952D9841FCC0106FE8A95A05E0F5D575C676BD2604719D3EFE__Utilities.s_hasTimeout)
				{
					CheckTimeout();
				}
				int num20 = num15;
				num9 = num;
				int num21 = span.IndexOf('\n');
				if (num21 < 0)
				{
					num21 = span.Length;
				}
				span = span.Slice(num21);
				num += num21;
				num10 = num;
				while (true)
				{
					num4 = Crawlpos();
					if (!span.IsEmpty && char.IsAsciiLetterLower(span[0]))
					{
						break;
					}
					UncaptureUntil(num4);
					if (_003CRegexGenerator_g_003EF096B0AF7CDBBC9952D9841FCC0106FE8A95A05E0F5D575C676BD2604719D3EFE__Utilities.s_hasTimeout)
					{
						CheckTimeout();
					}
					if (num9 >= num10 || (num10 = inputSpan.Slice(num9, num10 - num9).LastIndexOfAnyInRange('a', 'z')) < 0)
					{
						UncaptureUntil(0);
						return false;
					}
					num10 += num9;
					num = num10;
					span = inputSpan.Slice(num);
				}
				num15 = num20;
				num = num19;
				span = inputSpan.Slice(num);
				int num22 = num;
				if (_003CRegexGenerator_g_003EF096B0AF7CDBBC9952D9841FCC0106FE8A95A05E0F5D575C676BD2604719D3EFE__Utilities.s_hasTimeout)
				{
					CheckTimeout();
				}
				int num23 = num15;
				num11 = num;
				int num24 = span.IndexOf('\n');
				if (num24 < 0)
				{
					num24 = span.Length;
				}
				span = span.Slice(num24);
				num += num24;
				num12 = num;
				while (true)
				{
					num5 = Crawlpos();
					if (!span.IsEmpty && char.IsAsciiLetterUpper(span[0]))
					{
						break;
					}
					UncaptureUntil(num5);
					if (_003CRegexGenerator_g_003EF096B0AF7CDBBC9952D9841FCC0106FE8A95A05E0F5D575C676BD2604719D3EFE__Utilities.s_hasTimeout)
					{
						CheckTimeout();
					}
					if (num11 >= num12 || (num12 = inputSpan.Slice(num11, num12 - num11).LastIndexOfAnyInRange('A', 'Z')) < 0)
					{
						UncaptureUntil(0);
						return false;
					}
					num12 += num11;
					num = num12;
					span = inputSpan.Slice(num);
				}
				num15 = num23;
				num = num22;
				span = inputSpan.Slice(num);
				int num25 = num;
				if (_003CRegexGenerator_g_003EF096B0AF7CDBBC9952D9841FCC0106FE8A95A05E0F5D575C676BD2604719D3EFE__Utilities.s_hasTimeout)
				{
					CheckTimeout();
				}
				int num26 = num15;
				num2 = num;
				num13 = num;
				int num27 = span.IndexOfAnyExceptInRange('!', '~');
				if (num27 < 0)
				{
					num27 = span.Length;
				}
				if (num27 == 0)
				{
					UncaptureUntil(0);
					return false;
				}
				span = span.Slice(num27);
				num += num27;
				num14 = num;
				num13++;
				while (true)
				{
					num6 = Crawlpos();
					Capture(1, num2, num);
					if (!span.IsEmpty && !char.IsAsciiLetterOrDigit(span[0]))
					{
						break;
					}
					UncaptureUntil(num6);
					if (_003CRegexGenerator_g_003EF096B0AF7CDBBC9952D9841FCC0106FE8A95A05E0F5D575C676BD2604719D3EFE__Utilities.s_hasTimeout)
					{
						CheckTimeout();
					}
					if (num13 >= num14 || (num14 = inputSpan.Slice(num13, num14 - num13).LastIndexOfAnyExcept(_003CRegexGenerator_g_003EF096B0AF7CDBBC9952D9841FCC0106FE8A95A05E0F5D575C676BD2604719D3EFE__Utilities.s_asciiLettersAndDigits)) < 0)
					{
						UncaptureUntil(0);
						return false;
					}
					num14 += num13;
					num = num14;
					span = inputSpan.Slice(num);
				}
				num15 = num26;
				num = num25;
				span = inputSpan.Slice(num);
				int i;
				for (i = 0; i < 18 && (uint)i < (uint)span.Length && span[i] != '\n'; i++)
				{
				}
				if (i < 8)
				{
					UncaptureUntil(0);
					return false;
				}
				span = span.Slice(i);
				Capture(0, start, runtextpos = num + i);
				return true;
				[MethodImpl(MethodImplOptions.AggressiveInlining)]
				void UncaptureUntil(int capturePosition)
				{
					while (Crawlpos() > capturePosition)
					{
						Uncapture();
					}
				}
			}
		}

		protected override RegexRunner CreateInstance()
		{
			return new Runner();
		}
	}

	internal static readonly _003CRegexGenerator_g_003EF096B0AF7CDBBC9952D9841FCC0106FE8A95A05E0F5D575C676BD2604719D3EFE__PassRegex_0 Instance = new _003CRegexGenerator_g_003EF096B0AF7CDBBC9952D9841FCC0106FE8A95A05E0F5D575C676BD2604719D3EFE__PassRegex_0();

	private _003CRegexGenerator_g_003EF096B0AF7CDBBC9952D9841FCC0106FE8A95A05E0F5D575C676BD2604719D3EFE__PassRegex_0()
	{
		pattern = "(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=([\\x21-\\x7e]+)[^a-zA-Z0-9]).{8,18}";
		roptions = RegexOptions.Multiline | RegexOptions.IgnorePatternWhitespace;
		Regex.ValidateMatchTimeout(_003CRegexGenerator_g_003EF096B0AF7CDBBC9952D9841FCC0106FE8A95A05E0F5D575C676BD2604719D3EFE__Utilities.s_defaultTimeout);
		internalMatchTimeout = _003CRegexGenerator_g_003EF096B0AF7CDBBC9952D9841FCC0106FE8A95A05E0F5D575C676BD2604719D3EFE__Utilities.s_defaultTimeout;
		factory = new RunnerFactory();
		capsize = 2;
	}
}
