using System.Data;
using System.Linq;
using API.BusService.System;
using API.Common;
using Newtonsoft.Json.Linq;

namespace API.DataAccess.System;

public class SysOrganization
{
	public static DataTable GetSysOrganizationList(string search, string enable, int limit = 0, int offset = 0, string prop = "", string order = "")
	{
		string text = " SELECT Fid, FParentId, FName, FType ,FSort,FMileage,FAricles " + SQLHelper.total + " FROM TSysOrganization  WHERE FIsDelete=0";
		if (search != "")
		{
			text = text + " AND (FCode LIKE '%" + search + "% OR FName LIKE '%" + search + "%'')";
		}
		if (enable != "")
		{
			text = text + " AND FEnable=" + enable;
		}
		if (prop == "")
		{
			prop = "FSort";
			order = "ASC";
		}
		return SQLHelper.LocalDB.RunSqlDt(text, limit, offset, prop, order);
	}

	public static JObject GetMyOrganization(string id)
	{
		id = ((id == "") ? "0" : id);
		string text = " WITH CTE AS (SELECT Fid,FParentId,FName,FType,FSort,FMileage,FAricles FROM TSysOrganization WHERE Fid IN(" + id + ") UNION ALL ";
		text += " SELECT T1.Fid,T1.FParentId,T1.FName,T1.FType,T1.FSort,T1.FMileage,T1.FAricles FROM TSysOrganization T1 INNER JOIN CTE ON CTE.FParentId =T1.Fid)";
		text += " SELECT Fid,FParentId,FName,FType,FSort,FMileage,FAricles FROM CTE GROUP BY  Fid,FParentId,FName,FType,FSort,FMileage,FAricles";
		DataTable dtAll = SQLHelper.LocalDB.RunSqlDt(text);
		JArray sysOrganizationChild = BusSysOrganization.GetSysOrganizationChild(dtAll, 0);
		JObject jObject = new JObject();
		foreach (JToken item in sysOrganizationChild)
		{
			string jObject2 = Util.GetJObject(item, "label");
			jObject[jObject2] = string.Join(",", (item["children"] ?? new JArray()).Select((JToken token) => token["value"]?.ToString()).ToArray());
		}
		return jObject;
	}
}
