using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using API.BusService.System;
using API.Common;
using API.DataAccess.BiliBili;
using API.Models.Comm;
using API.Quartz;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace API.BusService.BiliBili;

public class BusBiliCookies
{
	public static JObject ValidateCookie(string cookie, string headers)
	{
		return new JObject
		{
			["FIdentifying"] = Util.GetCookieByKey(cookie, "DedeUserID"),
			["FCsrf"] = Util.GetCookieByKey(cookie, "bili_jct"),
			["FHeaders"] = JsonConvert.SerializeObject(JObject.Parse(headers))
		};
	}

	public static void AddCookieStatus(ConcurrentBag<JToken> bagUser, string cookieId, string status)
	{
		bagUser.Add(new JObject
		{
			["Fid"] = cookieId,
			["FStatus"] = status
		});
	}

	public static void UpdateCookieStatus(ConcurrentBag<JToken> bagUser, int userId)
	{
		foreach (JToken item in bagUser)
		{
			JObject jObject = (JObject)item;
			DataCURD.Save(jObject, "TCookies", "保存账号状态", "Fid", userId, "定时任务", null, SQLHelper.BiliLocalDB.InitCnn());
		}
	}

	public static void OpenChromium(string id, string key, string cookie, string userAgent)
	{
		string text = AppSettings.GetVal("TokenPrefix") + Token.GetTokenValue(BusSysUser.Instance.User.Id.ToString());
		string val = AppSettings.GetVal("TokenKey");
		string val2 = AppSettings.GetVal("Url", "Kestrel:Endpoints:Http");
		string text2 = Directory.GetCurrentDirectory() + "\\Util\\Chromium\\Bili-" + key;
		string fileName = text2 + "\\Chromium.exe";
		ProcessStartInfo processStartInfo = new ProcessStartInfo();
		processStartInfo.FileName = fileName;
		processStartInfo.Arguments = "-a " + text + " -c \"" + cookie + "\" -t None -i " + id + " -k " + key + " -s 0 -u " + val2 + " --token " + val + " --ua \"" + userAgent + "\"";
		ProcessStartInfo startInfo = processStartInfo;
		Process.Start(startInfo);
	}

	public static DataTable LoginBili(string id, string key, string cookie, string userAgent)
	{
		string text = AppSettings.GetVal("TokenPrefix") + Token.GetTokenValue(BusSysUser.Instance.User.Id.ToString());
		string val = AppSettings.GetVal("TokenKey");
		string val2 = AppSettings.GetVal("Url", "Kestrel:Endpoints:Http");
		string text2 = Directory.GetCurrentDirectory() + "\\Util\\Chromium\\Bili-" + key;
		Util.CopyAndRenameFolder(Directory.GetCurrentDirectory() + "\\Util\\Chromium\\BiliDefault", text2);
		string fileName = text2 + "\\Chromium.exe";
		ProcessStartInfo processStartInfo = new ProcessStartInfo();
		processStartInfo.FileName = fileName;
		processStartInfo.Arguments = "-a " + text + " -c \"" + cookie + "\" -t Login -i " + id + " -k " + key + " -m \"https://passport.bilibili.com/login\" -s 0 -u " + val2 + " --token " + val + " --ua \"" + userAgent + "\"";
		ProcessStartInfo startInfo = processStartInfo;
		Process.Start(startInfo)?.WaitForExit();
		Thread.Sleep(2000);
		return BiliCookies.GetCookiesListByKey(id);
	}

	public static async void LotteryDraw(string cookieId, string areaId, int userId)
	{
		DataTable areaList = BiliArea.GetAreaList(areaId, BusSysUser.Instance.User.Organization.BiliBili);
		string submissionTag = Util.GetJObject(areaList.Rows[0], "FLotteryDraw");
		string blackboardUrl = Util.GetJObject(areaList.Rows[0], "FBlackboardUrl");
		Uri uri = new Uri(blackboardUrl);
		blackboardUrl = uri.Segments[^1].Split('.')[0];
		DataTable cookiesList = BiliCookies.GetCookiesList(cookieId, await QzUtil.Validate(userId, BusSysUser.Instance.User.Address, "bilibili"), userId);
		List<Task> list = new List<Task>();
		if (submissionTag != "" && blackboardUrl != "")
		{
			foreach (DataRow row in cookiesList.Rows)
			{
				string key = row["FKey"].ToString() ?? "";
				string cookie = row["FCookie"].ToString() ?? "";
				string name = row["FName"].ToString() ?? "";
				string header = row["FHeaders"].ToString() ?? "";
				if (row["FCsrf"].ToString() == null)
				{
				}
				string proxyAddress = row["FProxyAddress"].ToString() ?? "";
				string proxyUserName = row["FProxyUserName"].ToString() ?? "";
				string proxyPassword = row["FProxyPassword"].ToString() ?? "";
				Util.WriteLog("BiliBili", name, "抽奖", "开始抽奖,请稍等！");
				list.Add(Task.Run(delegate
				{
					LotteryDraw(key, name, cookie, blackboardUrl, submissionTag, header, proxyAddress, proxyUserName, proxyPassword);
				}));
			}
			return;
		}
		Util.WriteLog("BiliBili", "错误信息", "抽奖", "未配置抽奖信息");
	}

	public static async void LotteryDraw(string key, string name, string cookie, string blackboard, string submissionTag, string header, string proxyAddress, string proxyUserName, string proxyPassword)
	{
		string csrf = Util.GetCookieByKey(cookie, "bili_jct");
		HttpClientHandler defaultHandler = null;
		if (proxyAddress != "")
		{
			defaultHandler = new HttpClientHandler
			{
				Proxy = new WebProxy
				{
					Address = new Uri(proxyAddress),
					Credentials = new NetworkCredential(proxyUserName, proxyPassword)
				}
			};
		}
		Dictionary<string, string> dictionary = HttpClientFactory.FormataHeader(header, cookie);
		dictionary.Remove("Accept-Language");
		HttpClientFactory httpClientFactory = new HttpClientFactory(key, dictionary, defaultHandler);
		string url = "https://api.bilibili.com/x/lottery/x/mytimes?csrf=" + csrf + "&sid=" + submissionTag;
		JObject jObject = await httpClientFactory.Get(url);
		string message = "";
		if (Util.GetJObject(jObject, "code") == "0")
		{
			int times = Util.GetJObject<int>(jObject["data"], "times");
			while (times > 0)
			{
				int num = ((times < 10) ? 1 : 10);
				jObject = await httpClientFactory.Post("https://api.bilibili.com/x/lottery/x/do", "csrf=" + csrf + "&num=" + num + "&page_id=" + blackboard + "&sid=" + submissionTag);
				await Task.Delay(2000);
				if (Util.GetJObject(jObject, "code") == "0")
				{
					JArray jObject2 = Util.GetJObject<JArray>(jObject, "data");
					if (jObject2 != null && jObject2.Count > 0)
					{
						for (int i = 0; i < jObject2.Count; i++)
						{
							JToken jToken = jObject2[i]["award_info"];
							if (jToken != null && jToken.Type != JTokenType.Null)
							{
								message = message + ((message == "") ? "" : "、") + Util.GetJObject(jToken, "name");
							}
						}
					}
					times -= num;
					continue;
				}
				Util.WriteLog("BiliBili", name, "抽奖", Util.GetJObject(jObject, "message"), ConsoleColor.Red);
				break;
			}
			Util.WriteLog("BiliBili", name, "抽奖", (message == "") ? "恭喜你未中奖！" : message, ConsoleColor.Green);
		}
		else
		{
			Util.WriteLog("BiliBili", name, "抽奖", Util.GetJObject(jObject, "message"), ConsoleColor.Red);
		}
	}

	public static async Task<JObject> GetSysOrganizationList2(Request model, string org)
	{
		JObject jObjectParam = new JObject { ["userId"] = BusSysUser.Instance.User.Id };
		model.jObjectParam = jObjectParam;
		jObjectParam = ((JObject)(await Util.Request("/Common/GetSysOrganizationList", model))) ?? new JObject();
		JArray source = Util.GetJObject<JArray>(jObjectParam, "rows") ?? new JArray();
		source = JArray.FromObject(source.Where((JToken token) => "BiliBili".Split(',').Any((string s) => ((token["label"] ?? ((JToken)"")).Value<string>() ?? "").Contains(s))));
		foreach (JToken item in source)
		{
			JArray jArray = (JArray)item["children"];
			if (jArray == null || jArray.Count <= 0)
			{
				continue;
			}
			foreach (JToken item2 in jArray)
			{
				Util.GetJObject(item2, "label");
				string jObject = Util.GetJObject(item2, "aricles");
				string jObject2 = Util.GetJObject(item2, "mileage", "0");
				string jObject3 = Util.GetJObject(item2, "value", "0");
				if (jObject != "0" && jObject2 != "0")
				{
					JArray jArray2 = (JArray)item2["children"];
					jArray2?.Add(new JObject
					{
						["label"] = "投稿",
						["value"] = jObject3 + "-投稿",
						["desc"] = jObject
					});
					jArray2?.Add(new JObject
					{
						["label"] = "直播",
						["value"] = jObject3 + "-直播",
						["desc"] = jObject2
					});
				}
			}
		}
		jObjectParam = new JObject { ["tree"] = source };
		if (org == "")
		{
			org = "2";
		}
		jObjectParam["treeValue"] = JArray.FromObject(org.Split(','));
		return jObjectParam;
	}

	public static async Task<JObject> GetSysOrganizationList(Request model, string org)
	{
		JObject jObjectParam = new JObject { ["userId"] = BusSysUser.Instance.User.Id };
		model.jObjectParam = jObjectParam;
		jObjectParam = ((JObject)(await Util.Request("/Common/GetSysOrganizationList", model))) ?? new JObject();
		JArray source = Util.GetJObject<JArray>(jObjectParam, "rows") ?? new JArray();
		source = JArray.FromObject(source.Where((JToken token) => "BiliBili".Split(',').Any((string s) => ((token["label"] ?? ((JToken)"")).Value<string>() ?? "").Contains(s))));
		jObjectParam = new JObject { ["tree"] = source };
		if (org == "")
		{
			org = "2";
		}
		jObjectParam["treeValue"] = JArray.FromObject(org.Split(','));
		return jObjectParam;
	}
}
