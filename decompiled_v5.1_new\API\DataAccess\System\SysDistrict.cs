using System.Data;
using API.Common;

namespace API.DataAccess.System;

public class SysDistrict
{
	public static DataTable GetSysDistrictList(string type, string parentCode)
	{
		string text = " SELECT FCode AS value,FName AS label,FZJM AS zjm FROM TSysDistrict WHERE 1=1";
		if (type != "")
		{
			text = text + " AND FType=" + type;
		}
		if (parentCode != "")
		{
			text = text + " AND FParentCode='" + parentCode + "'";
		}
		return SQLHelper.LocalDB.RunSqlDt(text);
	}
}
