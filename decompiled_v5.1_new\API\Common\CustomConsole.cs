using System;
using System.IO;
using System.Text;

namespace API.Common;

public class CustomConsole
{
	private class FilteringTextWriter(TextWriter originalWriter) : TextWriter()
	{
		private readonly TextWriter _originalWriter = originalWriter;

		public override Encoding Encoding => originalWriter.Encoding;

		public override void WriteLine(string? value)
		{
			if (value != null && !value.Contains("安全警告:协商的 TLS 1.0 是非安全协议，只有在为了实现向后兼容性才受支持。建议的协议版本为 TLS 1.2 及更高版本。"))
			{
				originalWriter.WriteLine(value);
			}
		}
	}

	private static TextWriter? _originalOut;

	public static void Initialize()
	{
		_originalOut = Console.Out;
		Console.SetOut(new FilteringTextWriter(_originalOut));
	}
}
