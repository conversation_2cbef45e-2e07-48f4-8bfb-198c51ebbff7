using System.IO;
using System.Text;
using Newtonsoft.Json.Linq;

namespace API.Common;

public class FileCacheService
{
	private readonly string _cacheDirectory;

	public FileCacheService(int userId)
	{
		_cacheDirectory = Directory.GetCurrentDirectory() + "\\CaChe\\" + Util.CalcMD5(userId + AppSettings.GetVal("TokenKey"));
		Directory.CreateDirectory(_cacheDirectory);
	}

	public JObject? GetVal(string key)
	{
		string path = Path.Combine(_cacheDirectory, key);
		if (File.Exists(path))
		{
			return JObject.Parse(RsaEncrypt.RSADecrypt(Encoding.UTF8.GetString(File.ReadAllBytes(path))));
		}
		return null;
	}

	public void SetVal(string key, string str)
	{
		byte[] bytes = Encoding.UTF8.GetBytes(RsaEncrypt.RSAEncrypt(str));
		string path = Path.Combine(_cacheDirectory, key);
		File.WriteAllBytes(path, bytes);
	}
}
