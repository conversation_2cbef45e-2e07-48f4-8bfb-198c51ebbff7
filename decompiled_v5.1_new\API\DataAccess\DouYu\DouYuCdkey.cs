using System.Data;
using API.Common;

namespace API.DataAccess.DouYu;

public class DouYuCdkey
{
	public static DataTable GetCdkeyList(string status, string areaId, string cookieId, string activityId, string search, int userId, int limit = 0, int offset = 0, string prop = "", string order = "")
	{
		string text = " SELECT T1.Fid,T1.FName,T1.FCdkey,T1.FDate,CASE T1.FStatus WHEN 1 THEN '已导出' WHEN 0 THEN '' ELSE '状态不正确' END AS FStatusName ";
		text += " ,T2.FName AS FAreaName,T3.FName AS FCookieName,T4.FTaskName";
		text = text + SQLHelper.total + " FROM TCdkey T1";
		text += " LEFT JOIN TArea T2 ON T2.Fid=T1.FAreaId";
		text += " LEFT JOIN TCookies T3 ON T3.Fid=T1.FCookieId";
		text += " LEFT JOIN TAreaTask T4 ON T4.FPrizeId=T1.FPrizeId";
		text = text + "  WHERE T1.FUserId=" + userId;
		if (areaId != "")
		{
			text = text + " AND T1.FAreaId IN (" + areaId + ")";
		}
		if (cookieId != "")
		{
			text = text + " AND T1.FCookieId IN (" + cookieId + ")";
		}
		if (activityId != "")
		{
			text = text + " AND T1.FActivityId='" + activityId + "'";
		}
		if (search != "")
		{
			text = text + " AND (T1.FName LIKE '%" + search + "%' OR T1.FCdkey LIKE '%" + search + "%' )";
		}
		if (status != "")
		{
			text = text + " AND T1.FStatus=" + status;
		}
		if (prop == "")
		{
			prop = "T1.FDate";
			order = "DESC";
		}
		return SQLHelper.DouYuLocalDB.RunSqlDt(text, limit, offset, prop, order);
	}

	public static void UpdateCdkeyList(string cookieId, string areaId, int userId)
	{
		string text = " DELETE TCdkey WHERE Fid IN ";
		text += " ( SELECT T1.Fid FROM TCdkey T1";
		text += " INNER JOIN TCdkey T2 ON T2.FCdkey=T1.FCdkey AND T2.FStatus!=-1 AND T2.FUserId=T1.FUserId AND T2.FCookieId =T1.FCookieId AND T2.FAreaId=T1.FAreaId ";
		text = text + " WHERE T1.FStatus=-1 AND T1.FUserId=" + userId;
		if (cookieId != "")
		{
			text = text + " AND T1.FCookieId IN (" + cookieId + ")";
		}
		if (areaId != "")
		{
			text = text + " AND T1.FAreaId IN (" + areaId + ")";
		}
		text += ")";
		text += " UPDATE TCdkey SET FStatus=0 WHERE FStatus=-1";
		text = text + " DELETE TCdkey WHERE Fid IN(SELECT MAX(Fid) FROM TCdkey WHERE FUserId=" + userId + " GROUP BY FCdkey HAVING COUNT(*)>1)";
		SQLHelper.DouYuLocalDB.RunSqlDt(text);
	}

	public static string ExportCdkeyList(string id, int userId)
	{
		string text = " SELECT T2.FName AS 分区名称,T3.FName AS 账号名称,T4.FTaskName AS 任务名称, T1.FName AS Cdkey名称,T1.FCdkey AS Cdkey,T1.FDate AS 领取时间";
		text += " FROM TCdkey T1";
		text += " LEFT JOIN TArea T2 ON T2.Fid=T1.FAreaId";
		text += " LEFT JOIN TCookies T3 ON T3.Fid=T1.FCookieId";
		text += " LEFT JOIN TAreaTask T4 ON T4.FAreaId=T1.FAreaId AND T4.FPrizeId=T1.FPrizeId";
		text = text + "  WHERE T1.FUserId=" + userId;
		if (id != "")
		{
			text = text + " AND T1.Fid IN (" + id + ")";
		}
		text += " ORDER BY T1.FDate DESC";
		DataTable dt = SQLHelper.DouYuLocalDB.RunSqlDt(text);
		Util.FileDownload(out string absolute, out string relative);
		ExcelHelper.X2003.TableToExcelForXLS(dt, absolute);
		text = " UPDATE TCdkey SET FStatus=1 WHERE FUserId=" + userId + " AND Fid IN (" + id + ")";
		SQLHelper.DouYuLocalDB.RunSqlText(text);
		return relative;
	}
}
