using System;
using System.Data;
using System.Threading.Tasks;
using API.Common;
using API.DataAccess.BiliBili;
using API.Models.Comm;
using API.Quartz;
using Newtonsoft.Json.Linq;
using Quartz;

namespace API.BusService.BiliBili;

public class BusBiliQuartz
{
	public static async Task BatchScheduleJob(IScheduler scheduler, DataTable dt, int userId)
	{
		for (int i = 0; i < dt.Rows.Count; i++)
		{
			string text = dt.Rows[i]["FEnable"].ToString() ?? "";
			if (text == "1")
			{
				string text2 = dt.Rows[i]["Fid"].ToString() ?? "";
				string jobGroup = "定时任务-BiliBili-" + text2;
				string jobName = dt.Rows[i]["FJobName"].ToString() ?? "";
				string s = dt.Rows[i]["FExecTime"].ToString() ?? "";
				int num = int.Parse(dt.Rows[i]["FDifference"].ToString() ?? "0");
				int seconds = int.Parse(dt.Rows[i]["FSeconds"].ToString() ?? "0");
				string text3 = dt.Rows[i]["FAreaId"].ToString() ?? "";
				string text4 = dt.Rows[i]["FCookieId"].ToString() ?? "";
				string type = dt.Rows[i]["FType"].ToString() ?? "";
				string text5 = dt.Rows[i]["FParam"].ToString() ?? "";
				DateTime dateTime = DateTime.ParseExact(s, "HH:mm:ss", null);
				string cronExpression = dateTime.Second + " " + dateTime.Minute + " " + dateTime.Hour + " ? * *";
				JObject jObjectQuartz = new JObject
				{
					["areaId"] = text3,
					["cookieId"] = text4,
					["param"] = text5,
					["difference"] = num,
					["type"] = type
				};
				bool flag = !(await scheduler.CheckExists(new JobKey(jobName, jobGroup)));
				bool flag2 = flag;
				if (flag2)
				{
					flag2 = await QzUtil.ScheduleJobPlus<QzBili>(scheduler, jobGroup, jobName, cronExpression, seconds, jObjectQuartz, userId);
				}
				if (flag2)
				{
					Util.WriteLog("BiliBili", "定时任务", jobName, "【BiliBili】【" + jobName + "】" + ((type != "") ? ("【" + type + "】") : "") + "加载成功！", ConsoleColor.Green);
				}
			}
		}
	}

	public static async Task<DataTable> GetQuartzList(IScheduler scheduler, DataTable dt)
	{
		for (int i = 0; i < dt.Rows.Count; i++)
		{
			string id = dt.Rows[i]["Fid"].ToString() ?? "";
			string jobName = dt.Rows[i]["FJobName"].ToString() ?? "";
			string enable = dt.Rows[i]["FEnable"].ToString() ?? "";
			if (await scheduler.CheckExists(new JobKey(jobName, "定时任务-BiliBili-" + id)))
			{
				if (await QzUtil.ExecutingJob(scheduler, "定时任务-BiliBili-" + id, jobName))
				{
					dt.Rows[i]["FStatus"] = "正在执行";
				}
				else if (enable == "1")
				{
					dt.Rows[i]["FStatus"] = "等待执行";
				}
				else if (await scheduler.DeleteJob(new JobKey(jobName, "定时任务-BiliBili-" + id)))
				{
					dt.Rows[i]["FStatus"] = "未启动";
				}
				else
				{
					dt.Rows[i]["FStatus"] = "异常任务";
				}
			}
			else if (enable == "1")
			{
				dt.Rows[i]["FStatus"] = "未启动";
			}
			else
			{
				dt.Rows[i]["FStatus"] = "已停用";
			}
		}
		return dt;
	}

	public static async Task ShutdownQuartz(ISchedulerFactory schedulerFactory, JObject jObject)
	{
		IScheduler scheduler = await schedulerFactory.GetScheduler();
		string id = Util.GetJObject(jObject, "Fid");
		string jobName = Util.GetJObject(jObject, "FJobName");
		if (await scheduler.CheckExists(new JobKey(jobName, "定时任务-BiliBili-" + id)))
		{
			await scheduler.DeleteJob(new JobKey(jobName, "定时任务-BiliBili-" + id));
			return;
		}
		throw new Exception("关闭任务失败，请稍后再试！");
	}

	public static async Task StartQuartz(ISchedulerFactory schedulerFactory, JObject jObject, int userId)
	{
		IScheduler scheduler = await schedulerFactory.GetScheduler();
		string id = Util.GetJObject(jObject, "Fid");
		string jObject2 = Util.GetJObject(jObject, "FGroupId");
		DataTable dt = BiliQuartz.GetQuartzList(id, jObject2, "", userId);
		if (dt.Rows.Count == 0)
		{
			throw new Exception("当前数据不正确！");
		}
		string jobName = dt.Rows[0]["FJobName"].ToString() ?? "";
		if (!(await scheduler.CheckExists(new JobKey(jobName, "定时任务-BiliBili-" + id))))
		{
			string s = dt.Rows[0]["FExecTime"].ToString() ?? "";
			int num = int.Parse(dt.Rows[0]["FDifference"].ToString() ?? "0");
			int seconds = int.Parse(dt.Rows[0]["FSeconds"].ToString() ?? "0");
			string text = dt.Rows[0]["FAreaId"].ToString() ?? "";
			string text2 = dt.Rows[0]["FCookieId"].ToString() ?? "";
			string text3 = dt.Rows[0]["FType"].ToString() ?? "";
			string text4 = dt.Rows[0]["FParam"].ToString() ?? "";
			DateTime dateTime = DateTime.ParseExact(s, "HH:mm:ss", null);
			string cronExpression = dateTime.Second + " " + dateTime.Minute + " " + dateTime.Hour + " ? * *";
			JObject jObject3 = new JObject
			{
				["areaId"] = text,
				["cookieId"] = text2,
				["param"] = text4,
				["difference"] = num,
				["type"] = text3
			};
			await QzUtil.ScheduleJobPlus<QzBili>(scheduler, "定时任务-BiliBili-" + id, jobName, cronExpression, seconds, jObject3, userId);
			return;
		}
		throw new Exception("当前任务已启动！");
	}

	public static async Task ExecQuartz(IScheduler scheduler, JObject jObject, User user)
	{
		string id = Util.GetJObject(jObject, "Fid");
		string groupId = Util.GetJObject(jObject, "FGroupId");
		string jobName = Util.GetJObject(jObject, "FJobName");
		if (await scheduler.CheckExists(new JobKey(jobName, "定时任务-BiliBili-" + id)))
		{
			await scheduler.TriggerJob(new JobKey(jobName, "定时任务-BiliBili-" + id));
			await Task.Delay(200);
			return;
		}
		DataTable dt = BiliQuartz.GetQuartzList(id, groupId, "", user.Id);
		if (dt.Rows.Count == 0)
		{
			throw new Exception("当前数据不正确！");
		}
		jobName = dt.Rows[0]["FJobName"].ToString() ?? "";
		if (!(await scheduler.CheckExists(new JobKey(jobName, "定时任务-BiliBili-" + id))))
		{
			int num = int.Parse(dt.Rows[0]["FDifference"].ToString() ?? "0");
			int seconds = int.Parse(dt.Rows[0]["FSeconds"].ToString() ?? "0");
			string text = dt.Rows[0]["FAreaId"].ToString() ?? "";
			string text2 = dt.Rows[0]["FCookieId"].ToString() ?? "";
			string text3 = dt.Rows[0]["FType"].ToString() ?? "";
			string text4 = dt.Rows[0]["FParam"].ToString() ?? "";
			JObject jObject2 = new JObject
			{
				["areaId"] = text,
				["cookieId"] = text2,
				["param"] = text4,
				["difference"] = num,
				["type"] = text3
			};
			await QzUtil.ScheduleJobPlus<QzBili>(scheduler, "定时任务-BiliBili-" + id, jobName, "", seconds, jObject2, user.Id);
			return;
		}
		throw new Exception("当前任务已启动！");
	}

	public static async Task StopQuartz(IScheduler scheduler, JObject jObject)
	{
		string id = Util.GetJObject(jObject, "Fid");
		string jobName = Util.GetJObject(jObject, "FJobName");
		while (await QzUtil.ExecutingJob(scheduler, "定时任务-BiliBili-" + id, jobName))
		{
			await scheduler.Interrupt(new JobKey(jobName, "定时任务-BiliBili-" + id));
			await Task.Delay(500);
		}
	}

	public static async Task LoginQuartz(ISchedulerFactory schedulerFactory, int userId)
	{
		IScheduler scheduler = await schedulerFactory.GetScheduler();
		DataTable quartzExecList = BiliQuartz.GetQuartzExecList(userId);
		await BatchScheduleJob(scheduler, quartzExecList, userId);
	}
}
