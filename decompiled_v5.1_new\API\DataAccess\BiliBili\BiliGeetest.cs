using System;
using System.Data;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using API.Common;
using Newtonsoft.Json.Linq;

namespace API.DataAccess.BiliBili;

public class BiliGeetest
{
	private static HttpClient client = new HttpClient();

	public static DataTable GetGeetestList(string search, int limit = 0, int offset = 0, string prop = "", string order = "")
	{
		string text = " SELECT CASE FMethod WHEN 1 THEN 'POST' ELSE 'GET' END AS FMethodName,*";
		text += " , CASE  WHEN FUrl = 'http://127.0.0.1:1011/geetest3_combination' THEN 999999 ";
		text += "         WHEN FName = '稿件删除' THEN 999998";
		text += " ELSE 0 END AS FSort FROM TGeetest WHERE 1=1";
		if (search != "")
		{
			text = text + " AND ( FName LIKE '%" + search + "%' OR FUrl LIKE '%" + search + "%' )";
		}
		if (prop == "")
		{
			prop = "FEnable DESC , FSort ";
			order = "ASC";
		}
		return SQLHelper.BiliLocalDB.RunSqlDt(text, limit, offset, prop, order);
	}

	public static void EnableGeetest(JObject jObject, int userId, string userName, string curTime)
	{
		DataCURD.Save(jObject, "TGeetest", "启用禁用打码", "Fid", userId, userName, curTime, SQLHelper.BiliLocalDB.InitCnn());
	}

	public static JObject GetGeetestList(string name)
	{
		DataTable geetestList = GetGeetestList(name, 0, 0, "", "");
		if (geetestList.Rows.Count == 0)
		{
			throw new Exception("请配置" + name + "打码信息");
		}
		JObject jObject = new JObject();
		for (int i = 0; i < geetestList.Columns.Count; i++)
		{
			jObject[geetestList.Columns[i].ColumnName] = geetestList.Rows[0][i].ToString();
		}
		return jObject;
	}

	public static async Task<JObject> Geetest4(string v_voucher, string content, string proxyAddress, string proxyUserName, string proxyPassword)
	{
		JObject jObject = new JObject();
		if (proxyAddress != "")
		{
			string[] array = proxyAddress.Split("://");
			if (proxyUserName != "" && proxyPassword != "")
			{
				jObject["http"] = array[0] + "://" + proxyUserName + ":" + proxyPassword + "@" + array[1];
				jObject["https"] = array[0] + "://" + proxyUserName + ":" + proxyPassword + "@" + array[1];
			}
			else
			{
				jObject["http"] = array[0] + "://" + array[1];
				jObject["https"] = array[0] + "://" + array[1];
			}
		}
		if (client == null)
		{
			client = new HttpClient();
		}
		JObject requestBody = new JObject
		{
			["v_voucher"] = v_voucher,
			["content"] = content,
			["proxies"] = jObject
		};
		await Task.Delay(1000);
		StringContent content2 = new StringContent(requestBody.ToString(), Encoding.UTF8, "application/json");
		string text = await (await client.PostAsync("http://127.0.0.1:8000/api/ocr", content2)).Content.ReadAsStringAsync();
		JObject jObject2 = new JObject();
		try
		{
			jObject2 = JObject.Parse(text);
		}
		catch
		{
			jObject2["code"] = -100;
			jObject2["message"] = text;
			jObject2["msg"] = text;
			jObject2["data"] = null;
		}
		return jObject2;
	}
}
