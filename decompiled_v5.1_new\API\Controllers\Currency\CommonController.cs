using System;
using System.Data;
using System.Threading.Tasks;
using API.BusService.System;
using API.Common;
using API.DataAccess.BiliBili;
using API.DataAccess.DouYu;
using API.DataAccess.System;
using API.Models.Comm;
using API.Quartz;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Newtonsoft.Json.Linq;
using Quartz;

namespace API.Controllers.Currency;

public class CommonController(ISchedulerFactory schedulerFactory) : Controller()
{
	private readonly ISchedulerFactory _schedulerFactory = schedulerFactory;

	[RequestSizeLimit(4294967296L)]
	[HttpPost]
	public async Task<Response> Upload()
	{
		Response mRet = new Response();
		try
		{
			string text = base.Request.Form["path"].ToString() ?? "Common";
			string subfolders = base.Request.Form["subfolders"].ToString() ?? "0";
			string format = base.Request.Form["format"].ToString() ?? "";
			IFormFileCollection files = base.HttpContext.Request.Form.Files;
			JArray jArray = await Util.Upload(files, (text == "") ? "Common" : text, subfolders, format);
			if (jArray.Count == 0)
			{
				throw new Exception("文件未能上传成功!");
			}
			mRet.data = jArray[0];
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = ex.Message;
		}
		return mRet;
	}

	[HttpPost]
	public Response AddCookies([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			response.data = SysUserCookies.AddCookies(model);
			return response;
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
			return response;
		}
	}

	[HttpPost]
	public Response EditCookie([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			response.data = SysUserCookies.EditCookie(model);
			return response;
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
			return response;
		}
	}

	[HttpPost]
	public Response DelCookie([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			response.data = SysUserCookies.DelCookie(model);
			return response;
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
			return response;
		}
	}

	[HttpPost]
	public Response EmptyCookie([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			response.data = SysUserCookies.EmptyCookie(model);
			return response;
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
			return response;
		}
	}

	[HttpPost]
	public Response GetUserCookiesList([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			response.data = SysUserCookies.GetUserCookiesList(model);
			return response;
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
			return response;
		}
	}

	[HttpPost]
	public Response GetUserCookiesList2([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			response.data = SysUserCookies.GetUserCookiesList2(model);
			return response;
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
			return response;
		}
	}

	[HttpPost]
	public Response ValidateCookie([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			response.data = SysUserCookies.ValidateCookie(model);
			return response;
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
			return response;
		}
	}

	[HttpPost]
	public Response ValidateCookie2([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			response.data = SysUserCookies.ValidateCookie2(model);
			return response;
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
			return response;
		}
	}

	[HttpPost]
	public Response EditPWD([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "oldpw1");
			string jObject2 = Util.GetJObject(model.jObjectParam, "newpw1");
			string jObject3 = Util.GetJObject(model.jObjectParam, "newpw2");
			SysUser.EditPWD(jObject, jObject2, jObject3, BusSysUser.Instance.User.Name, BusSysUser.Instance.User.Id, model.curTime);
			response.data = "";
			response.message = "您的密码是：" + jObject2;
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response GetIdentifyingList([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			response.data = SysUserCookies.GetIdentifyingList(model);
			return response;
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
			return response;
		}
	}

	[HttpPost]
	public Response BiliUpateAreaInfo([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			response.data = BiliArea.GetAreaInfo(model);
			return response;
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
			return response;
		}
	}

	[HttpPost]
	public Response DouYuUpateAreaInfo([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			response.data = DouYuArea.GetAreaInfo(model);
			return response;
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
			return response;
		}
	}

	[HttpPost]
	public Response GetSysOrganizationList([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectSearch, "search");
			string jObject2 = Util.GetJObject(model.jObjectSearch, "enable", "1");
			DataTable sysOrganizationList = SysOrganization.GetSysOrganizationList(jObject, jObject2, model.limit, model.offset, model.prop, model.order);
			JArray sysOrganizationChild = BusSysOrganization.GetSysOrganizationChild(sysOrganizationList, 0);
			response.data = new
			{
				total = sysOrganizationChild.Count,
				rows = sysOrganizationChild
			};
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response AddExpiraation([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "userId");
			string jObject2 = Util.GetJObject(model.jObjectParam, "name");
			string jObject3 = Util.GetJObject(model.jObjectParam, "key");
			string jObject4 = Util.GetJObject(model.jObjectParam, "org");
			response.data = BusPayOrder.GetPayInfo(jObject2, jObject3, jObject4, jObject);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response AddExpiraation2([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "userId");
			string jObject2 = Util.GetJObject(model.jObjectParam, "name");
			string jObject3 = Util.GetJObject(model.jObjectParam, "key");
			string jObject4 = Util.GetJObject(model.jObjectParam, "org");
			JObject jObject5 = Util.GetJObject<JObject>(model.jObjectParam, "jObjectContent");
			response.data = BusPayOrder.GetPayInfo2(jObject2, jObject3, jObject4, jObject, jObject5);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response GetControlByCatalog([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "catalog");
			response.data = SysControl.GetSysControlValue(jObject);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response GetSysControlByCatalog([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "catalog");
			DataTable sysControlByCatalog = SysControl.GetSysControlByCatalog(jObject);
			response.data = Util.GetTableResponse(sysControlByCatalog);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public async Task<Response> BulletScreen([FromBody] Request model)
	{
		Response mRet = new Response();
		try
		{
			string userId = Util.GetJObject(model.jObjectParam, "userId");
			string roomNo = Util.GetJObject(model.jObjectParam, "roomId");
			string msg = Util.GetJObject(model.jObjectParam, "msg");
			IScheduler scheduler = await schedulerFactory.GetScheduler();
			JobKey jobKey = new JobKey("辅助弹幕", "定时任务-BiliBili-" + userId);
			if (await scheduler.CheckExists(jobKey))
			{
				mRet.data = "执行中，请稍等！";
			}
			else
			{
				TriggerBuilder triggerBuilder = TriggerBuilder.Create();
				ITrigger trigger = triggerBuilder.StartAt(DateBuilder.NextGivenSecondDate(DateTime.UtcNow, 40)).Build();
				IJobDetail jobDetail = JobBuilder.Create<QzBili.辅助弹幕>().WithIdentity(jobKey).Build();
				JObject value = new JObject
				{
					["msg"] = msg,
					["roomNo"] = roomNo
				};
				Util.WriteLog("BiliBili", "弹幕日志", roomNo, msg);
				jobDetail.JobDataMap.Add("params", value);
				jobDetail.JobDataMap.Add("userId", 9160);
				await scheduler.ScheduleJob(jobDetail, trigger);
				mRet.data = "预计 " + DateTime.Now.AddMinutes(1.0).ToString("HH:mm:ss") + " 执行！";
			}
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}

	[HttpPost]
	public async Task<Response> Watch([FromBody] Request model)
	{
		Response mRet = new Response();
		try
		{
			string userId = Util.GetJObject(model.jObjectParam, "userId");
			string roomNo = Util.GetJObject(model.jObjectParam, "roomId");
			string areaId = Util.GetJObject(model.jObjectParam, "areaId");
			int watchMinutes = Util.GetJObject<int>(model.jObjectParam, "watchMinutes");
			IScheduler scheduler = await schedulerFactory.GetScheduler();
			JobKey jobKey = new JobKey("辅助观看", "定时任务-BiliBili-" + userId);
			if (await scheduler.CheckExists(jobKey))
			{
				mRet.data = "执行中，请稍等！";
			}
			else
			{
				TriggerBuilder triggerBuilder = TriggerBuilder.Create();
				ITrigger trigger = triggerBuilder.StartNow().Build();
				IJobDetail jobDetail = JobBuilder.Create<QzBili.辅助观看>().WithIdentity(jobKey).Build();
				JObject value = new JObject
				{
					["watchMinutes"] = watchMinutes,
					["roomNo"] = roomNo,
					["areaId"] = areaId
				};
				jobDetail.JobDataMap.Add("params", value);
				jobDetail.JobDataMap.Add("userId", 9160);
				await scheduler.ScheduleJob(jobDetail, trigger);
				mRet.data = "预计 " + DateTime.Now.AddMinutes(watchMinutes).ToString("HH:mm:ss") + " 执行完成！";
			}
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}

	[HttpPost]
	public Response Help([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "userId");
			JArray jArray = Util.GetJObject<JArray>(model.jObjectParam, "array") ?? new JArray();
			DataTable dataTable = jArray.ToObject<DataTable>();
			if (dataTable == null)
			{
				throw new Exception("请选择账号！");
			}
			string sSql = "UPDATE TBusHelpRoom SET FEnable=0 WHERE FUserId=" + jObject;
			SQLHelper.LocalDB.RunSqlText(sSql);
			SQLHelper.LocalDB.SqlBulkCopyByDataTable("TBusHelpRoom", dataTable);
			response.data = "您还有" + SQLHelper.LocalDB.RunSqlStr("SELECT FBattery FROM TSysUser WHERE Fid=" + jObject) + "个电池";
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response SaveWidgetsConfig([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "key");
			string jObject2 = Util.GetJObject(model.jObjectParam, "val");
			if (jObject == "GRID")
			{
				SysUser.EditSysUser(new JObject
				{
					["Fid"] = BusSysUser.Instance.User.Id,
					["FGrid"] = jObject2
				}, BusSysUser.Instance.User.Name, BusSysUser.Instance.User.Id, model.curTime);
			}
			response.data = "";
		}
		catch
		{
			response.code = 500;
			response.message = "保存格式不正确！";
		}
		return response;
	}

	[HttpPost]
	public Response GetSysNoticeList([FromBody] Request model, User user)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectSearch, "id", "0");
			DataTable sysNoticeList = SysNotice.GetSysNoticeList(jObject, user);
			response.data = Util.GetTableResponse(sysNoticeList);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response GetHomeList([FromBody] Request model, User user)
	{
		Response response = new Response();
		try
		{
			SqlParameter[] prams = new SqlParameter[7]
			{
				new SqlParameter("@userId", user.Id),
				new SqlParameter("@p1", Util.GetJObject(model.jObjectSearch, "p1")),
				new SqlParameter("@p2", Util.GetJObject(model.jObjectSearch, "p2")),
				new SqlParameter("@p3", Util.GetJObject(model.jObjectSearch, "p3")),
				new SqlParameter("@p4", Util.GetJObject(model.jObjectSearch, "p4")),
				new SqlParameter("@p5", Util.GetJObject(model.jObjectSearch, "p5")),
				new SqlParameter("@p6", Util.GetJObject(model.jObjectSearch, "p6"))
			};
			DataTable dt = SQLHelper.LocalDB.RunSqlDt("EXEC PGetHome @userId,@p1,@p2,@p3,@p4,@p5,@p6", prams);
			response.data = Util.GetTableResponse(dt);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response GetBulletSceenList([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectSearch, "type");
			DataTable liveMsgList = BiliLive.GetLiveMsgList(jObject, "", "");
			response.data = Util.GetTableResponse(liveMsgList);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response StarSysNotice([FromBody] Request model, User user)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "id", "0");
			SysNotice.StarSysNotice(jObject, user);
			response.data = "";
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response WriteLog([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "typeName");
			string jObject2 = Util.GetJObject(model.jObjectParam, "name");
			string jObject3 = Util.GetJObject(model.jObjectParam, "jobName");
			string jObject4 = Util.GetJObject(model.jObjectParam, "msg");
			string jObject5 = Util.GetJObject(model.jObjectParam, "consoleColor");
			ConsoleColor value = ConsoleColor.White;
			if (jObject5 == "red")
			{
				value = ConsoleColor.Red;
			}
			Util.WriteLog(jObject, jObject2, jObject3, jObject4, value, writeLog: false);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}
}
