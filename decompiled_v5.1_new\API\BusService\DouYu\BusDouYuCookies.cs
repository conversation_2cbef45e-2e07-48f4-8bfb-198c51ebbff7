using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Net.Http;
using System.Threading.Tasks;
using API.BusService.System;
using API.Common;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace API.BusService.DouYu;

public class BusDouYuCookies
{
	public static async Task<JObject> ValidateCookie(string cookie, string headers, string proxyId)
	{
		string uid = Util.GetCookieByKey(cookie, "acf_uid");
		Dictionary<string, string> defaultHeaders = HttpClientFactory.FormataHeader(headers, cookie);
		HttpClientHandler defaultHandler = null;
		_ = proxyId != "";
		HttpClientFactory httpClientFactory = new HttpClientFactory(uid, defaultHeaders, defaultHandler);
		Dictionary<string, string> defaultHeaders2 = new Dictionary<string, string>
		{
			{ "Host", "www.douyu.com" },
			{ "Referer", "https://www.douyu.com/creator/main/live" }
		};
		JObject jObject = await httpClientFactory.Get("https://www.douyu.com/japi/creator/w/apinc/getUserInfo", defaultHeaders2);
		if (Util.GetJObject(jObject, "error") == "0")
		{
			string jObject2 = Util.GetJObject(jObject["data"], "nickName");
			string jObject3 = Util.GetJObject(jObject["data"], "rid");
			return new JObject
			{
				["FIdentifying"] = uid,
				["FHeaders"] = JsonConvert.SerializeObject(JObject.Parse(headers)),
				["FName"] = jObject2,
				["FRoomId"] = jObject3
			};
		}
		throw new Exception(Util.GetJObject(jObject, "msg"));
	}

	public static void UpdateCookieStatus(ConcurrentBag<JToken> bagUser, int userId)
	{
		foreach (JToken item in bagUser)
		{
			JObject jObject = (JObject)item;
			DataCURD.Save(jObject, "TCookies", "保存账号状态", "Fid", userId, "定时任务", null, SQLHelper.DouYuLocalDB.InitCnn());
		}
	}

	public static void AddCookieStatus(ConcurrentBag<JToken> bagUser, string cookieId, string status)
	{
		bagUser.Add(new JObject
		{
			["Fid"] = cookieId,
			["FStatus"] = status
		});
	}

	public static void LoginDouYu(string id, string key, string cookie, string userAgent)
	{
		string text = AppSettings.GetVal("TokenPrefix") + Token.GetTokenValue(BusSysUser.Instance.User.Id.ToString());
		string val = AppSettings.GetVal("TokenKey");
		string val2 = AppSettings.GetVal("Url", "Kestrel:Endpoints:Http");
		string text2 = Directory.GetCurrentDirectory() + "\\Util\\Chromium\\DouYu-" + key;
		Util.CopyAndRenameFolder(Directory.GetCurrentDirectory() + "\\Util\\Chromium\\BiliDefault", text2);
		Util.CopyAndRenameFolder(Directory.GetCurrentDirectory() + "\\Util\\Chromium\\DouYuDefault", text2, delete: false);
		string fileName = text2 + "\\Chromium.exe";
		ProcessStartInfo processStartInfo = new ProcessStartInfo();
		processStartInfo.FileName = fileName;
		processStartInfo.Arguments = "-a " + text + " -c \"" + cookie + "\" -t Login -i " + id + " -k " + key + " -m \"https://www.douyu.com/creator/main/live\" -s 0 -u " + val2 + " --token " + val + " --ua \"" + userAgent + "\"";
		ProcessStartInfo startInfo = processStartInfo;
		Process.Start(startInfo)?.WaitForExit();
	}

	public static void OpenChromium(string id, string key, string cookie, string userAgent)
	{
		string text = AppSettings.GetVal("TokenPrefix") + Token.GetTokenValue(BusSysUser.Instance.User.Id.ToString());
		string val = AppSettings.GetVal("TokenKey");
		string val2 = AppSettings.GetVal("Url", "Kestrel:Endpoints:Http");
		string text2 = Directory.GetCurrentDirectory() + "\\Util\\Chromium\\DouYu-" + key;
		string fileName = text2 + "\\Chromium.exe";
		ProcessStartInfo processStartInfo = new ProcessStartInfo();
		processStartInfo.FileName = fileName;
		processStartInfo.Arguments = "-a " + text + " -c \"" + cookie + "\" -t None -i " + id + " -k " + key + " -s 0 -m \"https://www.douyu.com/creator/main/live\" -u " + val2 + " --token " + val + " --ua \"" + userAgent + "\"";
		ProcessStartInfo startInfo = processStartInfo;
		Process.Start(startInfo);
	}
}
