using System;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace API.Common;

public class DateTimeConvert : IsoDateTimeConverter
{
	public override void Write<PERSON><PERSON>(JsonWriter writer, object? value, JsonSerializer serializer)
	{
		if (value != null && DateTime.TryParse(value.ToString(), out var result))
		{
			if (result.ToLongTimeString() == "0:00:00")
			{
				writer.WriteValue(result.Date.ToString("yyyy-MM-dd"));
			}
			else
			{
				writer.WriteValue(result.ToString("yyyy-MM-dd HH:mm:ss"));
			}
		}
		else
		{
			writer.WriteValue(value);
		}
	}
}
