using System;
using System.CodeDom.Compiler;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Text.RegularExpressions;
using System.Text.RegularExpressions.Generated;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using API.BusService.BiliBili;
using API.BusService.System;
using API.Common;
using API.DataAccess.BiliBili;
using API.Models.Comm;
using BiliveDanmakuAgent;
using BiliveDanmakuAgent.Model;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Quartz;

namespace API.Quartz;

public class QzBili
{
	public class 更新Cdkey列表 : IJob
	{
		public async Task Execute(IJobExecutionContext context)
		{
			List<Task> taskList = new List<Task>();
			string[] jobGroup = (context.JobDetail.JobDataMap.GetString("jobGroup") ?? "").Split("-");
			string jobName = context.JobDetail.JobDataMap.GetString("jobName") ?? "";
			ConcurrentBag<JToken> bagUser = new ConcurrentBag<JToken>();
			try
			{
				JObject @params = (context.JobDetail.JobDataMap.Get("params") as JObject) ?? new JObject();
				int seconds = context.JobDetail.JobDataMap.GetInt("seconds");
				int userId = context.JobDetail.JobDataMap.GetInt("userId");
				string areaId = Util.GetJObject(@params, "areaId");
				string cookieId = Util.GetJObject(@params, "cookieId");
				Util.GetJObject(@params, "type");
				string keys = await QzUtil.Validate(userId, BusSysUser.Instance.User.Address, "bilibili", areaId);
				int jObject = Util.GetJObject<int>(@params, "difference");
				if (jObject != 0)
				{
					int num = new Random().Next(0, jObject);
					seconds = ((seconds != 0) ? (seconds + num) : 0);
					DateTime datetime = DateTime.Now.AddSeconds(num);
					Util.WriteLog(jobGroup[1], jobGroup[0], jobName, datetime.ToString("yyyy-MM-dd HH:mm:ss") + "开始执行");
					while (DateTime.Now <= datetime && QzUtil.IsExecute(seconds, context))
					{
						await QzUtil.Delay();
					}
				}
				DataTable dtCookies = BiliCookies.GetCookiesList(cookieId, keys, userId, "1", areaId);
				DataTable dtActivity = BiliArea.GetAreaActivityList(areaId);
				string areaName = BiliArea.GetAreaName(areaId);
				string text = "";
				for (int i = 0; (i < dtActivity.Rows.Count) & QzUtil.IsExecute(seconds, context); i++)
				{
					string text2 = dtActivity.Rows[i]["FActivityUrl"].ToString() ?? "";
					string text3 = dtActivity.Rows[i]["FActivityId"].ToString() ?? "";
					text = text + "," + text2 + text3;
				}
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, "开始");
				List<int> list = (from row in dtCookies.AsEnumerable()
					select row.Field<int>("FProxyId")).Distinct().ToList();
				foreach (int item in list)
				{
					DataTable dtGroup = dtCookies.Select($"FProxyId = {item}").CopyToDataTable();
					taskList.Add(Task.Run(async delegate
					{
						await SwitchExcute(context, jobGroup, jobName, seconds, userId, areaId, areaName, dtGroup, dtActivity, bagUser);
					}));
					await Task.Delay(10);
				}
				foreach (Task item2 in taskList)
				{
					item2.Wait();
				}
				BusBiliCookies.UpdateCookieStatus(bagUser, userId);
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, QzUtil.WriteStatus(context, seconds));
			}
			catch (Exception ex)
			{
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, ex.Message, ConsoleColor.Red);
			}
		}

		private static async Task SwitchExcute(IJobExecutionContext context, string[] jobGroup, string jobName, int seconds, int userId, string areaId, string areaName, DataTable dtCookies, DataTable dtActivity, ConcurrentBag<JToken> bagUser)
		{
			for (int i = 0; i < dtCookies.Rows.Count; i++)
			{
				if (!QzUtil.IsExecute(seconds, context))
				{
					break;
				}
				DataRow drCookie = dtCookies.Rows[i];
				await Execute3(context, jobGroup, jobName, seconds, userId, areaId, areaName, drCookie, dtActivity, bagUser);
			}
		}

		private static async Task Execute3(IJobExecutionContext context, string[] jobGroup, string jobName, int seconds, int userId, string areaId, string areaName, DataRow drCookie, DataTable dtActivity, ConcurrentBag<JToken> bagUser)
		{
			string cookieId = drCookie["Fid"].ToString() ?? "";
			string name = drCookie["FKey"].ToString() ?? "";
			string cookie = drCookie["FCookie"].ToString() ?? "";
			string name2 = drCookie["FName"].ToString() ?? "";
			string header = drCookie["FHeaders"].ToString() ?? "";
			string csrf = drCookie["FCsrf"].ToString() ?? "";
			string text = drCookie["FProxyAddress"].ToString() ?? "";
			string userName = drCookie["FProxyUserName"].ToString() ?? "";
			string password = drCookie["FProxyPassword"].ToString() ?? "";
			try
			{
				Util.WriteLog(jobGroup[1], name2, jobName, "【" + areaName + "】开始更新");
				HttpClientHandler defaultHandler = null;
				if (text != "")
				{
					defaultHandler = new HttpClientHandler
					{
						Proxy = new WebProxy
						{
							Address = new Uri(text),
							Credentials = new NetworkCredential(userName, password)
						}
					};
				}
				Dictionary<string, string> dictionary = HttpClientFactory.FormataHeader(header, cookie);
				dictionary.Remove("Accept-Language");
				HttpClientFactory httpClientFactory = new HttpClientFactory(name, dictionary, defaultHandler);
				string typeName = jobGroup[1];
				string name3 = name2;
				string jobName2 = jobName;
				Util.WriteLog(typeName, name3, jobName2, await httpClientFactory.GetIp());
				for (int i = 0; (i < dtActivity.Rows.Count) & QzUtil.IsExecute(seconds, context); i++)
				{
					string text2 = "";
					string text3 = dtActivity.Rows[i]["FActivityId"].ToString() ?? "";
					jobName2 = dtActivity.Rows[i]["FType"].ToString() ?? "";
					if (jobName2 == "方式一")
					{
						text2 = "https://api.bilibili.com/x/lottery/rewards/awards/mylist/v2";
					}
					JObject jObject = await httpClientFactory.Get(text2 + "?activity_id=" + text3 + "&csrf=" + csrf);
					if (Util.GetJObject(jObject, "code") == "0")
					{
						JArray jArray = Util.GetJObject<JArray>(jObject["data"], "list") ?? new JArray();
						JArray jArray2 = new JArray();
						string text4 = "绑定账号";
						string text5 = "0";
						if (jobName2 == "方式一")
						{
							foreach (JToken item in jArray)
							{
								JToken jToken = item["extra_info"];
								if (jToken != null && jToken.Type != JTokenType.Null)
								{
									text4 = Util.GetJObject(jToken, "cdkey_content");
									text5 = Util.GetJObject(jToken, "cdkey_id");
								}
								long seconds2 = long.Parse(Util.GetJObject(item, "receive_time", DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString()));
								string text6 = DateTimeOffset.FromUnixTimeSeconds(seconds2).LocalDateTime.ToString("yyyy-MM-dd HH:mm:ss");
								jArray2.Add(new JObject
								{
									["FStatus"] = -1,
									["FUserId"] = userId,
									["FAreaId"] = areaId,
									["FCookieId"] = cookieId,
									["FName"] = item["award_name"],
									["FCdkey"] = text4,
									["FDate"] = text6,
									["FAwardId"] = item["award_id"],
									["FActivityId"] = item["activity_id"],
									["FUniqueId"] = Util.GetJObject(item, "unique_id"),
									["FUniqueKey"] = Util.GetJObject(item, "unique_id"),
									["FCdkeyId"] = text5
								});
							}
						}
						else
						{
							foreach (JToken item2 in jArray)
							{
								JToken jToken2 = item2["extra_info"];
								if (jToken2 != null && jToken2.Type != JTokenType.Null)
								{
									text4 = Util.GetJObject(jToken2, "cdkey_content");
									text5 = Util.GetJObject(jToken2, "cdkey_id");
								}
								long seconds3 = long.Parse(Util.GetJObject(item2, "receive_time", DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString()));
								string jObject2 = Util.GetJObject(item2, "unique_id");
								string text7 = DateTimeOffset.FromUnixTimeSeconds(seconds3).LocalDateTime.ToString("yyyy-MM-dd HH:mm:ss");
								jArray2.Add(new JObject
								{
									["FStatus"] = -1,
									["FUserId"] = userId,
									["FAreaId"] = areaId,
									["FCookieId"] = cookieId,
									["FName"] = item2["award_name"],
									["FCdkey"] = text4,
									["FDate"] = text7,
									["FAwardId"] = item2["award_id"],
									["FActivityId"] = item2["activity_id"],
									["FUniqueId"] = jObject2,
									["FUniqueKey"] = jObject2.Split('-').Last(),
									["FCdkeyId"] = text5
								});
							}
						}
						DataTable dt = jArray2.ToObject<DataTable>();
						BiliCdkey.UpdateCdkeyList(dt, cookieId, areaId, userId);
						continue;
					}
					throw new Exception(Util.GetJObject(jObject, "message"));
				}
				Util.WriteLog(jobGroup[1], name2, jobName, "【" + areaName + "】更新结束");
			}
			catch (Exception ex)
			{
				BusBiliCookies.AddCookieStatus(bagUser, cookieId, ex.Message);
				Util.WriteLog(jobGroup[1], name2, jobName, ex.Message, ConsoleColor.Red);
			}
		}
	}

	public class 更新任务信息 : IJob
	{
		public async Task Execute(IJobExecutionContext context)
		{
			string[] jobGroup = (context.JobDetail.JobDataMap.GetString("jobGroup") ?? "").Split("-");
			string jobName = context.JobDetail.JobDataMap.GetString("jobName") ?? "";
			ConcurrentBag<JToken> bagUser = new ConcurrentBag<JToken>();
			List<Task> taskList = new List<Task>();
			try
			{
				JObject @params = (context.JobDetail.JobDataMap.Get("params") as JObject) ?? new JObject();
				int seconds = context.JobDetail.JobDataMap.GetInt("seconds");
				int userId = context.JobDetail.JobDataMap.GetInt("userId");
				string areaId = Util.GetJObject(@params, "areaId");
				string cookieId = Util.GetJObject(@params, "cookieId");
				string type = Util.GetJObject(@params, "type");
				string areaTaskId = Util.GetJObject(@params, "param");
				string keys = await QzUtil.Validate(userId, BusSysUser.Instance.User.Address, "bilibili", areaId);
				int jObject = Util.GetJObject<int>(@params, "difference");
				if (jObject != 0)
				{
					int num = new Random().Next(0, jObject);
					seconds = ((seconds != 0) ? (seconds + num) : 0);
					DateTime datetime = DateTime.Now.AddSeconds(num);
					Util.WriteLog(jobGroup[1], jobGroup[0], jobName, datetime.ToString("yyyy-MM-dd HH:mm:ss") + "开始执行");
					while (DateTime.Now <= datetime && QzUtil.IsExecute(seconds, context))
					{
						await QzUtil.Delay();
					}
				}
				DataTable dtCookies = BiliCookies.GetCookiesList(cookieId, keys, userId, "1", areaId);
				DataTable dtAreaTask = BiliAreaTask.GetAreaTaskList("", areaId, "", "1");
				if (areaTaskId != "")
				{
					dtAreaTask = dtAreaTask.Select("Fid IN (" + areaTaskId + ")").CopyToDataTable();
				}
				string areaName = BiliArea.GetAreaName(areaId);
				string data = "";
				string tag = "";
				if (type == "浏览器")
				{
					for (int i = 0; (i < dtAreaTask.Rows.Count) & QzUtil.IsExecute(seconds, context); i++)
					{
						string text = dtAreaTask.Rows[i]["FTaskKey"].ToString() ?? "";
						string text2 = dtAreaTask.Rows[i]["FReceiveUrl"].ToString() ?? "";
						data = data + "," + text2 + text;
					}
					if (dtAreaTask.Rows.Count > 0 && Util.GetJObject(dtAreaTask.Rows[0], "FAreaType") == "方式一")
					{
						string jObject2 = Util.GetJObject(dtAreaTask.Rows[0], "FBlackboardUrl");
						string jObject3 = Util.GetJObject(dtAreaTask.Rows[0], "FSubmissionTag");
						if (jObject2 != "" && jObject3 != "")
						{
							tag = " --blackboardUrl \"" + jObject2 + "\" --submissionTag \"" + jObject3 + "\"";
						}
					}
				}
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, "开始");
				for (int j = 0; j < dtCookies.Rows.Count; j++)
				{
					if (dtAreaTask.Rows.Count <= 0)
					{
						break;
					}
					if (!QzUtil.IsExecute(seconds, context))
					{
						break;
					}
					DataRow drCookie = dtCookies.Rows[j];
					if (type == "浏览器")
					{
						await Execute2(context, jobGroup, jobName, seconds, userId, areaId, areaName, data, tag, drCookie, bagUser);
					}
					else if (type == "接口")
					{
						taskList.Add(Task.Run(async delegate
						{
							await Execute3(context, jobGroup, jobName, seconds, userId, areaId, areaName, drCookie, dtAreaTask, areaTaskId, bagUser);
						}));
					}
				}
				foreach (Task item in taskList)
				{
					item.Wait();
				}
				BusBiliCookies.UpdateCookieStatus(bagUser, userId);
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, QzUtil.WriteStatus(context, seconds));
			}
			catch (Exception ex)
			{
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, ex.Message, ConsoleColor.Red);
			}
		}

		private static async Task Execute2(IJobExecutionContext context, string[] jobGroup, string jobName, int seconds, int userId, string areaId, string areaName, string data, string tag, DataRow drCookie, ConcurrentBag<JToken> bagUser)
		{
			string id = drCookie["Fid"].ToString() ?? "";
			string name = drCookie["FName"].ToString() ?? "";
			string text = drCookie["FKey"].ToString() ?? "";
			string json = drCookie["FHeaders"].ToString() ?? "";
			string text2 = drCookie["FProxyAddress"].ToString() ?? "";
			try
			{
				if (text2 != "")
				{
					Util.WriteLog(jobGroup[1], name, jobName, "已配置代理地址，无法使用*浏览器*操作！");
					return;
				}
				JObject jToken = JObject.Parse(json);
				string jObject = Util.GetJObject(jToken, "User-Agent");
				string text3 = AppSettings.GetVal("TokenPrefix") + Token.GetTokenValue(userId.ToString());
				string val = AppSettings.GetVal("TokenKey");
				string val2 = AppSettings.GetVal("Url", "Kestrel:Endpoints:Http");
				string text4 = Directory.GetCurrentDirectory() + "\\Util\\Chromium\\Bili-" + text;
				string fileName = text4 + "\\Chromium.exe";
				Util.WriteLog(jobGroup[1], name, jobName, "【" + areaName + "】开始更新");
				ProcessStartInfo processStartInfo = new ProcessStartInfo();
				processStartInfo.FileName = fileName;
				processStartInfo.Arguments = "-a " + text3 + " -c \"" + BusSysUser.Instance.User.Cookie + "\" -t Task -i " + id + " -k " + text + " -s 300 -u " + val2 + " --token " + val + " --ua \"" + jObject + "\" -m \"https://www.bilibili.com/\" --area " + areaId + " -d \"" + data.Trim(',') + "\"" + tag;
				ProcessStartInfo startInfo = processStartInfo;
				Process p = Process.Start(startInfo);
				await Task.Delay(2000);
				while (p != null && !p.HasExited && QzUtil.IsExecute(seconds, context))
				{
					await Task.Delay(2000);
				}
				if (p != null && !p.HasExited)
				{
					p.CloseMainWindow();
				}
				await Task.Delay(2000);
				Util.WriteLog(jobGroup[1], name, jobName, "【" + areaName + "】更新结束");
			}
			catch (Exception ex)
			{
				BusBiliCookies.AddCookieStatus(bagUser, id, ex.Message);
				Util.WriteLog(jobGroup[1], name, jobName, ex.Message, ConsoleColor.Red);
			}
		}

		private static async Task Execute3(IJobExecutionContext context, string[] jobGroup, string jobName, int seconds, int userId, string areaId, string areaName, DataRow drCookie, DataTable dtAreaTask, string areaTaskId, ConcurrentBag<JToken> bagUser)
		{
			string cookieId = drCookie["Fid"].ToString() ?? "";
			string name = drCookie["FKey"].ToString() ?? "";
			string cookie = drCookie["FCookie"].ToString() ?? "";
			string name2 = drCookie["FName"].ToString() ?? "";
			string header = drCookie["FHeaders"].ToString() ?? "";
			string csrf = drCookie["FCsrf"].ToString() ?? "";
			string text = drCookie["FProxyAddress"].ToString() ?? "";
			string userName = drCookie["FProxyUserName"].ToString() ?? "";
			string password = drCookie["FProxyPassword"].ToString() ?? "";
			string mileage = drCookie["FMileage"].ToString() ?? "";
			string aricles = drCookie["FAricles"].ToString() ?? "";
			try
			{
				Util.WriteLog(jobGroup[1], name2, jobName, "【" + areaName + "】开始更新(大约" + ((double)dtAreaTask.Rows.Count * 1.3).ToString("0.0") + "秒)");
				HttpClientHandler defaultHandler = null;
				if (text != "")
				{
					defaultHandler = new HttpClientHandler
					{
						Proxy = new WebProxy
						{
							Address = new Uri(text),
							Credentials = new NetworkCredential(userName, password)
						}
					};
				}
				Dictionary<string, string> dictionary = HttpClientFactory.FormataHeader(header, cookie);
				dictionary.Remove("Accept-Language");
				HttpClientFactory httpClientFactory = new HttpClientFactory(name, dictionary, defaultHandler);
				string typeName = jobGroup[1];
				string name3 = name2;
				string jobName2 = jobName;
				Util.WriteLog(typeName, name3, jobName2, await httpClientFactory.GetIp());
				await Task.Delay(1000);
				JArray jArrayCookieTask = new JArray();
				JArray taskGroup = new JArray();
				string jObject = Util.GetJObject(dtAreaTask.Rows[0], "FTotalKey");
				if (jObject != "")
				{
					JObject jObject2 = await httpClientFactory.Get("https://api.bilibili.com/x/task/totalv2?csrf=" + csrf + "&task_ids=" + jObject + "&web_location=888.117171");
					if (Util.GetJObject(jObject2, "code") == "0")
					{
						JArray jArray = Util.GetJObject<JArray>(jObject2["data"], "list") ?? new JArray();
						foreach (JToken item in jArray)
						{
							JArray jObject3 = Util.GetJObject<JArray>(item, "accumulative_check_points");
							if (jObject3 == null)
							{
								continue;
							}
							foreach (JToken item2 in jObject3)
							{
								string jObject4 = Util.GetJObject(item2, "sid");
								JArray jObject5 = Util.GetJObject<JArray>(item2, "list");
								if (jObject5 != null && jObject5.Count > 0)
								{
									string jObject6 = Util.GetJObject(jObject5[0], "cur_value");
									taskGroup.Add(new JObject
									{
										["sid"] = jObject4,
										["cur_value"] = jObject6
									});
								}
							}
						}
					}
				}
				JObject jObject7 = await httpClientFactory.Get("https://api.bilibili.com/x/web-interface/nav");
				if (Util.GetJObject(jObject7, "code") == "0")
				{
					string imgKey = MyRegex().Split(Util.GetJObject(Util.GetJObject<JToken>(jObject7["data"], "wbi_img"), "img_url")).ToList().Last()
						.Replace(".png", "");
					string subKey = MyRegex().Split(Util.GetJObject(Util.GetJObject<JToken>(jObject7["data"], "wbi_img"), "sub_url")).ToList().Last()
						.Replace(".png", "");
					int i = 0;
					while ((i < dtAreaTask.Rows.Count) & QzUtil.IsExecute(seconds, context))
					{
						string text2 = "";
						string taskKey = dtAreaTask.Rows[i]["FTaskKey"].ToString() ?? "";
						jobName2 = dtAreaTask.Rows[i]["FAreaType"].ToString() ?? "";
						string text3 = dtAreaTask.Rows[i]["FTaskType"].ToString() ?? "";
						int result;
						if (((!(text3 == "里程") && !(text3 == "每日")) || !(mileage == "0")) && (!(text3 == "投稿") || !(aricles == "0")))
						{
							if (jobName2 == "方式一")
							{
								text2 = "https://api.bilibili.com/x/activity_components/mission/info?";
							}
							string text4 = "task_id=" + taskKey + "&web_location=888.81821";
							string url = text2 + BusBiliUtil.GetWRid(imgKey, subKey, text4);
							JObject jObject8 = await httpClientFactory.Get(url);
							await Task.Delay(1200);
							if (Util.GetJObject(jObject8, "code") == "0")
							{
								JToken jObject9 = Util.GetJObject<JToken>(jObject8, "data");
								if (jobName2 == "方式一")
								{
									string text5 = Util.GetJObject(jObject9, "status") switch
									{
										"6" => "3", 
										"11" => "1", 
										"2" => "1", 
										"0" => "1", 
										_ => "0", 
									};
									if (!Util.GetJObject<bool>(jObject9, "task_finished") && text5 == "1")
									{
										text5 = "0";
									}
									JToken jObject10 = Util.GetJObject<JToken>(jObject9, "reward_info");
									JObject jObject11 = new JObject
									{
										["FUserId"] = userId,
										["FAreaId"] = areaId,
										["FCookieId"] = cookieId,
										["FAreaTaskId"] = dtAreaTask.Rows[i]["Fid"].ToString() ?? throw new Exception("参数不正确！"),
										["FTaskId"] = Util.GetJObject(jObject9, "task_id"),
										["FTaskName"] = Util.GetJObject(jObject9, "task_name") + ((Util.GetJObject(jObject9, "task_desc") == "") ? "" : (" - " + Util.GetJObject(jObject9, "task_desc"))),
										["FReceiveId"] = 0,
										["FReceiveStatus"] = text5,
										["FReceiveFrom"] = dtAreaTask.Rows[0]["FReceiveFrom"].ToString() ?? throw new Exception("参数不正确！"),
										["FAwardId"] = Util.GetJObject(jObject10, "award_inner_id"),
										["FAwardName"] = Util.GetJObject(jObject10, "award_name"),
										["FActivityId"] = Util.GetJObject(jObject9, "act_id"),
										["FActName"] = Util.GetJObject(jObject9, "act_name"),
										["FActId"] = 0,
										["FGroupId"] = 0,
										["FStockTotal"] = 0,
										["FStockConsumed"] = 0,
										["FPeriodTotal"] = 0,
										["FPeriodConsumed"] = 0,
										["FPrice"] = dtAreaTask.Rows[i]["FPrice"].ToString() ?? "0",
										["FCompulsory"] = dtAreaTask.Rows[i]["FCompulsory"].ToString() ?? "0",
										["FComplete"] = "",
										["FDaily"] = dtAreaTask.Rows[i]["FDaily"].ToString() ?? "0",
										["FSort"] = dtAreaTask.Rows[i]["FSort"].ToString() ?? "0",
										["FEnable"] = dtAreaTask.Rows[i]["FEnable"].ToString() ?? "0"
									};
									string jObject12 = Util.GetJObject(jObject10, "award_description");
									JToken jObject13 = Util.GetJObject<JToken>(jObject9, "stock_info");
									string jObject14 = Util.GetJObject(jObject13, "total_stock");
									string jObject15 = Util.GetJObject(jObject13, "day_stock");
									string text6 = Util.Regex(jObject12, "本奖品共", "份");
									if (text6 == "")
									{
										text6 = Util.Regex(jObject12, "本奖品", "份");
									}
									string text7 = Util.Regex(jObject12, "每日限量发放", "份");
									if (text7 == "")
									{
										text7 = Util.Regex(jObject12, "每日限量", "份");
									}
									text6 = ((text6 != "") ? text6 : jObject14);
									text7 = ((text7 != "") ? text7 : jObject15);
									if (!int.TryParse(text6, out result))
									{
										text6 = jObject14;
									}
									if (!int.TryParse(text7, out result))
									{
										text7 = jObject15;
									}
									int num = int.Parse(text6) - int.Parse(text6) * int.Parse(jObject14) / 100;
									int num2 = int.Parse(text7) - int.Parse(text7) * int.Parse(jObject15) / 100;
									jObject11["FStockTotal"] = text6;
									jObject11["FStockConsumed"] = num;
									jObject11["FPeriodTotal"] = text7;
									jObject11["FPeriodConsumed"] = num2;
									if (dtAreaTask.Rows[i]["FComplete"].ToString() == "1" && taskGroup.Count > 0)
									{
										List<JToken> list = taskGroup.Where((JToken token) => token["sid"]?.ToString() == taskKey).ToList();
										if (list.Count > 0)
										{
											jObject11["FComplete"] = Util.GetJObject(list[0], "cur_value");
										}
									}
									jArrayCookieTask.Add(jObject11);
								}
								else
								{
									JToken jObject16 = Util.GetJObject<JToken>(jObject9, "task_info");
									JToken jObject17 = Util.GetJObject<JToken>(jObject16, "reward_info");
									JToken jObject18 = Util.GetJObject<JToken>(jObject9, "act_info");
									JArray jObject19 = Util.GetJObject<JArray>(jObject16, "reward_stock_configs");
									JObject jObject11 = new JObject
									{
										["FUserId"] = userId,
										["FAreaId"] = areaId,
										["FCookieId"] = cookieId,
										["FAreaTaskId"] = dtAreaTask.Rows[0]["Fid"].ToString() ?? throw new Exception("参数不正确！"),
										["FReceiveFrom"] = dtAreaTask.Rows[0]["FReceiveFrom"].ToString() ?? throw new Exception("参数不正确！"),
										["FTaskId"] = Util.GetJObject(jObject16, "id"),
										["FTaskName"] = Util.GetJObject(jObject16, "task_name"),
										["FReceiveId"] = Util.GetJObject(jObject16, "receive_id"),
										["FReceiveStatus"] = Util.GetJObject(jObject16, "receive_status"),
										["FAwardId"] = Util.GetJObject(jObject17, "reward_id"),
										["FAwardName"] = Util.GetJObject(jObject17, "reward_name"),
										["FActivityId"] = Util.GetJObject(jObject17, "reward_act_id"),
										["FActName"] = Util.GetJObject(jObject18, "act_name"),
										["FActId"] = Util.GetJObject(jObject18, "id"),
										["FGroupId"] = 0,
										["FStockTotal"] = 0,
										["FStockConsumed"] = 0,
										["FPeriodTotal"] = 0,
										["FPeriodConsumed"] = 0,
										["FPrice"] = dtAreaTask.Rows[0]["FPrice"].ToString() ?? "0",
										["FCompulsory"] = dtAreaTask.Rows[0]["FCompulsory"].ToString() ?? "0",
										["FComplete"] = "",
										["FDaily"] = dtAreaTask.Rows[0]["FDaily"].ToString() ?? "0",
										["FSort"] = dtAreaTask.Rows[0]["FSort"].ToString() ?? "0",
										["FEnable"] = dtAreaTask.Rows[0]["FEnable"].ToString() ?? "0"
									};
									if (dtAreaTask.Rows[0]["FComplete"].ToString() == "1")
									{
										JArray jObject20 = Util.GetJObject<JArray>(jObject16, "group_list");
										if (jObject20 != null && jObject20.Count > 0)
										{
											jObject11["FComplete"] = Util.GetJObject(jObject20[0], "group_complete_num");
											jObject11["FGroupId"] = Util.GetJObject(jObject20[0], "group_id");
										}
									}
									if (jObject19 != null)
									{
										for (int num3 = 0; num3 < jObject19.Count; num3++)
										{
											string jObject21 = Util.GetJObject(jObject19[num3], "cycle_type");
											if (jObject21 == "1")
											{
												jObject11["FStockTotal"] = Util.GetJObject(jObject19[num3], "total");
												jObject11["FStockConsumed"] = Util.GetJObject(jObject19[num3], "consumed");
											}
											else if (jObject21 == "2")
											{
												jObject11["FPeriodTotal"] = Util.GetJObject(jObject19[num3], "total");
												jObject11["FPeriodConsumed"] = Util.GetJObject(jObject19[num3], "consumed");
											}
										}
									}
									jArrayCookieTask.Add(jObject11);
								}
							}
							else
							{
								Util.WriteLog(jobGroup[1], name2, jobName, Util.GetJObject(jObject8, "message"), ConsoleColor.Red);
								i--;
							}
						}
						result = i++;
					}
					if (jArrayCookieTask.Count > 0)
					{
						DataTable dt = jArrayCookieTask.ToObject<DataTable>();
						BiliCookiesTask.UpdateCookiesTask(dt, cookieId, areaId, userId, areaTaskId);
					}
					Util.WriteLog(jobGroup[1], name2, jobName, "【" + areaName + "】更新结束");
					return;
				}
				throw new Exception(Util.GetJObject(jObject7, "message"));
			}
			catch (Exception ex)
			{
				BusBiliCookies.AddCookieStatus(bagUser, cookieId, ex.Message);
				Util.WriteLog(jobGroup[1], name2, jobName, ex.Message, ConsoleColor.Red);
			}
		}
	}

	public class 更新任务信息Plus : IJob
	{
		public async Task Execute(IJobExecutionContext context)
		{
			string[] jobGroup = (context.JobDetail.JobDataMap.GetString("jobGroup") ?? "").Split("-");
			string jobName = context.JobDetail.JobDataMap.GetString("jobName") ?? "";
			ConcurrentBag<JToken> bagUser = new ConcurrentBag<JToken>();
			List<Task> taskList = new List<Task>();
			try
			{
				JObject @params = (context.JobDetail.JobDataMap.Get("params") as JObject) ?? new JObject();
				int seconds = context.JobDetail.JobDataMap.GetInt("seconds");
				int userId = context.JobDetail.JobDataMap.GetInt("userId");
				string areaId = Util.GetJObject(@params, "areaId");
				string cookieId = Util.GetJObject(@params, "cookieId");
				string jObject = Util.GetJObject(@params, "param");
				int.TryParse(jObject, out var time);
				if (time == 0)
				{
					time = 5000;
				}
				string keys = await QzUtil.Validate(userId, BusSysUser.Instance.User.Address, "bilibili", areaId);
				int jObject2 = Util.GetJObject<int>(@params, "difference");
				if (jObject2 != 0)
				{
					int num = new Random().Next(0, jObject2);
					seconds = ((seconds != 0) ? (seconds + num) : 0);
					DateTime datetime = DateTime.Now.AddSeconds(num);
					Util.WriteLog(jobGroup[1], jobGroup[0], jobName, datetime.ToString("yyyy-MM-dd HH:mm:ss") + "开始执行");
					while (DateTime.Now <= datetime && QzUtil.IsExecute(seconds, context))
					{
						await QzUtil.Delay();
					}
				}
				DataTable dtCookies = BiliCookies.GetCookiesList(cookieId, keys, userId, "1", areaId);
				DataTable dtAreaTask = BiliAreaTask.GetAreaTaskList("", areaId, "", "1");
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, "开始");
				HttpClientFactory httpClientFactory = new HttpClientFactory();
				JArray jArray = new JArray();
				foreach (DataRow row in dtAreaTask.Rows)
				{
					if (QzUtil.IsExecute(seconds, context))
					{
						string key = row["FTaskKey"].ToString() ?? "";
						await Task.Delay(100);
						JObject jObject3 = await httpClientFactory.Get("https://api.bilibili.com/x/activity_components/mission/info?task_id=" + key);
						if (!(Util.GetJObject(jObject3, "code") == "0"))
						{
							throw new Exception(Util.GetJObject(jObject3, "message"));
						}
						JToken item = jObject3["data"] ?? throw new Exception("未获取到参数");
						jArray.Add(item);
					}
				}
				for (int i = 0; i < dtCookies.Rows.Count; i++)
				{
					if (dtAreaTask.Rows.Count <= 0)
					{
						break;
					}
					if (!QzUtil.IsExecute(seconds, context))
					{
						break;
					}
					await Task.Delay(time);
					DataRow drCookie = dtCookies.Rows[i];
					await Execute3(context, jobGroup, jobName, seconds, userId, areaId, drCookie, dtAreaTask, jArray, bagUser);
				}
				foreach (Task item2 in taskList)
				{
					item2.Wait();
				}
				BusBiliCookies.UpdateCookieStatus(bagUser, userId);
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, QzUtil.WriteStatus(context, seconds));
			}
			catch (Exception ex)
			{
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, ex.Message, ConsoleColor.Red);
			}
		}

		private static async Task Execute3(IJobExecutionContext context, string[] jobGroup, string jobName, int seconds, int userId, string areaId, DataRow drCookie, DataTable dtAreaTask, JArray jArrayTask, ConcurrentBag<JToken> bagUser)
		{
			string cookieId = drCookie["Fid"].ToString() ?? "";
			string name = drCookie["FKey"].ToString() ?? "";
			string cookie = drCookie["FCookie"].ToString() ?? "";
			string name2 = drCookie["FName"].ToString() ?? "";
			string header = drCookie["FHeaders"].ToString() ?? "";
			string csrf = drCookie["FCsrf"].ToString() ?? "";
			string text = drCookie["FProxyAddress"].ToString() ?? "";
			string userName = drCookie["FProxyUserName"].ToString() ?? "";
			string password = drCookie["FProxyPassword"].ToString() ?? "";
			string mileage = drCookie["FMileage"].ToString() ?? "";
			string aricles = drCookie["FAricles"].ToString() ?? "";
			try
			{
				string areaName = Util.GetJObject(dtAreaTask.Rows[0], "FAreaName");
				Util.WriteLog(jobGroup[1], name2, jobName, "【" + areaName + "】开始更新");
				HttpClientHandler defaultHandler = null;
				if (text != "")
				{
					defaultHandler = new HttpClientHandler
					{
						Proxy = new WebProxy
						{
							Address = new Uri(text),
							Credentials = new NetworkCredential(userName, password)
						}
					};
				}
				Dictionary<string, string> dictionary = HttpClientFactory.FormataHeader(header, cookie);
				dictionary.Remove("Accept-Language");
				HttpClientFactory httpClientFactory = new HttpClientFactory(name, dictionary, defaultHandler);
				string typeName = jobGroup[1];
				string name3 = name2;
				Util.WriteLog(typeName, name3, jobName, await httpClientFactory.GetIp());
				JArray jArrayCookieTask = new JArray();
				new JArray();
				string jObject = Util.GetJObject(dtAreaTask.Rows[0], "FTotalKey");
				if (jObject != "")
				{
					JObject jObject2 = await httpClientFactory.Get("https://api.bilibili.com/x/task/totalv2?csrf=" + csrf + "&task_ids=" + jObject + "&web_location=888.117171");
					if (Util.GetJObject(jObject2, "code") == "0")
					{
						JArray jArray = Util.GetJObject<JArray>(jObject2["data"], "list") ?? new JArray();
						foreach (JToken item in jArray)
						{
							if (!QzUtil.IsExecute(seconds, context))
							{
								continue;
							}
							JArray jObject3 = Util.GetJObject<JArray>(item, "accumulative_check_points");
							if (jObject3 != null)
							{
								foreach (JToken item2 in jObject3)
								{
									string taskKey = Util.GetJObject(item2, "sid");
									string jObject4 = Util.GetJObject(item2, "status");
									IEnumerable<JToken> enumerable = jArrayTask.Where(delegate(JToken token)
									{
										if (token["task_id"] != null)
										{
											JToken? jToken = token["task_id"];
											if (jToken == null || jToken.Type != JTokenType.Null)
											{
												return token["task_id"]?.ToString() == taskKey;
											}
										}
										return false;
									});
									if (!enumerable.Any())
									{
										continue;
									}
									if (enumerable.Count() > 1)
									{
										throw new Exception("分区任务信息不正确！");
									}
									JArray jObject5 = Util.GetJObject<JArray>(item2, "list");
									string complete = "";
									if (jObject5 != null && jObject5.Count > 0)
									{
										complete = Util.GetJObject(jObject5[0], "cur_value");
									}
									foreach (JToken item3 in enumerable)
									{
										NewMethod(userId, areaId, dtAreaTask, cookieId, jArrayCookieTask, item3, complete, taskKey, jObject4, mileage, aricles);
									}
								}
								continue;
							}
							string taskKey2 = Util.GetJObject(item, "task_id");
							string jObject6 = Util.GetJObject(item, "task_status");
							IEnumerable<JToken> enumerable2 = jArrayTask.Where(delegate(JToken token)
							{
								if (token["task_id"] != null)
								{
									JToken? jToken = token["task_id"];
									if (jToken == null || jToken.Type != JTokenType.Null)
									{
										return token["task_id"]?.ToString() == taskKey2;
									}
								}
								return false;
							});
							if (!enumerable2.Any())
							{
								continue;
							}
							if (enumerable2.Count() > 1)
							{
								throw new Exception("分区任务信息不正确！");
							}
							foreach (JToken item4 in enumerable2)
							{
								NewMethod(userId, areaId, dtAreaTask, cookieId, jArrayCookieTask, item4, "", taskKey2, jObject6, mileage, aricles);
							}
						}
						if (jArrayCookieTask.Count > 0)
						{
							DataTable dt = jArrayCookieTask.ToObject<DataTable>();
							BiliCookiesTask.UpdateCookiesTask(dt, cookieId, areaId, userId, "");
						}
						Util.WriteLog(jobGroup[1], name2, jobName, "【" + areaName + "】更新结束");
						return;
					}
					throw new Exception(Util.GetJObject(jObject2, "message"));
				}
				throw new Exception("缺少参数，无法快速更新！");
			}
			catch (Exception ex)
			{
				BusBiliCookies.AddCookieStatus(bagUser, cookieId, ex.Message);
				Util.WriteLog(jobGroup[1], name2, jobName, ex.Message, ConsoleColor.Red);
			}
			static void NewMethod(int num, string text7, DataTable dataTable, string text8, JArray jArray2, JToken jToken, string text9, string text2, string status, string text4, string text5)
			{
				JToken jObject7 = Util.GetJObject<JToken>(jToken, "reward_info");
				DataRow[] array = dataTable.Select("FTaskKey='" + text2 + "'");
				if (array != null && array.Length == 1)
				{
					string text3 = array[0]["FTaskType"].ToString() ?? "";
					if (((!(text3 == "里程") && !(text3 == "每日")) || !(text4 == "0")) && (!(text3 == "投稿") || !(text5 == "0")))
					{
						string text6 = ((status == "3") ? "3" : ((!(status == "2")) ? "0" : "1"));
						status = text6;
						JObject jObject8 = new JObject
						{
							["FUserId"] = num,
							["FAreaId"] = text7,
							["FCookieId"] = text8,
							["FAreaTaskId"] = Util.GetJObject(array[0], "Fid"),
							["FTaskId"] = Util.GetJObject(jToken, "task_id"),
							["FTaskName"] = Util.GetJObject(jToken, "task_name") + ((Util.GetJObject(jToken, "task_desc") == "") ? "" : (" - " + Util.GetJObject(jToken, "task_desc"))),
							["FReceiveId"] = 0,
							["FReceiveStatus"] = status,
							["FReceiveFrom"] = Util.GetJObject(array[0], "FReceiveFrom"),
							["FAwardId"] = Util.GetJObject(jObject7, "award_inner_id"),
							["FAwardName"] = Util.GetJObject(jObject7, "award_name"),
							["FActivityId"] = Util.GetJObject(jToken, "act_id"),
							["FActName"] = Util.GetJObject(jToken, "act_name"),
							["FActId"] = 0,
							["FGroupId"] = 0,
							["FStockTotal"] = 0,
							["FStockConsumed"] = 0,
							["FPeriodTotal"] = 0,
							["FPeriodConsumed"] = 0,
							["FPrice"] = Util.GetJObject(array[0], "FPrice"),
							["FCompulsory"] = Util.GetJObject(array[0], "FCompulsory"),
							["FComplete"] = ((Util.GetJObject(array[0], "FComplete") == "1") ? text9 : ""),
							["FDaily"] = Util.GetJObject(array[0], "FDaily"),
							["FSort"] = Util.GetJObject(array[0], "FSort"),
							["FEnable"] = Util.GetJObject(array[0], "FEnable")
						};
						string jObject9 = Util.GetJObject(jObject7, "award_description");
						JToken jObject10 = Util.GetJObject<JToken>(jToken, "stock_info");
						string jObject11 = Util.GetJObject(jObject10, "total_stock");
						string jObject12 = Util.GetJObject(jObject10, "day_stock");
						string text10 = Util.Regex(jObject9, "本奖品共", "份");
						if (text10 == "")
						{
							text10 = Util.Regex(jObject9, "本奖品", "份");
						}
						string text11 = Util.Regex(jObject9, "每日限量发放", "份");
						if (text11 == "")
						{
							text11 = Util.Regex(jObject9, "每日限量", "份");
						}
						text10 = ((text10 != "") ? text10 : jObject11);
						text11 = ((text11 != "") ? text11 : jObject12);
						if (!int.TryParse(text10, out var result))
						{
							text10 = "0";
						}
						if (!int.TryParse(text11, out result))
						{
							text11 = "0";
						}
						int num2 = int.Parse(text10) - int.Parse(text10) * int.Parse(jObject11) / 100;
						int num3 = int.Parse(text11) - int.Parse(text11) * int.Parse(jObject12) / 100;
						jObject8["FStockTotal"] = text10;
						jObject8["FStockConsumed"] = num2;
						jObject8["FPeriodTotal"] = text11;
						jObject8["FPeriodConsumed"] = num3;
						jArray2.Add(jObject8);
					}
				}
			}
		}
	}

	public class 开启直播 : IJob
	{
		public async Task Execute(IJobExecutionContext context)
		{
			string[] jobGroup = (context.JobDetail.JobDataMap.GetString("jobGroup") ?? "").Split("-");
			string jobName = context.JobDetail.JobDataMap.GetString("jobName") ?? "";
			ConcurrentBag<JToken> bagUser = new ConcurrentBag<JToken>();
			List<Task> taskList = new List<Task>();
			try
			{
				JObject @params = (context.JobDetail.JobDataMap.Get("params") as JObject) ?? new JObject();
				int seconds = context.JobDetail.JobDataMap.GetInt("seconds");
				int userId = context.JobDetail.JobDataMap.GetInt("userId");
				string areaId = Util.GetJObject(@params, "areaId");
				string cookieId = Util.GetJObject(@params, "cookieId");
				string keys = await QzUtil.Validate(userId, BusSysUser.Instance.User.Address, "bilibili", areaId);
				string param = Util.GetJObject(@params, "param");
				int jObject = Util.GetJObject<int>(@params, "difference");
				if (jObject != 0)
				{
					int num = new Random().Next(0, jObject);
					seconds = ((seconds != 0) ? (seconds + num) : 0);
					DateTime datetime = DateTime.Now.AddSeconds(num);
					Util.WriteLog(jobGroup[1], jobGroup[0], jobName, datetime.ToString("yyyy-MM-dd HH:mm:ss") + "开始执行");
					while (DateTime.Now <= datetime && QzUtil.IsExecute(seconds, context))
					{
						await QzUtil.Delay();
					}
				}
				DataTable dtCookies = BiliCookies.GetCookiesList(cookieId, keys, userId, "1", areaId);
				DataTable dtCookiesLive = BiliLive.GetLivePathAll(cookieId, areaId, userId);
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, "开始");
				string areaName = BiliArea.GetAreaName(areaId);
				for (int i = 0; i < dtCookies.Rows.Count; i++)
				{
					if (!QzUtil.IsExecute(seconds, context))
					{
						break;
					}
					int index = i;
					DataRow drCookie = dtCookies.Rows[index];
					string text = drCookie["Fid"].ToString() ?? "";
					string name = drCookie["FName"].ToString() ?? "";
					if (drCookie["FKey"].ToString() == null)
					{
					}
					string json = drCookie["FHeaders"].ToString() ?? "";
					string livePath = "";
					string liveLog = "";
					JObject jToken = JObject.Parse(json);
					Util.GetJObject(jToken, "User-Agent");
					DataRow[] array = dtCookiesLive.Select("FCookieId=" + text);
					if (array != null && array.Length == 1)
					{
						livePath = array[0]["FLivePath"].ToString() ?? "";
						liveLog = array[0]["FLiveLog"].ToString() ?? "";
					}
					string text2 = Directory.GetCurrentDirectory() + "\\WWWRoot\\files\\Bili\\Live\\" + areaName + "\\";
					if (!Directory.Exists(text2) || Directory.GetFiles(text2).Length == 0)
					{
						Util.WriteLog(jobGroup[1], name, jobName, "请配置直播视频！", ConsoleColor.Red);
						BusBiliCookies.AddCookieStatus(bagUser, text, "请配置直播视频！");
						break;
					}
					livePath = "";
					string[] files = Directory.GetFiles(text2);
					ShuffleArray(files);
					string[] array2 = files;
					foreach (string text3 in array2)
					{
						string fileName = Path.GetFileName(text3);
						if (liveLog == "")
						{
							liveLog = fileName;
							livePath = text3;
							break;
						}
						if (!liveLog.Contains(fileName))
						{
							liveLog = liveLog + "," + fileName;
							livePath = text3;
							break;
						}
					}
					string[] array3 = liveLog.Split(',');
					if (array3.Length > 16)
					{
						liveLog = string.Join(',', array3.Skip(1));
					}
					if (livePath == "")
					{
						for (int k = 0; k < array3.Length; k++)
						{
							string text4 = text2 + "\\" + array3[k];
							if (File.Exists(text4))
							{
								livePath = text4;
								liveLog = string.Join(',', array3.Skip(k + 1)) + "," + array3[k];
								break;
							}
						}
					}
					taskList.Add(Task.Run(async delegate
					{
						await Execute3(index, jobGroup, jobName, areaId, areaName, drCookie, livePath, liveLog, param, userId, bagUser, seconds, context);
					}));
					await Task.Delay(3000);
				}
				foreach (Task item in taskList)
				{
					item.Wait();
				}
				BusBiliCookies.UpdateCookieStatus(bagUser, userId);
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, QzUtil.WriteStatus(context, seconds));
			}
			catch (Exception ex)
			{
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, ex.Message, ConsoleColor.Red);
			}
			static void ShuffleArray(string[] array4)
			{
				Random random = new Random();
				for (int num2 = array4.Length - 1; num2 > 0; num2--)
				{
					int num3 = random.Next(num2 + 1);
					ref string reference = ref array4[num3];
					ref string reference2 = ref array4[num2];
					string text5 = array4[num2];
					string text6 = array4[num3];
					reference = text5;
					reference2 = text6;
				}
			}
		}

		public static async Task Execute3(int index, string[] jobGroup, string jobName, string areaId, string areaName, DataRow drCookie, string livePath, string liveLog, string param, int userId, ConcurrentBag<JToken> bagUser, int seconds, IJobExecutionContext context)
		{
			string id = drCookie["Fid"].ToString() ?? "";
			string name = drCookie["FKey"].ToString() ?? "";
			string cookie = drCookie["FCookie"].ToString() ?? "";
			string name2 = drCookie["FName"].ToString() ?? "";
			string _uid = drCookie["FIdentifying"].ToString() ?? "";
			string text = drCookie["FHeaders"].ToString() ?? "";
			string csrf = drCookie["FCsrf"].ToString() ?? "";
			string roomNo = drCookie["FRoomId"].ToString() ?? "";
			string roomTitle = drCookie["FRoomTitle"].ToString() ?? "";
			string rtmpConfig = drCookie["FRoomRTMP"].ToString() ?? "";
			string text2 = drCookie["FProxyAddress"].ToString() ?? "";
			string text3 = drCookie["FProxyUserName"].ToString() ?? "";
			string text4 = drCookie["FProxyPassword"].ToString() ?? "";
			string proxy = "";
			try
			{
				JObject jToken = JObject.Parse(text);
				Util.GetJObject(jToken, "User-Agent");
				HttpClientHandler defaultHandler = null;
				if (text2 != "")
				{
					defaultHandler = new HttpClientHandler
					{
						Proxy = new WebProxy
						{
							Address = new Uri(text2),
							Credentials = new NetworkCredential(text3, text4)
						}
					};
					proxy = ((!(text3 != "")) ? (" -http_proxy " + text2) : ((!text2.Contains("https://")) ? (" -http_proxy http://" + text3 + ":" + text4 + "@" + text2.Replace("http://", "")) : (" -http_proxy https://" + text3 + ":" + text4 + "@" + text2.Replace("https://", ""))));
				}
				Dictionary<string, string> dictionary = HttpClientFactory.FormataHeader(text, cookie);
				dictionary.Remove("Accept-Language");
				HttpClientFactory httpClientFactory = new HttpClientFactory(name, dictionary, defaultHandler);
				string typeName = jobGroup[1];
				string name3 = name2;
				Util.WriteLog(typeName, name3, jobName, await httpClientFactory.GetIp());
				JObject jObject = await httpClientFactory.Get("https://api.bilibili.com/x/web-interface/nav");
				string imgKey = "";
				string subKey = "";
				if (Util.GetJObject(jObject, "code") == "0")
				{
					imgKey = MyRegex().Split(Util.GetJObject(Util.GetJObject<JToken>(jObject["data"], "wbi_img"), "img_url")).ToList().Last()
						.Replace(".png", "");
					subKey = MyRegex().Split(Util.GetJObject(Util.GetJObject<JToken>(jObject["data"], "wbi_img"), "sub_url")).ToList().Last()
						.Replace(".png", "");
				}
				string liveStatus = "0";
				int breakTimes = 0;
				string rtmp = "";
				Process process = null;
				Util.WriteLog(jobGroup[1], name2, jobName, "【" + areaName + "】开始直播");
				DanmakuApi dc = null;
				bool beginDm = false;
				bool myself = false;
				Task task = null;
				DataTable dt = BiliLive.GetLiveMsgList("直播间", id, areaId);
				if (param == "是")
				{
					try
					{
						dc = new DanmakuApi(int.Parse(roomNo), cookie);
						dc.ConnectAsync()?.Wait();
						dc.DanmakuMsgReceivedEvent += delegate(object sender, DanmakuReceivedEventArgs e)
						{
							try
							{
								JObject jObject7 = JObject.Parse(e.Danmaku.RawString);
								string userName = "";
								string text9 = "";
								JToken jToken3 = null;
								switch (Util.GetJObject(jObject7, "cmd"))
								{
								case "INTERACT_WORD":
									text9 = Util.GetJObject(jObject7["data"], "uid");
									userName = Util.GetJObject(jObject7["data"], "uname");
									if (text9 != _uid && (task == null || task.IsCompleted))
									{
										task = Task.Run(delegate
										{
											SendMsg(dt, "进入直播间", csrf, roomNo, httpClientFactory, areaName, userName);
										});
									}
									break;
								case "DANMU_MSG":
								{
									jToken3 = Util.GetJObject<JToken>(jObject7, "info");
									text9 = jToken3?[2]?[0]?.ToObject<string>();
									userName = jToken3?[2]?[1]?.ToObject<string>() ?? "";
									string msg = jToken3?[1]?.ToObject<string>() ?? "";
									if (text9 == _uid && !myself)
									{
										myself = true;
									}
									else
									{
										myself = false;
										if (task == null || task.IsCompleted)
										{
											task = Task.Run(delegate
											{
												SendMsg(dt, "收到弹幕", csrf, roomNo, httpClientFactory, areaName, userName, msg);
											});
										}
									}
									break;
								}
								case "SEND_GIFT":
								{
									jToken3 = Util.GetJObject<JToken>(jObject7, "data");
									string giftName = Util.GetJObject(jObject7["data"], "giftName");
									text9 = Util.GetJObject(jObject7["data"], "uid");
									userName = Util.GetJObject(jObject7["data"], "uname");
									if (task == null || task.IsCompleted)
									{
										task = Task.Run(delegate
										{
											SendMsg(dt, "收到礼物", csrf, roomNo, httpClientFactory, areaName, userName, "", giftName);
										});
									}
									break;
								}
								}
							}
							catch (JsonException)
							{
								Console.WriteLine("The string cannot be converted to JSON.");
							}
						};
					}
					catch (Exception ex)
					{
						Util.WriteLog(jobGroup[1], name2, jobName, "自动回弹幕启动失败|" + ex.Message, ConsoleColor.Red);
					}
				}
				else
				{
					beginDm = true;
				}
				while (QzUtil.IsExecute(seconds, context) && breakTimes < 6)
				{
					if (liveStatus == "0")
					{
						JObject jObject2 = await httpClientFactory.Get("https://api.live.bilibili.com/xlive/web-room/v1/index/getRoomBaseInfo?room_ids=" + roomNo + "&req_biz=video");
						if (!(Util.GetJObject(jObject2, "code") == "0"))
						{
							Util.WriteLog(jobGroup[1], name2, jobName, "获取直播间【" + roomNo + "】信息失败|" + Util.GetJObject(jObject2, "message") + "；正在重新获取", ConsoleColor.Red);
							breakTimes++;
							await Task.Delay(1000);
							continue;
						}
						JToken jObject3 = Util.GetJObject<JToken>(jObject2["data"], "by_room_ids");
						JToken jObject4 = Util.GetJObject<JToken>(jObject3, roomNo);
						Util.GetJObject(jObject4, "uid");
						string jObject5 = Util.GetJObject(jObject4, "title");
						Util.GetJObject(jObject4, "parent_area_id");
						liveStatus = Util.GetJObject(jObject4, "live_status");
						if (liveStatus == "0")
						{
							if (roomTitle != "" && jObject5 != roomTitle)
							{
								await httpClientFactory.Post("https://api.live.bilibili.com/room/v1/Room/update", "platform=pc&room_id=" + roomNo + "&title=" + roomTitle + "&csrf_token=" + csrf + "&csrf=" + csrf);
								Thread.Sleep(2000);
							}
							string text5 = "access_key=&appkey=" + Util.GetCookieByKey(cookie, "appkey") + "&area_v2=" + areaId + "&build=7482&csrf=" + Util.GetCookieByKey(cookie, "bili_jct") + "&csrf_token=" + Util.GetCookieByKey(cookie, "bili_jct") + "&platform=pc_link&room_id=" + roomNo + "&ts=" + DateTimeOffset.Now.ToUnixTimeSeconds() + "&type=2&version=6.4.0.7482";
							text5 = BusBiliUtil.GetWRidPlus(imgKey, subKey, text5);
							jObject2 = await httpClientFactory.Post("https://api.live.bilibili.com/room/v1/Room/startLive", text5);
							if (!(Util.GetJObject(jObject2, "code") == "0"))
							{
								throw new Exception(Util.GetJObject(jObject2, "message"));
							}
							JToken jObject6 = Util.GetJObject<JToken>(jObject2["data"], "rtmp");
							rtmp = Util.GetJObject(jObject6, "addr") + Util.GetJObject(jObject6, "code");
							liveStatus = "1";
						}
						else if (rtmp == "")
						{
							string text6 = "access_key=&appkey=" + Util.GetCookieByKey(cookie, "appkey") + "&build=7482&csrf=" + Util.GetCookieByKey(cookie, "bili_jct") + "&csrf_token=" + Util.GetCookieByKey(cookie, "bili_jct") + "&platform=pc_link&room_id=" + roomNo + "&ts=" + DateTimeOffset.Now.ToUnixTimeSeconds() + "&version=6.4.0.7482";
							text6 = BusBiliUtil.GetWRidPlus(imgKey, subKey, text6);
							jObject2 = await httpClientFactory.Post("https://api.live.bilibili.com/room/v1/Room/stopLive", text6);
							if (!(Util.GetJObject(jObject2, "code") == "0"))
							{
								Util.WriteLog(jobGroup[1], name2, jobName, "FetchWebUpStreamAddr：" + Util.GetJObject(jObject2, "message") + "；正在重试", ConsoleColor.Red);
								breakTimes++;
								continue;
							}
							liveStatus = "0";
						}
					}
					if (liveStatus == "1")
					{
						if (rtmp == "")
						{
							Util.WriteLog(jobGroup[1], name2, jobName, "RTMP获取失败！正在重新获取！", ConsoleColor.Red);
							liveStatus = "0";
							breakTimes++;
							continue;
						}
						if (process == null || process.HasExited)
						{
							string text7 = rtmpConfig.Replace("{视频路径}", "\"" + livePath + "\"").Replace("{RTMP}", "\"" + rtmp + "\"").Replace(" {代理地址}", proxy.Replace("&", ""));
							if (AppSettings.GetVal("Log") == "True")
							{
								Console.WriteLine(">>>" + Directory.GetCurrentDirectory() + "\\Util\\FFmpeg\\ffmpeg.exe " + text7);
							}
							process = Util.ProcessStart("\\FFmpeg\\ffmpeg.exe", text7);
							breakTimes++;
							if (!beginDm)
							{
								beginDm = true;
								SendMsg(dt, "开始直播", csrf, roomNo, httpClientFactory, areaName);
							}
						}
					}
					for (int i = 0; i < 30; i++)
					{
						if (!QzUtil.IsExecute(seconds, context))
						{
							break;
						}
						if (process == null)
						{
							break;
						}
						if (process.HasExited)
						{
							break;
						}
						await Task.Delay(1000);
					}
					if (process == null || process.HasExited)
					{
						liveStatus = "0";
					}
				}
				if (param == "是")
				{
					SendMsg(dt, "结束直播", csrf, roomNo, httpClientFactory, areaName);
				}
				string text8 = "access_key=&appkey=" + Util.GetCookieByKey(cookie, "appkey") + "&build=7482&csrf=" + Util.GetCookieByKey(cookie, "bili_jct") + "&csrf_token=" + Util.GetCookieByKey(cookie, "bili_jct") + "&platform=pc_link&room_id=" + roomNo + "&ts=" + DateTimeOffset.Now.ToUnixTimeSeconds() + "&version=6.4.0.7482";
				text8 = BusBiliUtil.GetWRidPlus(imgKey, subKey, text8);
				JObject jToken2 = await httpClientFactory.Post("https://api.live.bilibili.com/room/v1/Room/stopLive", text8);
				if (Util.GetJObject(jToken2, "code") == "0")
				{
					Util.WriteLog(jobGroup[1], name2, jobName, "【" + areaName + "】结束直播");
				}
				else
				{
					Util.WriteLog(jobGroup[1], name2, jobName, "【" + areaName + "】结束直播失败|" + Util.GetJObject(jToken2, "message"), ConsoleColor.Red);
				}
				dc?.Dispose();
				await Task.Delay(index * 1000);
				BiliLive.SaveLiveLog(id, areaId, userId, liveLog);
				Util.WriteLog(jobGroup[1], name2, jobName, "【" + areaName + "】直播结束" + ((breakTimes >= 6) ? "，重试次数过多,已超过5次" : ""));
			}
			catch (Exception ex2)
			{
				BusBiliCookies.AddCookieStatus(bagUser, id, ex2.Message);
				Util.WriteLog(jobGroup[1], name2, jobName, ex2.Message, ConsoleColor.Red);
			}
		}

		private static async void SendMsg(DataTable dtDm, string trigger, string csrf, string roomNo, HttpClientFactory httpClientFactory, string areaName, string userName = "", string keyword = "", string giftName = "")
		{
			string text = "";
			DataTable dataTable = dtDm.Copy();
			if (keyword != "")
			{
				text = " AND ((FLogic='与' AND '" + keyword + "' LIKE '%'+FKeyword1+'%' AND '" + keyword + "' LIKE '%'+FKeyword2+'%' AND '" + keyword + "' LIKE '%'+FKeyword3+'%')";
				text = text + " OR (FLogic='或' AND ('" + keyword + "' LIKE '%'+FKeyword1+'%' OR (FKeyword2<>'' AND '" + keyword + "' LIKE '%'+FKeyword2+'%') OR (FKeyword3<>'' AND '" + keyword + "' LIKE '%'+FKeyword3+'%') ) ) )";
			}
			DataRow[] array = dataTable.Select("FTrigger='" + trigger + "'" + text);
			if (array != null && array.Length != 0)
			{
				dataTable = array.CopyToDataTable();
				string msg = "";
				if (dataTable.Rows.Count > 0)
				{
					msg = dataTable.Rows[new Random().Next(0, dataTable.Rows.Count - 1)]["FMsg"].ToString() ?? "";
				}
				if (msg != "")
				{
					msg = msg.Replace("{礼物名称}", giftName);
					msg = msg.Replace("{用户名称}", userName);
					msg = msg.Replace("{用户名称4位}", (userName.Length > 5) ? userName.Substring(0, 4) : userName);
					msg = msg.Replace("{游戏分区}", areaName);
					await Task.Delay(3000);
					await BulletScreen(csrf, roomNo, httpClientFactory, msg);
					await Task.Delay(3000);
				}
			}
		}
	}

	public class 定时投稿 : IJob
	{
		public async Task Execute(IJobExecutionContext context)
		{
			string[] jobGroup = (context.JobDetail.JobDataMap.GetString("jobGroup") ?? "").Split("-");
			string jobName = context.JobDetail.JobDataMap.GetString("jobName") ?? "";
			ConcurrentBag<JToken> bagUser = new ConcurrentBag<JToken>();
			new List<Task>();
			try
			{
				JObject @params = (context.JobDetail.JobDataMap.Get("params") as JObject) ?? new JObject();
				int seconds = context.JobDetail.JobDataMap.GetInt("seconds");
				int userId = context.JobDetail.JobDataMap.GetInt("userId");
				string areaId = Util.GetJObject(@params, "areaId");
				string cookieId = Util.GetJObject(@params, "cookieId");
				string keys = await QzUtil.Validate(userId, BusSysUser.Instance.User.Address, "bilibili", areaId);
				Util.GetJObject(@params, "type");
				string jObject = Util.GetJObject(@params, "param");
				int time = 1;
				int num = 1;
				int title = 1;
				string[] array = jObject.Split(',');
				if (array.Length != 0 && int.TryParse(array[0], out var result))
				{
					time = result;
				}
				if (array.Length > 1 && int.TryParse(array[1], out var result2))
				{
					num = result2;
				}
				if (array.Length > 2 && int.TryParse(array[2], out var result3))
				{
					title = result3;
				}
				int jObject2 = Util.GetJObject<int>(@params, "difference");
				if (jObject2 != 0)
				{
					int num2 = new Random().Next(0, jObject2);
					seconds = ((seconds != 0) ? (seconds + num2) : 0);
					DateTime datetime = DateTime.Now.AddSeconds(num2);
					Util.WriteLog(jobGroup[1], jobGroup[0], jobName, datetime.ToString("yyyy-MM-dd HH:mm:ss") + "开始执行");
					while (DateTime.Now <= datetime && QzUtil.IsExecute(seconds, context))
					{
						await QzUtil.Delay();
					}
				}
				DataTable cookiesList = BiliCookies.GetCookiesList(cookieId, keys, userId, "1", areaId);
				string ids = string.Join(",", from row in cookiesList.Select("FAricles=1").AsEnumerable()
					select row["Fid"].ToString());
				string areaName = BiliArea.GetAreaName(areaId);
				if (QzUtil.IsExecute(seconds, context))
				{
					JArray cookieArticlesInfo = BiliArticles.GetCookieArticlesInfo(ids, title.ToString(), areaId, areaName, userId);
					await BiliArticles.Submit(cookieArticlesInfo, areaId, time, num, userId, jobName, seconds, context);
				}
				BusBiliCookies.UpdateCookieStatus(bagUser, userId);
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, QzUtil.WriteStatus(context, seconds));
			}
			catch (Exception ex)
			{
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, ex.Message, ConsoleColor.Red);
			}
		}
	}

	public class 弹幕礼物观看 : IJob
	{
		public async Task Execute(IJobExecutionContext context)
		{
			string[] jobGroup = (context.JobDetail.JobDataMap.GetString("jobGroup") ?? "").Split("-");
			string jobName = context.JobDetail.JobDataMap.GetString("jobName") ?? "";
			ConcurrentBag<JToken> bagUser = new ConcurrentBag<JToken>();
			List<Task> taskList = new List<Task>();
			try
			{
				JObject jToken = (context.JobDetail.JobDataMap.Get("params") as JObject) ?? new JObject();
				int seconds = context.JobDetail.JobDataMap.GetInt("seconds");
				int userId = context.JobDetail.JobDataMap.GetInt("userId");
				string areaId = Util.GetJObject(jToken, "areaId");
				string cookieId = Util.GetJObject(jToken, "cookieId");
				string type = Util.GetJObject(jToken, "type");
				string param = Util.GetJObject(jToken, "param", "否");
				int jObject = Util.GetJObject<int>(jToken, "difference");
				if (jObject != 0)
				{
					int num = new Random().Next(0, jObject);
					seconds = ((seconds != 0) ? (seconds + num) : 0);
					DateTime datetime = DateTime.Now.AddSeconds(num);
					Util.WriteLog(jobGroup[1], jobGroup[0], jobName, datetime.ToString("yyyy-MM-dd HH:mm:ss") + "开始执行");
					while (DateTime.Now <= datetime && QzUtil.IsExecute(seconds, context))
					{
						await QzUtil.Delay();
					}
				}
				DataTable dtCookies = BiliCookies.GetCookiesList(cookieId, "", userId, "0");
				DataTable dtLiveConfig = BiliLive.GetLiveConfigList(cookieId, areaId, userId);
				HttpClientFactory httpClientFactory = new HttpClientFactory();
				string identifying = "";
				for (int i = 0; i < dtLiveConfig.Rows.Count; i++)
				{
					string roomNo = Util.GetJObject(dtLiveConfig.Rows[i], "FRoomNo");
					JObject jObject2 = await httpClientFactory.Get("https://api.live.bilibili.com/xlive/web-room/v1/index/getRoomBaseInfo?room_ids=" + roomNo + "&req_biz=video");
					if (Util.GetJObject(jObject2, "code") == "0")
					{
						JToken jObject3 = Util.GetJObject<JToken>(jObject2["data"], "by_room_ids");
						JToken jObject4 = Util.GetJObject<JToken>(jObject3, roomNo);
						string jObject5 = Util.GetJObject(jObject4, "uid");
						identifying += ((identifying == "") ? jObject5 : ("," + jObject5));
					}
				}
				await QzUtil.Validate2(userId, BusSysUser.Instance.User.Address, identifying.TrimEnd(','));
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, "开始");
				for (int i = 0; i < dtCookies.Rows.Count; i++)
				{
					if (!QzUtil.IsExecute(seconds, context))
					{
						break;
					}
					DataRow drCookie = dtCookies.Rows[i];
					DataRow[] drLiveConfig = dtLiveConfig.Select("FCookieId=" + drCookie["Fid"].ToString());
					if (drLiveConfig == null || drLiveConfig.Length == 0)
					{
						continue;
					}
					await Task.Delay(3000);
					if (type == "浏览器")
					{
						taskList.Add(Task.Run(async delegate
						{
							await Execute2(jobGroup, jobName, drCookie, drLiveConfig.CopyToDataTable(), userId, param, bagUser, seconds, context);
						}));
					}
					else if (type == "接口")
					{
						taskList.Add(Task.Run(async delegate
						{
							await Execute3(jobGroup, jobName, drCookie, drLiveConfig.CopyToDataTable(), param, bagUser, seconds, context);
						}));
					}
				}
				foreach (Task item in taskList)
				{
					item.Wait();
				}
				BusBiliCookies.UpdateCookieStatus(bagUser, userId);
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, QzUtil.WriteStatus(context, seconds));
			}
			catch (Exception ex)
			{
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, ex.Message, ConsoleColor.Red);
			}
		}

		private static async Task Execute2(string[] jobGroup, string jobName, DataRow drCookie, DataTable dtLiveConfig, int userId, string isLive, ConcurrentBag<JToken> bagUser, int seconds, IJobExecutionContext context)
		{
			string cookieId = drCookie["Fid"].ToString() ?? "";
			string key = drCookie["FKey"].ToString() ?? "";
			string name = drCookie["FName"].ToString() ?? "";
			string json = drCookie["FHeaders"].ToString() ?? "";
			try
			{
				string text = drCookie["FProxyAddress"].ToString() ?? "";
				if (text != "")
				{
					Util.WriteLog(jobGroup[1], name, jobName, "已配置代理地址，无法使用*浏览器*操作！");
					return;
				}
				JObject jToken = JObject.Parse(json);
				string userAgent = Util.GetJObject(jToken, "User-Agent");
				string auth = AppSettings.GetVal("TokenPrefix") + Token.GetTokenValue(userId.ToString());
				string tokenKey = AppSettings.GetVal("TokenKey");
				string url = AppSettings.GetVal("Url", "Kestrel:Endpoints:Http");
				DataRow[] array = dtLiveConfig.Select("FCookieId=" + cookieId);
				if (array != null && array.Length != 0)
				{
					HttpClientFactory httpClientFactory = new HttpClientFactory(key);
					DataTable dt = array.CopyToDataTable();
					for (int i = 0; i < dt.Rows.Count; i++)
					{
						DataRow dr = dt.Rows[i];
						string roomNo = Util.GetJObject(dr, "FRoomNo");
						if (roomNo == "")
						{
							Util.WriteLog(jobGroup[1], name, jobName, "直播间房间号不能为空！", ConsoleColor.Red);
							continue;
						}
						JObject jObject = await httpClientFactory.Get("https://api.live.bilibili.com/xlive/web-room/v1/index/getRoomBaseInfo?room_ids=" + roomNo + "&req_biz=video");
						if (Util.GetJObject(jObject, "code") == "0")
						{
							JToken jObject2 = Util.GetJObject<JToken>(jObject["data"], "by_room_ids");
							JToken jObject3 = Util.GetJObject<JToken>(jObject2, roomNo);
							string jObject4 = Util.GetJObject(jObject3, "area_id");
							string jObject5 = Util.GetJObject(jObject3, "area_name");
							Util.GetJObject(jObject3, "parent_area_id");
							if (jObject4 == "" || jObject4 == "0")
							{
								Util.WriteLog(jobGroup[1], name, jobName, roomNo + "分区获取失败！", ConsoleColor.Red);
								continue;
							}
							if (jObject4 != Util.GetJObject(dr, "FAreaId") && isLive == "否")
							{
								Util.WriteLog(jobGroup[1], name, jobName, "直播间【" + roomNo + "】分区不一致，当前：" + jObject5 + "！", ConsoleColor.Red);
								continue;
							}
							string jObject6 = Util.GetJObject(jObject3, "live_status");
							if (jObject6 == "0" && isLive == "否")
							{
								Util.WriteLog(jobGroup[1], name, jobName, "直播间【" + roomNo + "】未开播！", ConsoleColor.Red);
								continue;
							}
							Util.WriteLog(jobGroup[1], name, jobName, "直播间【" + roomNo + "】开始");
							string text2 = Util.GetJObject(dr, "FGiftCode");
							string text3 = Util.GetJObject(dr, "FGiftNum");
							if (text2 == "" || text3 == "")
							{
								text2 = "0";
								text3 = "0";
							}
							string jObject7 = Util.GetJObject(dr, "FBulletScreenNum");
							jObject7 = ((jObject7 == "") ? "0" : jObject7);
							string jObject8 = Util.GetJObject(dr, "FWatchMinutes");
							jObject8 = ((jObject8 == "") ? "0" : jObject8);
							string text4 = "http://live.bilibili.com/" + roomNo;
							string text5 = "-a " + auth + " -c \"" + BusSysUser.Instance.User.Cookie + "\" -t LiveTask -i " + cookieId + " -k " + key + " -s " + seconds + " -u " + url + " --token " + tokenKey + " --ua \"" + userAgent + "\" -m " + text4;
							text5 = text5 + " --giftCode " + text2 + " --giftNum " + text3 + " --bulletScreenNum " + jObject7 + " --watchMinutes " + jObject8;
							Process p = Util.ProcessStart("\\Chromium\\Bili-" + key + "\\Chromium.exe", text5);
							await QzUtil.Delay(1000, 2000);
							while (p != null && !p.HasExited && QzUtil.IsExecute(seconds, context))
							{
								await QzUtil.Delay();
							}
							if (p != null && !p.HasExited)
							{
								p.CloseMainWindow();
							}
							await QzUtil.Delay(1000, 2000);
							Util.WriteLog(jobGroup[1], name, jobName, "直播间【" + roomNo + "】结束");
						}
						else
						{
							Util.WriteLog(jobGroup[1], name, jobName, "直播间【" + roomNo + "】信息失败|" + Util.GetJObject(jObject, "message"), ConsoleColor.Red);
						}
					}
				}
				Util.WriteLog(jobGroup[1], name, jobName, "全部结束");
			}
			catch (Exception ex)
			{
				Util.WriteLog(jobGroup[1], name, jobName, ex.Message, ConsoleColor.Red);
				BusBiliCookies.AddCookieStatus(bagUser, cookieId, ex.Message);
			}
		}

		private static async Task Execute3(string[] jobGroup, string jobName, DataRow drCookie, DataTable dtLiveConfig, string isLive, ConcurrentBag<JToken> bagUser, int seconds, IJobExecutionContext context)
		{
			string cookieId = drCookie["Fid"].ToString() ?? "";
			string key = drCookie["FKey"].ToString() ?? "";
			string cookie = drCookie["FCookie"].ToString() ?? "";
			string name = drCookie["FName"].ToString() ?? "";
			string header = drCookie["FHeaders"].ToString() ?? "";
			string csrf = drCookie["FCsrf"].ToString() ?? "";
			string uid = drCookie["FIdentifying"].ToString() ?? "";
			string text = drCookie["FProxyAddress"].ToString() ?? "";
			string userName = drCookie["FProxyUserName"].ToString() ?? "";
			string password = drCookie["FProxyPassword"].ToString() ?? "";
			try
			{
				Util.WriteLog(jobGroup[1], name, jobName, "弹幕礼物观看开始");
				JObject jToken = JObject.Parse(header);
				string ua = Util.GetJObject(jToken, "User-Agent");
				HttpClientHandler defaultHandler = null;
				if (text != "")
				{
					defaultHandler = new HttpClientHandler
					{
						Proxy = new WebProxy
						{
							Address = new Uri(text),
							Credentials = new NetworkCredential(userName, password)
						}
					};
				}
				Dictionary<string, string> dictionary = HttpClientFactory.FormataHeader(header, cookie);
				dictionary.Remove("Accept-Language");
				HttpClientFactory httpClientFactory = new HttpClientFactory(key, dictionary, defaultHandler);
				HttpClientFactory httpClientFactoryDm = new HttpClientFactory(key, dictionary, defaultHandler);
				string typeName = jobGroup[1];
				string name2 = name;
				string jobName2 = jobName;
				Util.WriteLog(typeName, name2, jobName2, await httpClientFactory.GetIp());
				string liveBuvid = Util.GetCookieByKey(cookie, "LIVE_BUVID");
				if (liveBuvid == "")
				{
					string jObject = Util.GetJObject(Util.GetJObject<JToken>(await httpClientFactory.Get("https://api.live.bilibili.com/news/v1/notice/recom?product=live"), "responseHeaders"), "Set-Cookie");
					liveBuvid = Util.GetCookieByKey(jObject, "LIVE_BUVID");
					if (liveBuvid != "")
					{
						cookie = cookie.Trim() + "; LIVE_BUVID=" + liveBuvid;
						dictionary = HttpClientFactory.FormataHeader(header, cookie);
						dictionary.Remove("Accept-Language");
						httpClientFactory = new HttpClientFactory(key, dictionary, defaultHandler);
						httpClientFactoryDm = new HttpClientFactory(key, dictionary, defaultHandler);
					}
				}
				for (int i = 0; i < dtLiveConfig.Rows.Count; i++)
				{
					if (!QzUtil.IsExecute(seconds, context))
					{
						break;
					}
					List<Task> taskList = new List<Task>();
					int index = i;
					string roomNo = Util.GetJObject(dtLiveConfig.Rows[index], "FRoomNo");
					string giftName = Util.GetJObject(dtLiveConfig.Rows[index], "FGiftName");
					string giftCode = Util.GetJObject(dtLiveConfig.Rows[index], "FGiftCode");
					int.TryParse(Util.GetJObject(dtLiveConfig.Rows[index], "FWatchMinutes"), out var watchMinutes);
					int.TryParse(Util.GetJObject(dtLiveConfig.Rows[index], "FBulletScreenNum"), out var bulletScreenNum);
					int.TryParse(Util.GetJObject(dtLiveConfig.Rows[index], "FGiftNum"), out var giftNum);
					int.TryParse(Util.GetJObject(dtLiveConfig.Rows[index], "FGiftPrice"), out var giftPrice);
					await Task.Delay(2000);
					JObject jObject2 = await httpClientFactory.Get("https://api.live.bilibili.com/xlive/web-room/v1/index/getRoomBaseInfo?room_ids=" + roomNo + "&req_biz=video");
					if (Util.GetJObject(jObject2, "code") == "0")
					{
						JToken jObject3 = Util.GetJObject<JToken>(jObject2["data"], "by_room_ids");
						JToken jObject4 = Util.GetJObject<JToken>(jObject3, roomNo);
						string ruid = Util.GetJObject(jObject4, "uid");
						string areaId = Util.GetJObject(jObject4, "area_id");
						jobName2 = Util.GetJObject(jObject4, "area_name");
						string parentAreaId = Util.GetJObject(jObject4, "parent_area_id");
						if (areaId == "" || areaId == "0")
						{
							throw new Exception("分区获取失败！");
						}
						string jObject5 = Util.GetJObject(jObject4, "live_status");
						if (areaId != Util.GetJObject(dtLiveConfig.Rows[index], "FAreaId") && isLive == "否")
						{
							Util.WriteLog(jobGroup[1], name, jobName, "直播间【" + roomNo + "】分区不一致，当前：" + jobName2 + "！", ConsoleColor.Red);
							continue;
						}
						if (jObject5 == "0" && isLive == "否")
						{
							Util.WriteLog(jobGroup[1], name, jobName, "直播间【" + roomNo + "】未开播！");
							continue;
						}
						taskList.Add(Task.Run(async delegate
						{
							await Watch(jobGroup, jobName, name, csrf, roomNo, ruid, parentAreaId, areaId, ++watchMinutes, liveBuvid, ua, httpClientFactory, seconds, context);
						}));
						await Task.Delay(2000);
						if (bulletScreenNum > 0)
						{
							DataTable dtBulletScreen = BiliLive.GetLiveMsgList("弹幕礼物观看", cookieId, areaId);
							if (dtBulletScreen.Rows.Count == 0)
							{
								Util.WriteLog(jobGroup[1], name, jobName, "【" + jobName2 + "】无弹幕，请配置弹幕系统！", ConsoleColor.Red);
							}
							taskList.Add(Task.Run(async delegate
							{
								await BulletScreenNum(jobGroup, jobName, name, csrf, roomNo, dtBulletScreen, bulletScreenNum, (watchMinutes == 0) ? 5000 : (watchMinutes * 60 / bulletScreenNum), httpClientFactoryDm, seconds, context);
							}));
						}
						await Task.Delay(2000);
						if (giftCode != "" && giftNum > 0 && giftPrice > 0)
						{
							taskList.Add(Task.Run(async delegate
							{
								await Gift(jobGroup, jobName, name, csrf, roomNo, uid, ruid, giftCode, giftName, giftNum, giftPrice, httpClientFactory, seconds, context);
							}));
						}
						foreach (Task item in taskList)
						{
							item.Wait();
						}
					}
					else
					{
						Util.WriteLog(jobGroup[1], name, jobName, "直播间【" + roomNo + "】信息失败|" + Util.GetJObject(jObject2, "message"), ConsoleColor.Red);
					}
				}
				Util.WriteLog(jobGroup[1], name, jobName, "全部结束");
			}
			catch (Exception ex)
			{
				BusBiliCookies.AddCookieStatus(bagUser, cookieId, ex.Message);
				Util.WriteLog(jobGroup[1], name, jobName, ex.Message, ConsoleColor.Red);
			}
		}

		public static async Task BulletScreenNum(string[] jobGroup, string jobName, string name, string csrf, string roomNo, DataTable dtBulletScreen, int dtBulletScreenNum, int interval, HttpClientFactory httpClientFactory, int seconds, IJobExecutionContext context)
		{
			_ = 1;
			try
			{
				for (int i = 0; i < dtBulletScreenNum && QzUtil.IsExecute(seconds, context); i++)
				{
					string msg = dtBulletScreen.Rows[new Random().Next(0, dtBulletScreen.Rows.Count - 1)]["FMsg"].ToString() ?? "";
					if (!(msg != ""))
					{
						continue;
					}
					JObject jToken = await BulletScreen(csrf, roomNo, httpClientFactory, msg);
					if (Util.GetJObject(jToken, "code") == "0")
					{
						Util.WriteLog(jobGroup[1], name, jobName, "直播间【" + roomNo + "】发送弹幕:" + msg);
					}
					else
					{
						if (Util.GetJObject(jToken, "code") != "0" && Util.GetJObject(jToken, "message").Contains("频率"))
						{
							i--;
							continue;
						}
						Util.WriteLog(jobGroup[1], name, jobName, Util.GetJObject(jToken, "message"), ConsoleColor.Red);
					}
					for (int j = 0; j < interval; j++)
					{
						if (!QzUtil.IsExecute(seconds, context))
						{
							break;
						}
						if (i == dtBulletScreenNum - 1)
						{
							break;
						}
						await QzUtil.Delay(1000, 1000);
					}
				}
				Util.WriteLog(jobGroup[1], name, jobName, "直播间【" + roomNo + "】发送弹幕完毕");
			}
			catch (Exception ex)
			{
				Util.WriteLog(jobGroup[1], name, jobName, ex.Message, ConsoleColor.Red);
			}
		}

		public static async Task Gift(string[] jobGroup, string jobName, string name, string csrf, string roomNo, string uid, string ruid, string giftCode, string giftName, int giftNum, int giftPrice, HttpClientFactory httpClientFactory, int seconds, IJobExecutionContext context)
		{
			Util.WriteLog(jobGroup[1], name, jobName, "发礼物前");
			try
			{
				JObject jObject = await httpClientFactory.Get("https://api.bilibili.com/x/web-interface/nav");
				if (Util.GetJObject(jObject, "code") == "0")
				{
					string imgKey = MyRegex().Split(Util.GetJObject(Util.GetJObject<JToken>(jObject["data"], "wbi_img"), "img_url")).ToList().Last()
						.Replace(".png", "");
					string subKey = MyRegex().Split(Util.GetJObject(Util.GetJObject<JToken>(jObject["data"], "wbi_img"), "sub_url")).ToList().Last()
						.Replace(".png", "");
					for (int i = 0; i < giftNum; i++)
					{
						if (!QzUtil.IsExecute(seconds, context))
						{
							break;
						}
						string text = "uid=" + uid + "&gift_id=" + giftCode + "&ruid=" + ruid + "&send_ruid=0&gift_num=1&coin_type=gold&bag_id=0&platform=pc&biz_code=Live&biz_id=" + roomNo + "&storm_beat_id=0&metadata=&price=" + giftPrice + "&receive_users=&live_statistics=%7B%22pc_client%22:%22pcWeb%22,%22jumpfrom%22:%2271001%22,%22room_category%22:%220%22,%22source_event%22:0,%22official_channel%22:%7B%22program_room_id%22:%22-99998%22,%22program_up_id%22:%22-99998%22%7D%7D&statistics=%7B%22platform%22:5,%22pc_client%22:%22pcWeb%22,%22appId%22:100%7D&web_location=444.8&csrf=" + csrf + "&" + BusBiliUtil.GetWRid(imgKey, subKey);
						JObject jToken = await httpClientFactory.Post("https://api.live.bilibili.com/xlive/revenue/v1/gift/sendGold?" + text, "");
						if (Util.GetJObject(jToken, "code") == "0")
						{
							Util.WriteLog(jobGroup[1], name, jobName, "直播间【" + roomNo + "】发送一个" + giftName);
						}
						else
						{
							Util.WriteLog(jobGroup[1], name, jobName, Util.GetJObject(jToken, "message"), ConsoleColor.Red);
						}
						await QzUtil.Delay(1000, 3000);
					}
				}
				else
				{
					Util.WriteLog(jobGroup[1], name, jobName, "Cookie失效,发礼物停止！（" + Util.GetJObject(jObject, "message") + "）", ConsoleColor.Yellow);
				}
				Util.WriteLog(jobGroup[1], name, jobName, "直播间【" + roomNo + "】发送礼物完毕");
			}
			catch (Exception ex)
			{
				Util.WriteLog(jobGroup[1], name, jobName, ex.Message, ConsoleColor.Red);
			}
		}

		public static async Task Watch(string[] jobGroup, string jobName, string name, string csrf, string roomNo, string ruid, string parent_value, string areaId, int watchMinutes, string liveBuvid, string ua, HttpClientFactory httpClientFactory, int seconds, IJobExecutionContext context)
		{
			_ = 2;
			try
			{
				long lastBeatTime = 0L;
				int heartBeatCount = 0;
				string secretKey = "";
				List<int> secretRule = new List<int>();
				string timestamp2 = "";
				int heartbeatInterval = 60;
				DateTime curTime = DateTime.Now;
				while (QzUtil.IsExecute(seconds, context) && (DateTime.Now - curTime).Minutes < watchMinutes)
				{
					string uuid = Guid.NewGuid().ToString();
					long num = new DateTimeOffset(DateTime.UtcNow).ToUnixTimeMilliseconds();
					if (num - lastBeatTime <= (heartbeatInterval + 5) * 1000)
					{
						int sleepTime = (int)((heartbeatInterval + 5) * 1000 - (num - lastBeatTime));
						for (int p = 0; p < sleepTime; p += 1000)
						{
							if (!QzUtil.IsExecute(seconds, context))
							{
								break;
							}
							await Task.Delay(1000);
						}
					}
					if (!QzUtil.IsExecute(seconds, context))
					{
						continue;
					}
					long ts = new DateTimeOffset(DateTime.UtcNow).ToUnixTimeMilliseconds();
					new JObject();
					JObject jObject;
					if (heartBeatCount == 0)
					{
						string text = JsonConvert.SerializeObject(new int[4]
						{
							int.Parse(parent_value),
							int.Parse(areaId),
							heartBeatCount,
							int.Parse(roomNo)
						});
						string text2 = "id=" + text + "&device=[\"" + liveBuvid + "\",\"" + uuid + "\"]&ruid=" + ruid;
						text2 = text2 + "&ts=" + ts + "&is_patch=0&heart_beat=[]&ua=" + ua + "&csrf_token=" + csrf + "&csrf=" + csrf + "&visit_id=";
						jObject = await httpClientFactory.Post("https://live-trace.bilibili.com/xlive/data-interface/v1/x25Kn/E", text2);
					}
					else
					{
						string text3 = JsonConvert.SerializeObject(new
						{
							platform = "web",
							parent_id = long.Parse(parent_value),
							area_id = long.Parse(areaId),
							seq_id = heartBeatCount,
							room_id = long.Parse(roomNo),
							buvid = liveBuvid,
							uuid = uuid,
							ets = long.Parse(timestamp2),
							time = heartbeatInterval,
							ts = ts
						});
						string text4 = JsonConvert.SerializeObject(new int[4]
						{
							int.Parse(parent_value),
							int.Parse(areaId),
							heartBeatCount,
							int.Parse(roomNo)
						});
						string text5 = "s=" + BusBiliUtil.Sypder(text3, secretRule, secretKey) + "&id=" + text4 + "&device=[\"" + liveBuvid + "\",\"" + uuid + "\"]&ruid=" + ruid + "&ets=" + timestamp2 + "&benchmark=" + secretKey + "&time=60&ts=" + ts;
						text5 = text5 + "&ua=" + ua + "&csrf_token=" + csrf + "&csrf=" + csrf + "&visit_id=";
						jObject = await httpClientFactory.Post("https://live-trace.bilibili.com/xlive/data-interface/v1/x25Kn/X", text5);
					}
					lastBeatTime = new DateTimeOffset(DateTime.UtcNow).ToUnixTimeMilliseconds();
					if (Util.GetJObject(jObject, "code") != "0")
					{
						timestamp2 = new DateTimeOffset(DateTime.UtcNow).ToUnixTimeSeconds().ToString();
						heartBeatCount = 0;
						secretKey = "";
						secretRule = new List<int>();
						Util.WriteLog(jobGroup[1], name, jobName, "已重新进入直播间【" + roomNo + "】");
						continue;
					}
					heartBeatCount++;
					secretKey = Util.GetJObject(jObject["data"], "secret_key");
					secretRule = JArray.Parse(Util.GetJObject(jObject["data"], "secret_rule")).ToObject<List<int>>();
					heartbeatInterval = int.Parse(Util.GetJObject(jObject["data"], "heartbeat_interval"));
					timestamp2 = Util.GetJObject(jObject["data"], "timestamp");
					if (heartBeatCount == 1)
					{
						Util.WriteLog(jobGroup[1], name, jobName, "进入直播间【" + roomNo + "】");
					}
				}
				Util.WriteLog(jobGroup[1], name, jobName, "直播间【" + roomNo + "】观看直播完毕");
			}
			catch (Exception ex)
			{
				Util.WriteLog(jobGroup[1], name, jobName, ex.Message, ConsoleColor.Red);
			}
		}
	}

	public class 辅助观看 : IJob
	{
		public async Task Execute(IJobExecutionContext context)
		{
			List<Task> taskList = new List<Task>();
			try
			{
				JObject jToken = (context.JobDetail.JobDataMap.Get("params") as JObject) ?? new JObject();
				int userId = context.JobDetail.JobDataMap.GetInt("userId");
				string areaId = Util.GetJObject(jToken, "areaId");
				string roomNo = Util.GetJObject(jToken, "roomNo");
				int watchMinutes = Util.GetJObject<int>(jToken, "watchMinutes");
				DataTable dtCookies = BiliCookies.GetCookiesList("", "", userId);
				DataTable dtArea = BiliArea.GetAreaList(areaId, "");
				await QzUtil.Delay();
				for (int i = 0; i < dtCookies.Rows.Count; i++)
				{
					DataRow drCookie = dtCookies.Rows[i];
					if (watchMinutes > 0)
					{
						taskList.Add(Task.Run(async delegate
						{
							await Execute2(areaId, watchMinutes, roomNo, dtArea, drCookie);
						}));
					}
				}
				foreach (Task item in taskList)
				{
					item.Wait();
				}
			}
			catch
			{
			}
		}

		private static async Task Execute2(string areaId, int watchMinutes, string roomNo, DataTable dtArea, DataRow drCookie)
		{
			List<Task> taskList = new List<Task>();
			string cookie = drCookie["FCookie"].ToString() ?? "";
			if (drCookie["FName"].ToString() == null)
			{
			}
			string name = drCookie["FKey"].ToString() ?? "";
			string header = drCookie["FHeaders"].ToString() ?? "";
			HttpClientHandler defaultHandler = null;
			Dictionary<string, string> defaultHeaders = HttpClientFactory.FormataHeader(header, cookie);
			HttpClientFactory httpClientFactory = new HttpClientFactory(name, defaultHeaders, defaultHandler);
			string liveBuvid = Util.GetCookieByKey(cookie, "LIVE_BUVID");
			if (liveBuvid == "")
			{
				string jObject = Util.GetJObject(Util.GetJObject<JToken>(await httpClientFactory.Get("https://api.live.bilibili.com/news/v1/notice/recom?product=live"), "responseHeaders"), "Set-Cookie");
				liveBuvid = Util.GetCookieByKey(jObject, "LIVE_BUVID");
				drCookie["FCookie"] = cookie.Trim() + "; LIVE_BUVID=" + liveBuvid;
			}
			string[] array = roomNo.Split(",");
			foreach (string ruRoomId in array)
			{
				taskList.Add(Task.Run(async delegate
				{
					await Execute3(areaId, ruRoomId, watchMinutes, liveBuvid, httpClientFactory, dtArea, drCookie);
				}));
			}
			foreach (Task item in taskList)
			{
				item.Wait();
			}
		}

		public static async Task Execute3(string areaId, string ruRoomId, int watchMinutes, string liveBuvid, HttpClientFactory httpClientFactory, DataTable dtArea, DataRow drCookie)
		{
			string json = drCookie["FHeaders"].ToString() ?? "";
			string cookie = drCookie["FCookie"].ToString() ?? "";
			string csrf = drCookie["FCsrf"].ToString() ?? "";
			try
			{
				JObject jToken = JObject.Parse(json);
				string ua = Util.GetJObject(jToken, "User-Agent");
				if (!(ua != ""))
				{
					throw new Exception("User-Agent不能为空！");
				}
				ua = ua;
				if (dtArea.Rows.Count != 1)
				{
					throw new Exception("分区信息错误！");
				}
				string parent_value = dtArea.Rows[0]["FParentId"].ToString() ?? "";
				long lastBeatTime = 0L;
				int heartBeatCount = 0;
				string secretKey = "";
				List<int> secretRule = new List<int>();
				string timestamp2 = "";
				int heartbeatInterval = 60;
				Dictionary<string, string> dicDynamic = new Dictionary<string, string> { { "Cookie", cookie } };
				JObject jObject = await httpClientFactory.Get("https://api.live.bilibili.com/xlive/web-room/v2/index/getRoomPlayInfo?room_id=" + ruRoomId + "&protocol=0,1&format=0,1,2&codec=0,1,2&qn=0&platform=web&ptype=8&dolby=5&panorama=1", dicDynamic);
				if (Util.GetJObject(jObject, "code") == "0")
				{
					string ruid = Util.GetJObject(jObject["data"], "uid");
					dicDynamic.TryAdd("Cookie", cookie);
					DateTime curTime = DateTime.Now;
					while ((DateTime.Now - curTime).Minutes < watchMinutes)
					{
						string uuid = Guid.NewGuid().ToString();
						long num = new DateTimeOffset(DateTime.UtcNow).ToUnixTimeMilliseconds();
						if (num - lastBeatTime <= heartbeatInterval * 1000)
						{
							int sleepTime = (int)((heartbeatInterval + 5) * 1000 - (num - lastBeatTime));
							for (int p = 0; p < sleepTime; p += 1000)
							{
								await Task.Delay(1000);
							}
						}
						long ts = new DateTimeOffset(DateTime.UtcNow).ToUnixTimeMilliseconds();
						new JObject();
						JObject jObject2;
						if (heartBeatCount == 0)
						{
							string text = JsonConvert.SerializeObject(new int[4]
							{
								int.Parse(parent_value),
								int.Parse(areaId),
								heartBeatCount,
								int.Parse(ruRoomId)
							});
							string text2 = "id=" + text + "&device=[\"" + liveBuvid + "\",\"" + uuid + "\"]&ruid=" + ruid;
							text2 = text2 + "&ts=" + ts + "&is_patch=0&heart_beat=[]&ua=" + ua + "&csrf_token=" + csrf + "&csrf=" + csrf + "&visit_id=";
							jObject2 = await httpClientFactory.Post("https://live-trace.bilibili.com/xlive/data-interface/v1/x25Kn/E", text2, dicDynamic);
						}
						else
						{
							string text3 = JsonConvert.SerializeObject(new
							{
								platform = "web",
								parent_id = long.Parse(parent_value),
								area_id = long.Parse(areaId),
								seq_id = heartBeatCount,
								room_id = long.Parse(ruRoomId),
								buvid = liveBuvid,
								uuid = uuid,
								ets = long.Parse(timestamp2),
								time = heartbeatInterval,
								ts = ts
							});
							string text4 = JsonConvert.SerializeObject(new int[4]
							{
								int.Parse(parent_value),
								int.Parse(areaId),
								heartBeatCount,
								int.Parse(ruRoomId)
							});
							string text5 = "s=" + BusBiliUtil.Sypder(text3, secretRule, secretKey) + "&id=" + text4 + "&device=[\"" + liveBuvid + "\",\"" + uuid + "\"]&ruid=" + ruid + "&ets=" + timestamp2 + "&benchmark=" + secretKey + "&time=60&ts=" + ts;
							text5 = text5 + "&ua=" + ua + "&csrf_token=" + csrf + "&csrf=" + csrf + "&visit_id=";
							jObject2 = await httpClientFactory.Post("https://live-trace.bilibili.com/xlive/data-interface/v1/x25Kn/X", text5, dicDynamic);
						}
						lastBeatTime = new DateTimeOffset(DateTime.UtcNow).ToUnixTimeMilliseconds();
						if (Util.GetJObject(jObject2, "code") != "0")
						{
							timestamp2 = new DateTimeOffset(DateTime.UtcNow).ToUnixTimeSeconds().ToString();
							heartBeatCount = 0;
							secretKey = "";
							secretRule = new List<int>();
							Util.WriteLog("BiliBili", "辅助观看", ruRoomId, "重新观看");
							continue;
						}
						heartBeatCount++;
						secretKey = Util.GetJObject(jObject2["data"], "secret_key");
						secretRule = JArray.Parse(Util.GetJObject(jObject2["data"], "secret_rule")).ToObject<List<int>>();
						heartbeatInterval = int.Parse(Util.GetJObject(jObject2["data"], "heartbeat_interval"));
						timestamp2 = Util.GetJObject(jObject2["data"], "timestamp");
						if (heartBeatCount == 1)
						{
							Util.WriteLog("BiliBili", "辅助观看", ruRoomId, "观看开始");
						}
					}
					Util.WriteLog("BiliBili", "辅助观看", ruRoomId, "观看结束");
					return;
				}
				throw new Exception("获取直播间ruid失败！");
			}
			catch
			{
			}
		}
	}

	public class 辅助观看Plus : IJob
	{
		public async Task Execute(IJobExecutionContext context)
		{
			string[] jobGroup = (context.JobDetail.JobDataMap.GetString("jobGroup") ?? "").Split("-");
			string jobName = context.JobDetail.JobDataMap.GetString("jobName") ?? "";
			try
			{
				JObject @params = (context.JobDetail.JobDataMap.Get("params") as JObject) ?? new JObject();
				int seconds = context.JobDetail.JobDataMap.GetInt("seconds");
				int userId = context.JobDetail.JobDataMap.GetInt("userId");
				string areaId = Util.GetJObject(@params, "areaId");
				string cookieId = Util.GetJObject(@params, "cookieId");
				Util.GetJObject(@params, "type");
				string jObject = Util.GetJObject(@params, "param");
				int.TryParse(jObject, out var times);
				times = ((times != 0) ? times : 20);
				string keys = await QzUtil.Validate(userId, BusSysUser.Instance.User.Address, "bilibili", areaId);
				int jObject2 = Util.GetJObject<int>(@params, "difference");
				if (jObject2 != 0)
				{
					int num = new Random().Next(0, jObject2);
					seconds = ((seconds != 0) ? (seconds + num) : 0);
					DateTime datetime = DateTime.Now.AddSeconds(num);
					Util.WriteLog(jobGroup[1], jobGroup[0], jobName, datetime.ToString("yyyy-MM-dd HH:mm:ss") + "开始执行");
					while (DateTime.Now <= datetime && QzUtil.IsExecute(seconds, context))
					{
						await QzUtil.Delay();
					}
				}
				DataTable cookiesList = BiliCookies.GetCookiesList(cookieId, keys, userId, "1", areaId);
				string text = "";
				for (int i = 0; i < cookiesList.Rows.Count; i++)
				{
					DataRow dr = cookiesList.Rows[i];
					text = text + ((text == "") ? "" : ",") + Util.GetJObject(dr, "FRoomId");
				}
				if (times > 20)
				{
					throw new ArgumentException("观看时间不能超过 20 分钟！");
				}
				JObject jObjectParam = new JObject
				{
					["userId"] = BusSysUser.Instance.User.Id,
					["watchMinutes"] = times,
					["roomId"] = text,
					["areaId"] = areaId
				};
				Request model = new Request
				{
					jObjectParam = jObjectParam
				};
				object obj = await Util.Request("/Common/Watch", model);
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, obj?.ToString() ?? "");
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, QzUtil.WriteStatus(context, seconds));
			}
			catch (Exception ex)
			{
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, ex.Message, ConsoleColor.Red);
			}
		}
	}

	public class 辅助弹幕 : IJob
	{
		public async Task Execute(IJobExecutionContext context)
		{
			List<Task> list = new List<Task>();
			JObject jToken = (context.JobDetail.JobDataMap.Get("params") as JObject) ?? new JObject();
			int userId = context.JobDetail.JobDataMap.GetInt("userId");
			string roomNo = Util.GetJObject(jToken, "roomNo");
			string msg = Util.GetJObject(jToken, "msg");
			DataTable cookiesList = BiliCookies.GetCookiesList("", "", userId);
			for (int i = 0; i < cookiesList.Rows.Count; i++)
			{
				DataRow drCookie = cookiesList.Rows[i];
				list.Add(Task.Run(async delegate
				{
					await Execute2(drCookie, roomNo, msg);
				}));
			}
			foreach (Task item in list)
			{
				item.Wait();
			}
			await QzUtil.Delay(60000, 60000);
		}

		public static async Task Execute2(DataRow drCookie, string roomNoList, string msg)
		{
			string name = drCookie["FKey"].ToString() ?? "";
			string cookie = drCookie["FCookie"].ToString() ?? "";
			string text = drCookie["FHeaders"].ToString() ?? "";
			string csrf = drCookie["FCsrf"].ToString() ?? "";
			try
			{
				JObject.Parse(text);
				HttpClientHandler defaultHandler = null;
				Dictionary<string, string> defaultHeaders = HttpClientFactory.FormataHeader(text, cookie);
				HttpClientFactory httpClientFactory = new HttpClientFactory(name, defaultHeaders, defaultHandler);
				string[] roomNo = roomNoList.Split(",");
				for (int i = 0; i < roomNo.Length; i++)
				{
					if (msg != "")
					{
						Dictionary<string, string> dictionary = new Dictionary<string, string>();
						Random random = new Random();
						StringBuilder stringBuilder = new StringBuilder();
						for (int j = 0; j < 30; j++)
						{
							stringBuilder.Append("0123456789"[random.Next("0123456789".Length)]);
						}
						dictionary.Add("Content-Type", "multipart/form-data; boundary=---------------------------" + stringBuilder);
						string text2 = "bubble=0&color=16777215&mode=1&room_type=0&jumpfrom=0&reply_mid=0&reply_attr=0&replay_dmid=&fontsize=25&statistics={\"appId\":100,\"platform\":5}&rnd=" + new DateTimeOffset(DateTime.UtcNow).ToUnixTimeSeconds() + "&roomid=" + roomNo[i] + "&csrf=" + csrf + "&csrf_token=" + csrf;
						text2 = text2 + "&msg=" + msg;
						JObject jToken = await httpClientFactory.Post("https://api.live.bilibili.com/msg/send", text2, dictionary);
						if (Util.GetJObject(jToken, "code") == "0")
						{
							Util.WriteLog("BiliBili", "辅助弹幕", roomNo[i], msg);
						}
						else
						{
							Util.WriteLog("BiliBili", "辅助弹幕", roomNo[i], Util.GetJObject(jToken, "message"), ConsoleColor.Red);
						}
						await QzUtil.Delay(10000, 15000);
					}
				}
			}
			catch
			{
			}
		}
	}

	public class 领取里程投稿 : IJob
	{
		public async Task Execute(IJobExecutionContext context)
		{
			string[] jobGroup = (context.JobDetail.JobDataMap.GetString("jobGroup") ?? "").Split("-");
			string jobName = context.JobDetail.JobDataMap.GetString("jobName") ?? "";
			ConcurrentBag<JToken> bagUser = new ConcurrentBag<JToken>();
			ConcurrentBag<JToken> bagCdkey = new ConcurrentBag<JToken>();
			List<Task> taskList = new List<Task>();
			ConcurrentDictionary<string, int> conDic = new ConcurrentDictionary<string, int>();
			try
			{
				JObject @params = (context.JobDetail.JobDataMap.Get("params") as JObject) ?? new JObject();
				int seconds = context.JobDetail.JobDataMap.GetInt("seconds");
				int userId = context.JobDetail.JobDataMap.GetInt("userId");
				string areaId = Util.GetJObject(@params, "areaId");
				string cookieId = Util.GetJObject(@params, "cookieId");
				string type = Util.GetJObject(@params, "type");
				string jObject = Util.GetJObject(@params, "param");
				int.TryParse(jObject, out var times);
				times = ((times != 0) ? times : 701);
				string keys = await QzUtil.Validate(userId, BusSysUser.Instance.User.Address, "bilibili", areaId);
				int jObject2 = Util.GetJObject<int>(@params, "difference");
				if (jObject2 != 0)
				{
					int num = new Random().Next(0, jObject2);
					seconds = ((seconds != 0) ? (seconds + num) : 0);
					DateTime datetime = DateTime.Now.AddSeconds(num);
					Util.WriteLog(jobGroup[1], jobGroup[0], jobName, datetime.ToString("yyyy-MM-dd HH:mm:ss") + "开始执行");
					while (DateTime.Now <= datetime && QzUtil.IsExecute(seconds, context))
					{
						await QzUtil.Delay();
					}
				}
				string text = Util.ProcessExist("\\Geetest\\app.exe");
				if (text != "")
				{
					Util.WriteLog(jobGroup[1], jobGroup[0], jobName, text + "本地打码！", ConsoleColor.Yellow);
				}
				DataTable cookiesList = BiliCookies.GetCookiesList(cookieId, keys, userId, "1", areaId);
				DataTable receiveTaskList = BiliCookiesTask.GetReceiveTaskList(areaId, cookieId, TaskType.Normal, userId);
				CancellationTokenSource cts = new CancellationTokenSource();
				if (type == "接口")
				{
					var enumerable = from row in receiveTaskList.AsEnumerable()
						group row by row.Field<string>("FTaskId") into g
						select new
						{
							Category = g.Key
						};
					foreach (var group in enumerable)
					{
						conDic[group.Category + "Refresh"] = 0;
						conDic[group.Category] = 0;
						Task.Run(async delegate
						{
							await GetPublicStock("", "", "方式一", group.Category, conDic, seconds, context, cts.Token);
						}, cts.Token);
					}
				}
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, "开始");
				for (int num2 = 0; num2 < cookiesList.Rows.Count; num2++)
				{
					if (!QzUtil.IsExecute(seconds, context))
					{
						break;
					}
					DataRow drCookie = cookiesList.Rows[num2];
					string text2 = drCookie["Fid"].ToString() ?? "";
					DataRow[] drCookiesTasks = receiveTaskList.Select("FCookieId=" + text2);
					if (drCookiesTasks == null || drCookiesTasks.Length == 0)
					{
						continue;
					}
					if (type == "浏览器")
					{
						taskList.Add(Task.Run(async delegate
						{
							await Receive(jobGroup, jobName, areaId, drCookie, drCookiesTasks.CopyToDataTable(), times, userId, bagUser, seconds, context);
						}));
					}
					else if (type == "接口")
					{
						taskList.Add(Task.Run(async delegate
						{
							await Receive2(jobGroup, jobName, drCookie, drCookiesTasks.CopyToDataTable(), times, conDic, bagUser, bagCdkey, seconds, context);
						}));
					}
				}
				foreach (Task item in taskList)
				{
					item.Wait();
				}
				cts.Cancel();
				if (!bagCdkey.IsEmpty)
				{
					DataTable dt = JArray.FromObject(bagCdkey).ToObject<DataTable>();
					BiliCdkey.UpdateCdkeyList(dt, cookieId, "", userId, updateTask: true);
				}
				BusBiliCookies.UpdateCookieStatus(bagUser, userId);
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, QzUtil.WriteStatus(context, seconds));
			}
			catch (Exception ex)
			{
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, ex.Message, ConsoleColor.Red);
			}
		}
	}

	public class 领取里程投稿Plus : IJob
	{
		public async Task Execute(IJobExecutionContext context)
		{
			string[] jobGroup = (context.JobDetail.JobDataMap.GetString("jobGroup") ?? "").Split("-");
			string jobName = context.JobDetail.JobDataMap.GetString("jobName") ?? "";
			ConcurrentBag<JToken> bagUser = new ConcurrentBag<JToken>();
			ConcurrentBag<JToken> bagCdkey = new ConcurrentBag<JToken>();
			List<Task> taskList = new List<Task>();
			ConcurrentDictionary<string, int> conDic = new ConcurrentDictionary<string, int>();
			try
			{
				JObject @params = (context.JobDetail.JobDataMap.Get("params") as JObject) ?? new JObject();
				int seconds = context.JobDetail.JobDataMap.GetInt("seconds");
				int userId = context.JobDetail.JobDataMap.GetInt("userId");
				string cookieId = Util.GetJObject(@params, "cookieId");
				string type = Util.GetJObject(@params, "type");
				string jObject = Util.GetJObject(@params, "param");
				int.TryParse(jObject, out var times);
				times = ((times != 0) ? times : 751);
				string keys = await QzUtil.Validate(userId, BusSysUser.Instance.User.Address, "bilibili");
				int jObject2 = Util.GetJObject<int>(@params, "difference");
				if (jObject2 != 0)
				{
					int num = new Random().Next(0, jObject2);
					seconds = ((seconds != 0) ? (seconds + num) : 0);
					DateTime datetime = DateTime.Now.AddSeconds(num);
					Util.WriteLog(jobGroup[1], jobGroup[0], jobName, datetime.ToString("yyyy-MM-dd HH:mm:ss") + "开始执行");
					while (DateTime.Now <= datetime && QzUtil.IsExecute(seconds, context))
					{
						await QzUtil.Delay();
					}
				}
				string text = Util.ProcessExist("\\Geetest\\app.exe");
				if (text != "")
				{
					Util.WriteLog(jobGroup[1], jobGroup[0], jobName, text + "本地打码！", ConsoleColor.Yellow);
				}
				DataTable cookiesList = BiliCookies.GetCookiesList(cookieId, keys, userId);
				DataTable receiveTaskList = BiliCookiesTask.GetReceiveTaskList(BusSysUser.Instance.User.Organization.BiliBili, cookieId, TaskType.Normal, userId);
				CancellationTokenSource cts = new CancellationTokenSource();
				if (type == "接口")
				{
					var enumerable = from row in receiveTaskList.AsEnumerable()
						group row by row.Field<string>("FTaskId") into g
						select new
						{
							Category = g.Key
						};
					foreach (var group in enumerable)
					{
						conDic[group.Category + "Refresh"] = 0;
						conDic[group.Category] = 0;
						Task.Run(async delegate
						{
							await GetPublicStock("", "", "方式一", group.Category, conDic, seconds, context, cts.Token);
						}, cts.Token);
					}
				}
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, "开始");
				for (int num2 = 0; num2 < cookiesList.Rows.Count; num2++)
				{
					if (!QzUtil.IsExecute(seconds, context))
					{
						break;
					}
					DataRow drCookie = cookiesList.Rows[num2];
					string text2 = drCookie["Fid"].ToString() ?? "";
					DataRow[] drCookiesTasks = receiveTaskList.Select("FCookieId=" + text2);
					if (drCookiesTasks == null || drCookiesTasks.Length == 0)
					{
						continue;
					}
					if (type == "浏览器")
					{
						taskList.Add(Task.Run(async delegate
						{
							await Receive(jobGroup, jobName, "0", drCookie, drCookiesTasks.CopyToDataTable(), times, userId, bagUser, seconds, context);
						}));
					}
					else if (type == "接口")
					{
						taskList.Add(Task.Run(async delegate
						{
							await Receive2(jobGroup, jobName, drCookie, drCookiesTasks.CopyToDataTable(), times, conDic, bagUser, bagCdkey, seconds, context);
						}));
					}
				}
				foreach (Task item in taskList)
				{
					item.Wait();
				}
				cts.Cancel();
				if (!bagCdkey.IsEmpty)
				{
					DataTable dt = JArray.FromObject(bagCdkey).ToObject<DataTable>();
					BiliCdkey.UpdateCdkeyList(dt, cookieId, "", userId, updateTask: true);
				}
				BusBiliCookies.UpdateCookieStatus(bagUser, userId);
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, QzUtil.WriteStatus(context, seconds));
			}
			catch (Exception ex)
			{
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, ex.Message, ConsoleColor.Red);
			}
		}
	}

	public class 领取每日任务 : IJob
	{
		public async Task Execute(IJobExecutionContext context)
		{
			string[] jobGroup = (context.JobDetail.JobDataMap.GetString("jobGroup") ?? "").Split("-");
			string jobName = context.JobDetail.JobDataMap.GetString("jobName") ?? "";
			ConcurrentBag<JToken> bagUser = new ConcurrentBag<JToken>();
			ConcurrentBag<JToken> bagCdkey = new ConcurrentBag<JToken>();
			List<Task> taskList = new List<Task>();
			try
			{
				JObject @params = (context.JobDetail.JobDataMap.Get("params") as JObject) ?? new JObject();
				int seconds = context.JobDetail.JobDataMap.GetInt("seconds");
				int userId = context.JobDetail.JobDataMap.GetInt("userId");
				string areaId = Util.GetJObject(@params, "areaId");
				string cookieId = Util.GetJObject(@params, "cookieId");
				string type = Util.GetJObject(@params, "type");
				string keys = await QzUtil.Validate(userId, BusSysUser.Instance.User.Address, "bilibili", areaId);
				int jObject = Util.GetJObject<int>(@params, "difference");
				if (jObject != 0)
				{
					int num = new Random().Next(0, jObject);
					seconds = ((seconds != 0) ? (seconds + num) : 0);
					DateTime datetime = DateTime.Now.AddSeconds(num);
					Util.WriteLog(jobGroup[1], jobGroup[0], jobName, datetime.ToString("yyyy-MM-dd HH:mm:ss") + "开始执行");
					while (DateTime.Now <= datetime && QzUtil.IsExecute(seconds, context))
					{
						await QzUtil.Delay();
					}
				}
				string text = Util.ProcessExist("\\Geetest\\app.exe");
				if (text != "")
				{
					Util.WriteLog(jobGroup[1], jobGroup[0], jobName, text + "本地打码！", ConsoleColor.Yellow);
				}
				DataTable cookiesList = BiliCookies.GetCookiesList(cookieId, keys, userId, "1", areaId);
				DataTable receiveTaskList = BiliCookiesTask.GetReceiveTaskList(areaId, cookieId, TaskType.Daily, userId);
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, "开始");
				for (int i = 0; i < cookiesList.Rows.Count; i++)
				{
					if (!QzUtil.IsExecute(seconds, context))
					{
						break;
					}
					DataRow drCookie = cookiesList.Rows[i];
					string text2 = drCookie["Fid"].ToString() ?? "";
					DataRow[] drCookiesTasks = receiveTaskList.Select("FCookieId=" + text2);
					if (drCookiesTasks == null || drCookiesTasks.Length == 0)
					{
						continue;
					}
					if (type == "浏览器")
					{
						taskList.Add(Task.Run(async delegate
						{
							await Receive(jobGroup, jobName, areaId, drCookie, drCookiesTasks.CopyToDataTable(), 2000, userId, bagUser, seconds, context);
						}));
					}
					else if (type == "接口")
					{
						taskList.Add(Task.Run(async delegate
						{
							await Receive4(jobGroup, jobName, drCookie, drCookiesTasks.CopyToDataTable(), bagUser, bagCdkey, seconds, context);
						}));
					}
				}
				foreach (Task item in taskList)
				{
					item.Wait();
				}
				if (!bagCdkey.IsEmpty)
				{
					DataTable dt = JArray.FromObject(bagCdkey).ToObject<DataTable>();
					BiliCdkey.UpdateCdkeyList(dt, cookieId, "", userId, updateTask: true);
				}
				BusBiliCookies.UpdateCookieStatus(bagUser, userId);
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, QzUtil.WriteStatus(context, seconds));
			}
			catch (Exception ex)
			{
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, ex.Message, ConsoleColor.Red);
			}
		}
	}

	public class 领取强制任务 : IJob
	{
		public async Task Execute(IJobExecutionContext context)
		{
			string[] jobGroup = (context.JobDetail.JobDataMap.GetString("jobGroup") ?? "").Split("-");
			string jobName = context.JobDetail.JobDataMap.GetString("jobName") ?? "";
			ConcurrentBag<JToken> bagUser = new ConcurrentBag<JToken>();
			ConcurrentBag<JToken> bagCdkey = new ConcurrentBag<JToken>();
			List<Task> taskList = new List<Task>();
			try
			{
				JObject @params = (context.JobDetail.JobDataMap.Get("params") as JObject) ?? new JObject();
				int seconds = context.JobDetail.JobDataMap.GetInt("seconds");
				int userId = context.JobDetail.JobDataMap.GetInt("userId");
				string cookieId = Util.GetJObject(@params, "cookieId");
				string type = Util.GetJObject(@params, "type");
				string jObject = Util.GetJObject(@params, "param");
				int.TryParse(jObject, out var times);
				times = ((times != 0) ? times : 751);
				string keys = await QzUtil.Validate(userId, BusSysUser.Instance.User.Address, "bilibili");
				int jObject2 = Util.GetJObject<int>(@params, "difference");
				if (jObject2 != 0)
				{
					int num = new Random().Next(0, jObject2);
					seconds = ((seconds != 0) ? (seconds + num) : 0);
					DateTime datetime = DateTime.Now.AddSeconds(num);
					Util.WriteLog(jobGroup[1], jobGroup[0], jobName, datetime.ToString("yyyy-MM-dd HH:mm:ss") + "开始执行");
					while (DateTime.Now <= datetime && QzUtil.IsExecute(seconds, context))
					{
						await QzUtil.Delay();
					}
				}
				string text = Util.ProcessExist("\\Geetest\\app.exe");
				if (text != "")
				{
					Util.WriteLog(jobGroup[1], jobGroup[0], jobName, text + "本地打码！", ConsoleColor.Yellow);
				}
				DataTable cookiesList = BiliCookies.GetCookiesList(cookieId, keys, userId);
				DataTable receiveTaskList = BiliCookiesTask.GetReceiveTaskList(BusSysUser.Instance.User.Organization.BiliBili, cookieId, TaskType.Compulsory, userId);
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, "开始");
				for (int i = 0; i < cookiesList.Rows.Count; i++)
				{
					if (!QzUtil.IsExecute(seconds, context))
					{
						break;
					}
					DataRow drCookie = cookiesList.Rows[i];
					string text2 = drCookie["Fid"].ToString() ?? "";
					DataRow[] drCookiesTasks = receiveTaskList.Select("FCookieId=" + text2);
					if (drCookiesTasks == null || drCookiesTasks.Length == 0)
					{
						continue;
					}
					if (type == "浏览器")
					{
						taskList.Add(Task.Run(async delegate
						{
							await Receive(jobGroup, jobName, "0", drCookie, drCookiesTasks.CopyToDataTable(), times, userId, bagUser, seconds, context);
						}));
					}
					else if (type == "接口")
					{
						taskList.Add(Task.Run(async delegate
						{
							await Receive3(jobGroup, jobName, drCookie, drCookiesTasks.CopyToDataTable(), times, bagUser, bagCdkey, seconds, context);
						}));
					}
				}
				foreach (Task item in taskList)
				{
					item.Wait();
				}
				if (!bagCdkey.IsEmpty)
				{
					DataTable dt = JArray.FromObject(bagCdkey).ToObject<DataTable>();
					BiliCdkey.UpdateCdkeyList(dt, cookieId, "", userId, updateTask: true);
				}
				BusBiliCookies.UpdateCookieStatus(bagUser, userId);
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, QzUtil.WriteStatus(context, seconds));
			}
			catch (Exception ex)
			{
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, ex.Message, ConsoleColor.Red);
			}
		}
	}

	public class 执行代码 : IJob
	{
		public async Task Execute(IJobExecutionContext context)
		{
			string[] jobGroup = (context.JobDetail.JobDataMap.GetString("jobGroup") ?? "").Split("-");
			string jobName = context.JobDetail.JobDataMap.GetString("jobName") ?? "";
			try
			{
				JObject jToken = (context.JobDetail.JobDataMap.Get("params") as JObject) ?? new JObject();
				int seconds = context.JobDetail.JobDataMap.GetInt("seconds");
				int userId = context.JobDetail.JobDataMap.GetInt("userId");
				string areaId = Util.GetJObject(jToken, "areaId");
				string cookieId = Util.GetJObject(jToken, "cookieId");
				string param = Util.GetJObject(jToken, "param");
				DataTable cookiesList = BiliCookies.GetCookiesList(cookieId, "", userId);
				cookieId = string.Join(',', (from row in cookiesList.Rows.OfType<DataRow>()
					select Convert.ToString(row["Fid"])).ToList());
				int jObject = Util.GetJObject<int>(jToken, "difference");
				if (jObject != 0)
				{
					int num = new Random().Next(0, jObject);
					seconds = ((seconds != 0) ? (seconds + num) : 0);
					DateTime datetime = DateTime.Now.AddSeconds(num);
					Util.WriteLog(jobGroup[1], jobGroup[0], jobName, datetime.ToString("yyyy-MM-dd HH:mm:ss") + "开始执行");
					while (DateTime.Now <= datetime && QzUtil.IsExecute(seconds, context))
					{
						await QzUtil.Delay();
					}
				}
				Request model = new Request
				{
					jObjectParam = new JObject
					{
						["userId"] = userId,
						["userName"] = BusSysUser.Instance.User.Name,
						["catalog"] = param
					}
				};
				object obj = await Util.Request("/Common/GetSysControlByCatalog", model);
				if (obj != null)
				{
					JObject jToken2 = JObject.FromObject(obj);
					if (Util.GetJObject<int>(jToken2, "total") == 0)
					{
						throw new Exception("暂无可执行的数据！");
					}
					JArray jArray = Util.GetJObject<JArray>(jToken2, "rows") ?? new JArray();
					foreach (JToken item in jArray)
					{
						string jObject2 = Util.GetJObject(item, "FValue");
						if (jObject2 != "")
						{
							jObject2 = jObject2.Replace("|*|*|", " AND FAreaId=" + areaId + " AND FCookieId IN (" + cookieId + ")");
							Util.WriteLog(jobGroup[1], jobGroup[0], jobName, "操作成功，有" + SQLHelper.BiliLocalDB.RunSqlText(jObject2) + " 内容受到影响！");
						}
					}
				}
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, QzUtil.WriteStatus(context, seconds));
			}
			catch (Exception ex)
			{
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, ex.Message, ConsoleColor.Red);
			}
		}
	}

	public class 稿件刷播 : IJob
	{
		public async Task Execute(IJobExecutionContext context)
		{
			string[] jobGroup = (context.JobDetail.JobDataMap.GetString("jobGroup") ?? "").Split("-");
			string jobName = context.JobDetail.JobDataMap.GetString("jobName") ?? "";
			try
			{
				JObject jToken = (context.JobDetail.JobDataMap.Get("params") as JObject) ?? new JObject();
				int seconds = context.JobDetail.JobDataMap.GetInt("seconds");
				int userId = context.JobDetail.JobDataMap.GetInt("userId");
				string param = Util.GetJObject(jToken, "param");
				int jObject = Util.GetJObject<int>(jToken, "difference");
				if (jObject != 0)
				{
					int num = jObject;
					seconds = ((seconds != 0) ? (seconds + num) : 0);
					DateTime datetime = DateTime.Now.AddSeconds(num);
					while (DateTime.Now <= datetime && QzUtil.IsExecute(seconds, context))
					{
						await QzUtil.Delay();
					}
				}
				JObject jObject2 = new JObject
				{
					["Accept"] = "application/json, text/plain, */*",
					["Accept-Encoding"] = "gzip, deflate, br, zstd",
					["Cache-Control"] = "no-cache",
					["Content-Type"] = "application/x-www-form-urlencoded",
					["Origin"] = "https://www.bilibili.com",
					["Pragma"] = "no-cache",
					["Priority"] = "u=1, i",
					["Sec-Ch-Ua"] = "\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
					["Sec-Ch-Ua-Mobile"] = "?0",
					["Sec-Ch-Ua-Platform"] = "\"Windows\"",
					["Sec-Fetch-Dest"] = "empty",
					["Sec-Fetch-Mode"] = "cors",
					["Sec-Fetch-Site"] = "same-site",
					["User-Agent"] = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
				};
				Dictionary<string, string> defaultHeaders = jObject2.ToObject<Dictionary<string, string>>() ?? new Dictionary<string, string>();
				HttpClientFactory httpClientFactory = new HttpClientFactory("", defaultHeaders);
				string sSql = "SELECT * FROM TArticlesPlay WHERE FUserId=" + userId + " AND FDate='" + param + "'";
				DataTable dataTable = SQLHelper.BiliLocalDB.RunSqlDt(sSql);
				foreach (DataRow row in dataTable.Rows)
				{
					string id = Util.GetJObject(row, "Fid");
					string bvid = Util.GetJObject(row, "FBvid");
					JObject jObject3 = new JObject
					{
						["Accept"] = "application/json, text/plain, */*",
						["Accept-Encoding"] = "gzip, deflate, br, zstd",
						["Cache-Control"] = "no-cache",
						["Content-Type"] = "application/x-www-form-urlencoded",
						["Origin"] = "https://www.bilibili.com",
						["Pragma"] = "no-cache",
						["Priority"] = "u=1, i",
						["Referer"] = "https://www.bilibili.com/video/" + bvid + "/",
						["Sec-Ch-Ua"] = "\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
						["Sec-Ch-Ua-Mobile"] = "?0",
						["Sec-Ch-Ua-Platform"] = "\"Windows\"",
						["Sec-Fetch-Dest"] = "empty",
						["Sec-Fetch-Mode"] = "cors",
						["Sec-Fetch-Site"] = "same-site",
						["User-Agent"] = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
					};
					JObject jObject4 = jObject3;
					jObject4["Cookie"] = await BusBiliUtil.CrateCookie(httpClientFactory);
					JObject jObject5 = await httpClientFactory.Get("https://api.bilibili.com/x/web-interface/view?bvid=" + bvid, jObject3.ToObject<Dictionary<string, string>>() ?? new Dictionary<string, string>());
					if (Util.GetJObject(jObject5, "code") == "0")
					{
						int jObject6 = Util.GetJObject<int>(Util.GetJObject<JToken>(jObject5["data"], "stat"), "view");
						DataCURD.Save(new JObject
						{
							["Fid"] = id,
							["FEnd"] = jObject6
						}, "TArticlesPlay", "编辑", "Fid", userId, "", DateTime.Now.ToString(), SQLHelper.BiliLocalDB.InitCnn());
					}
				}
				Util.WriteLog("BiliBili", "稿件刷播", "稿件刷播", "播放量数据已更新！", ConsoleColor.Green);
			}
			catch (Exception ex)
			{
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, ex.Message, ConsoleColor.Red);
			}
		}
	}

	[DisallowConcurrentExecution]
	public class 更新Cookie信息 : IJob
	{
		public async Task Execute(IJobExecutionContext context)
		{
			string[] jobGroup = (context.JobDetail.JobDataMap.GetString("jobGroup") ?? "").Split("-");
			string jobName = context.JobDetail.JobDataMap.GetString("jobName") ?? "";
			try
			{
				JObject jToken = (context.JobDetail.JobDataMap.Get("params") as JObject) ?? new JObject();
				int seconds = context.JobDetail.JobDataMap.GetInt("seconds");
				int userId = context.JobDetail.JobDataMap.GetInt("userId");
				Util.GetJObject(jToken, "areaId");
				string cookieId = Util.GetJObject(jToken, "cookieId");
				Util.GetJObject(jToken, "param");
				int jObject = Util.GetJObject<int>(jToken, "difference");
				if (jObject != 0)
				{
					int num = new Random().Next(0, jObject);
					seconds = ((seconds != 0) ? (seconds + num) : 0);
					DateTime datetime = DateTime.Now.AddSeconds(num);
					Util.WriteLog(jobGroup[1], jobGroup[0], jobName, datetime.ToString("yyyy-MM-dd HH:mm:ss") + "开始执行");
					while (DateTime.Now <= datetime && QzUtil.IsExecute(seconds, context))
					{
						await QzUtil.Delay();
					}
				}
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, "开始");
				DataTable dtCookies = BiliCookies.GetCookiesList(cookieId, "", userId);
				for (int i = 0; i < dtCookies.Rows.Count; i++)
				{
					if (!QzUtil.IsExecute(seconds, context))
					{
						break;
					}
					DataRow dataRow = dtCookies.Rows[i];
					string text = dataRow["Fid"].ToString() ?? "";
					if (dataRow["FName"].ToString() == null)
					{
					}
					string text2 = dataRow["FKey"].ToString() ?? "";
					string json = dataRow["FHeaders"].ToString() ?? "";
					JObject jToken2 = JObject.Parse(json);
					string jObject2 = Util.GetJObject(jToken2, "User-Agent");
					string text3 = AppSettings.GetVal("TokenPrefix") + Token.GetTokenValue(userId.ToString());
					string val = AppSettings.GetVal("TokenKey");
					string val2 = AppSettings.GetVal("Url", "Kestrel:Endpoints:Http");
					string text4 = Directory.GetCurrentDirectory() + "\\Util\\Chromium\\Bili-" + text2;
					string fileName = text4 + "\\Chromium.exe";
					ProcessStartInfo info = new ProcessStartInfo
					{
						FileName = fileName,
						Arguments = "-a " + text3 + " -c \"" + BusSysUser.Instance.User.Cookie + "\" -t Cookie -i " + text + " -k " + text2 + " -s 60 -u " + val2 + " --token " + val + " --ua \"" + jObject2 + "\" -m \"https://www.bilibili.com/\""
					};
					Process.Start(info)?.WaitForExit();
					await Task.Delay(5000);
					Process.Start(info)?.WaitForExit();
				}
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, QzUtil.WriteStatus(context, seconds));
			}
			catch (Exception ex)
			{
				Util.WriteLog(jobGroup[1], jobGroup[0], jobName, ex.Message, ConsoleColor.Red);
			}
		}
	}

	private static async Task Receive(string[] jobGroup, string jobName, string areaId, DataRow drCookie, DataTable dtCookieTask, int times, int userId, ConcurrentBag<JToken> bagUser, int seconds, IJobExecutionContext context)
	{
		string cookieId = drCookie["Fid"].ToString() ?? "";
		string key = drCookie["FKey"].ToString() ?? "";
		string cookie = drCookie["FCookie"].ToString() ?? "";
		string name = drCookie["FName"].ToString() ?? "";
		string text = drCookie["FHeaders"].ToString() ?? "";
		string text2 = drCookie["FProxyAddress"].ToString() ?? "";
		if (text2 != "")
		{
			Util.WriteLog(jobGroup[1], name, jobName, "已配置代理地址，无法使用*浏览器*操作！");
			return;
		}
		try
		{
			Util.WriteLog(jobGroup[1], name, jobName, "开始");
			JObject jObjectHeader = JObject.Parse(text);
			HttpClientHandler defaultHandler = null;
			Dictionary<string, string> defaultHeaders = HttpClientFactory.FormataHeader(text, cookie);
			HttpClientFactory httpClientFactory = new HttpClientFactory(key, defaultHeaders, defaultHandler);
			string typeName = jobGroup[1];
			string name2 = name;
			Util.WriteLog(typeName, name2, jobName, await httpClientFactory.GetIp());
			string text3 = "";
			string text4 = "";
			string text5 = "";
			for (int i = 0; i < dtCookieTask.Rows.Count; i++)
			{
				if (!QzUtil.IsExecute(seconds, context))
				{
					break;
				}
				string jObject = Util.GetJObject(dtCookieTask.Rows[i], "FTaskKey");
				string jObject2 = Util.GetJObject(dtCookieTask.Rows[i], "FReceiveUrl");
				string jObject3 = Util.GetJObject(dtCookieTask.Rows[i], "FCompulsory");
				string jObject4 = Util.GetJObject(dtCookieTask.Rows[i], "FDaily");
				text4 = ((jObject3 == "1") ? "Compulsory" : ((!(jObject4 == "1")) ? "Normal" : "Daily"));
				text3 = text3 + "," + jObject2 + jObject;
				if (text5 == "")
				{
					text5 = jObject2 + jObject;
				}
			}
			string jObject5 = Util.GetJObject(jObjectHeader, "User-Agent");
			string text6 = AppSettings.GetVal("TokenPrefix") + Token.GetTokenValue(userId.ToString());
			string val = AppSettings.GetVal("TokenKey");
			string val2 = AppSettings.GetVal("Url", "Kestrel:Endpoints:Http");
			areaId = ((areaId == "") ? "0" : areaId);
			string arguments = "--sleep " + times + " -a " + text6 + " -c \"" + BusSysUser.Instance.User.Cookie + "\" -t " + text4 + " -i " + cookieId + " -k " + key + " -s " + seconds + " -u " + val2 + " --area " + areaId + " --token " + val + " --ua \"" + jObject5 + "\" -m " + text5 + " -d \"" + text3.Trim(',') + "\"";
			Process p = null;
			if (text3 != "")
			{
				p = Util.ProcessStart("\\Chromium\\Bili-" + key + "\\Chromium.exe", arguments);
			}
			await Task.Delay(2000);
			while (p != null && !p.HasExited && QzUtil.IsExecute(seconds, context))
			{
				await Task.Delay(2000);
			}
			if (p != null && !p.HasExited)
			{
				p.CloseMainWindow();
			}
			await Task.Delay(2000);
			Util.WriteLog(jobGroup[1], name, jobName, "结束");
		}
		catch (Exception ex)
		{
			BusBiliCookies.AddCookieStatus(bagUser, cookieId, ex.Message);
			Util.WriteLog(jobGroup[1], name, jobName, ex.Message, ConsoleColor.Red);
		}
	}

	private static async Task Receive2(string[] jobGroup, string jobName, DataRow drCookie, DataTable dtCookieTask, int times, ConcurrentDictionary<string, int> conDic, ConcurrentBag<JToken> bagUser, ConcurrentBag<JToken> bagCdkey, int seconds, IJobExecutionContext context)
	{
		string cookieId = drCookie["Fid"].ToString() ?? "";
		string key = drCookie["FKey"].ToString() ?? "";
		string cookie = drCookie["FCookie"].ToString() ?? "";
		string name = drCookie["FName"].ToString() ?? "";
		string text = drCookie["FHeaders"].ToString() ?? "";
		string csrf = drCookie["FCsrf"].ToString() ?? "";
		string proxyAddress = drCookie["FProxyAddress"].ToString() ?? "";
		string proxyUserName = drCookie["FProxyUserName"].ToString() ?? "";
		string proxyPassword = drCookie["FProxyPassword"].ToString() ?? "";
		string browserStatus = drCookie["FBrowserStatus"].ToString() ?? "";
		try
		{
			Util.WriteLog(jobGroup[1], name, jobName, "开始");
			JObject jObjectHeader = JObject.Parse(text);
			HttpClientHandler defaultHandler = null;
			if (proxyAddress != "")
			{
				defaultHandler = new HttpClientHandler
				{
					Proxy = new WebProxy
					{
						Address = new Uri(proxyAddress),
						Credentials = new NetworkCredential(proxyUserName, proxyPassword)
					}
				};
			}
			Dictionary<string, string> dic = HttpClientFactory.FormataHeader(text, cookie);
			HttpClientFactory httpClientFactory = new HttpClientFactory(key, dic, defaultHandler);
			string typeName = jobGroup[1];
			string name2 = name;
			string jobName2 = jobName;
			Util.WriteLog(typeName, name2, jobName2, await httpClientFactory.GetIp());
			bool validateRun = false;
			string imgKey = "";
			string subKey = "";
			DateTime curTime = DateTime.Parse(DateTime.Now.ToString("yyyy-MM-dd HH:59:57"));
			DateTime? geetestTime = null;
			DateTime receiveTime = DateTime.Now;
			int i = 0;
			while (i < dtCookieTask.Rows.Count && QzUtil.IsExecute(seconds, context))
			{
				int value;
				try
				{
					string type = Util.GetJObject(dtCookieTask.Rows[i], "FType");
					if (type == "方式一")
					{
						string task_id = Util.GetJObject(dtCookieTask.Rows[i], "FTaskId");
						jobName2 = Util.GetJObject(dtCookieTask.Rows[i], "FActivityId");
						name2 = Util.GetJObject(dtCookieTask.Rows[i], "FActName");
						typeName = Util.GetJObject(dtCookieTask.Rows[i], "FTaskName");
						string reward_name = Util.GetJObject(dtCookieTask.Rows[i], "FAwardName");
						string awardId = Util.GetJObject(dtCookieTask.Rows[i], "FAwardId");
						string userId = Util.GetJObject(dtCookieTask.Rows[i], "FUserId");
						string areaId = Util.GetJObject(dtCookieTask.Rows[i], "FAreaId");
						string receive_from = Util.GetJObject(dtCookieTask.Rows[i], "FReceiveFrom");
						dic["Origin"] = "https://www.bilibili.com";
						dic["Referer"] = "https://www.bilibili.com/blackboard/new-award-exchange.html?task_id=" + task_id;
						dic["Accept-Language"] = "zh-CN,zh;q=0.9";
						CancellationTokenSource cts = new CancellationTokenSource();
						while (imgKey == "" || subKey == "")
						{
							JObject jObject = await httpClientFactory.Get("https://api.bilibili.com/x/web-interface/nav", dic);
							if (Util.GetJObject(jObject, "code") == "0")
							{
								imgKey = MyRegex().Split(Util.GetJObject(Util.GetJObject<JToken>(jObject["data"], "wbi_img"), "img_url")).ToList().Last()
									.Replace(".png", "");
								subKey = MyRegex().Split(Util.GetJObject(Util.GetJObject<JToken>(jObject["data"], "wbi_img"), "sub_url")).ToList().Last()
									.Replace(".png", "");
								continue;
							}
							if (browserStatus == "0")
							{
								throw new Exception("浏览器登录失效，无法更新！|" + Util.GetJObject(jObject, "message"));
							}
							if (browserStatus == "2")
							{
								Util.WriteLog("BiliBili", name, jobName, "正在重试尝试|" + Util.GetJObject(jObject, "message"), ConsoleColor.Red);
								await Task.Delay(1000);
								continue;
							}
							Util.WriteLog(jobGroup[1], name, jobName, "Cookie失效,正在更新Cookie！（" + Util.GetJObject(jObject, "message") + "）", ConsoleColor.Yellow);
							string text2 = drCookie["Fid"].ToString() ?? "";
							string jObject2 = Util.GetJObject(jObjectHeader, "User-Agent");
							string text3 = AppSettings.GetVal("TokenPrefix") + Token.GetTokenValue(BusSysUser.Instance.User.Id.ToString());
							string val = AppSettings.GetVal("TokenKey");
							string val2 = AppSettings.GetVal("Url", "Kestrel:Endpoints:Http");
							string text4 = Directory.GetCurrentDirectory() + "\\Util\\Chromium\\Bili-" + key;
							string fileName = text4 + "\\Chromium.exe";
							ProcessStartInfo processStartInfo = new ProcessStartInfo();
							processStartInfo.FileName = fileName;
							processStartInfo.Arguments = "-a " + text3 + " -c \"" + BusSysUser.Instance.User.Cookie + "\" -t Cookie -i " + text2 + " -k " + key + " -s 60 -u " + val2 + " --token " + val + " --ua \"" + jObject2 + "\" -m \"https://www.bilibili.com/\"";
							ProcessStartInfo startInfo = processStartInfo;
							Process.Start(startInfo)?.WaitForExit();
							await Task.Delay(1000);
							string sSql = " SELECT FCookie,FCsrf,FBrowserStatus FROM TCookies WHERE Fid=" + cookieId;
							DataTable dataTable = SQLHelper.BiliLocalDB.RunSqlDt(sSql);
							browserStatus = Util.GetJObject(dataTable.Rows[0], "FBrowserStatus");
							if (browserStatus != "1")
							{
								throw new Exception("浏览器登录失效！");
							}
							csrf = Util.GetJObject(dataTable.Rows[0], "FCsrf");
							dic["Cookie"] = Util.GetJObject(dataTable.Rows[0], "FCookie");
							value = i--;
						}
						Task.Run(async delegate
						{
							await GetStock(subKey, imgKey, type, task_id, conDic, seconds, context, cts.Token);
						}, cts.Token);
						bool taskRun = true;
						int validateTimes = 0;
						while (taskRun && QzUtil.IsExecute(seconds, context))
						{
							string p = "task_id=" + task_id + "&activity_id=" + jobName2 + "&activity_name=" + name2 + "&task_name=" + typeName.Split(" - ")[0] + "&reward_name=" + reward_name + "&gaia_vtoken=&receive_from=" + receive_from + "&csrf=" + csrf;
							while (!validateRun && QzUtil.IsExecute(seconds, context))
							{
								Util.WriteLog(jobGroup[1], name, jobName, "正在打码！", ConsoleColor.Yellow);
								value = validateTimes++;
								if (validateTimes > 4)
								{
									cts.Cancel();
									throw new Exception("打码重试次数过多！");
								}
								JObject jToken = await httpClientFactory.Post("https://api.bilibili.com/x/activity_components/mission/receive?" + BusBiliUtil.GetWRid(imgKey, subKey), p, dic);
								if (Util.GetJObject(jToken, "code") == "202100")
								{
									string v_voucher = Util.GetJObject(jToken, "data");
									JObject jObject3 = await httpClientFactory.Post("https://api.bilibili.com/x/gaia-vgate/v2/register", "csrf=" + csrf + "&v_voucher=" + v_voucher, new Dictionary<string, string> { { "Content-Type", "application/x-www-form-urlencoded" } });
									if (Util.GetJObject(jObject3, "code") == "0")
									{
										string jObject4 = Util.GetJObject(jObject3["data"], "content");
										jObject3 = await BiliGeetest.Geetest4(v_voucher, jObject4, proxyAddress, proxyUserName, proxyPassword);
										if (Util.GetJObject(jObject3, "code") == "0")
										{
											string text5 = HttpUtility.UrlEncode(Util.GetJObject(jObject3["data"], "token"));
											string text6 = HttpUtility.UrlEncode(Util.GetJObject(jObject3["data"], "content"));
											string text7 = HttpUtility.UrlEncode(Util.GetJObject(jObject3["data"], "dm_track"));
											string text8 = "token=" + text5 + "&content=" + text6 + "&dm_track=" + text7 + "&csrf=" + csrf;
											JObject jObject5 = await httpClientFactory.Post("https://api.bilibili.com/x/gaia-vgate/v2/validate", text8, dic);
											if (Util.GetJObject(jObject5, "code") == "0")
											{
												if (Util.GetJObject(jObject5["data"], "is_valid") == "1")
												{
													Util.WriteLog(jobGroup[1], name, jobName, "打码成功，valid验证成功！");
													geetestTime = DateTime.Now;
													validateRun = true;
												}
												else
												{
													Util.WriteLog("BiliBili", name, jobName, "验证失败，正在重试！", ConsoleColor.Red);
												}
											}
											else
											{
												Util.WriteLog("BiliBili", name, jobName, Util.GetJObject(jObject5, "message"), ConsoleColor.Red);
											}
										}
										else
										{
											Util.WriteLog("BiliBili", name, jobName, "验证失败|" + JsonConvert.SerializeObject(jObject3), ConsoleColor.Red);
										}
									}
									else
									{
										Util.WriteLog("BiliBili", name, jobName, Util.GetJObject(jObject3, "message"), ConsoleColor.Red);
									}
								}
								else
								{
									if (Util.GetJObject(jToken, "code") == "202101")
									{
										cts.Cancel();
										throw new Exception(Util.GetJObject(jToken, "message"));
									}
									Util.WriteLog(jobGroup[1], name, jobName, "无需打码！", ConsoleColor.Yellow);
									await Task.Delay(1000);
									if (times % 10 != 2)
									{
										geetestTime = DateTime.Now;
									}
									validateRun = true;
								}
							}
							if ((DateTime.Now > curTime && DateTime.Now < curTime.AddSeconds(5.0)) || (conDic[task_id + "Refresh"] == 1 && (!conDic.TryGetValue(task_id, out value) || conDic[task_id] > 0)))
							{
								while ((DateTime.Now - receiveTime).TotalMilliseconds <= (double)times)
								{
									await Task.Delay(50);
								}
								int time = (int)(DateTime.Now - receiveTime).TotalMilliseconds;
								JObject jObject6 = await httpClientFactory.Post("https://api.bilibili.com/x/activity_components/mission/receive?" + BusBiliUtil.GetWRid(imgKey, subKey), p, dic);
								receiveTime = DateTime.Now;
								typeName = HttpUtility.UrlDecode(typeName);
								if (Util.GetJObject(jObject6, "code") == "0")
								{
									Util.WriteLog(jobGroup[1], name, jobName, typeName + "【库存" + (conDic.TryGetValue(task_id, out var value2) ? value2.ToString() : "未知") + "】已抢到！(" + time + ")", ConsoleColor.Green);
									PushCdkey(type, userId, areaId, cookieId, awardId, jobName2, jObject6["data"], bagCdkey);
									break;
								}
								if (Util.GetJObject(jObject6, "code") == "202031" || Util.GetJObject(jObject6, "code") == "75086")
								{
									Util.WriteLog(jobGroup[1], name, jobName, typeName + "已领取！", ConsoleColor.Green);
									break;
								}
								if (Util.GetJObject(jObject6, "code") == "202033")
								{
									Util.WriteLog(jobGroup[1], name, jobName, typeName + Util.GetJObject(jObject6, "message"), ConsoleColor.Red);
									break;
								}
								if (Util.GetJObject(jObject6, "code") == "75255" || Util.GetJObject(jObject6, "code") == "75154")
								{
									Util.WriteLog(jobGroup[1], name, jobName, typeName + "没有库存啦！(" + time + ")", ConsoleColor.Red);
									if (conDic[task_id + "Refresh"] == 1)
									{
										break;
									}
								}
								else if (Util.GetJObject(jObject6, "code") == "202100")
								{
									int num = (int)(geetestTime.HasValue ? (DateTime.Now - geetestTime.Value).TotalSeconds : (-1.0));
									if ((times % 10 == 1 || times % 10 == 2) && num != -1 && num < 60)
									{
										Util.WriteLog(jobGroup[1], name, jobName, "需要验证，但已为你智能跳过！（" + num + "秒前打码）", ConsoleColor.Yellow);
										continue;
									}
									Util.WriteLog(jobGroup[1], name, jobName, "正在打码！", ConsoleColor.Yellow);
									string v_voucher = Util.GetJObject(jObject6, "data");
									jObject6 = await httpClientFactory.Post("https://api.bilibili.com/x/gaia-vgate/v2/register", "csrf=" + csrf + "&v_voucher=" + v_voucher, new Dictionary<string, string> { { "Content-Type", "application/x-www-form-urlencoded" } });
									if (Util.GetJObject(jObject6, "code") == "0")
									{
										string jObject7 = Util.GetJObject(jObject6["data"], "content");
										jObject6 = await BiliGeetest.Geetest4(v_voucher, jObject7, proxyAddress, proxyUserName, proxyPassword);
										if (Util.GetJObject(jObject6, "code") == "0")
										{
											string text9 = HttpUtility.UrlEncode(Util.GetJObject(jObject6["data"], "token"));
											string text10 = HttpUtility.UrlEncode(Util.GetJObject(jObject6["data"], "content"));
											string text11 = HttpUtility.UrlEncode(Util.GetJObject(jObject6["data"], "dm_track"));
											string text12 = "token=" + text9 + "&content=" + text10 + "&dm_track=" + text11 + "&csrf=" + csrf;
											JObject jObject8 = await httpClientFactory.Post("https://api.bilibili.com/x/gaia-vgate/v2/validate", text12, dic);
											if (Util.GetJObject(jObject8, "code") == "0")
											{
												if (Util.GetJObject(jObject8["data"], "is_valid") == "1")
												{
													Util.WriteLog(jobGroup[1], name, jobName, "打码成功，valid验证成功！");
													geetestTime = DateTime.Now;
													validateRun = true;
												}
												else
												{
													Util.WriteLog("BiliBili", name, jobName, "验证失败，正在重试！", ConsoleColor.Red);
												}
											}
											else
											{
												Util.WriteLog("BiliBili", name, jobName, Util.GetJObject(jObject8, "message"), ConsoleColor.Red);
											}
										}
										else
										{
											Util.WriteLog("BiliBili", name, jobName, "验证失败|" + JsonConvert.SerializeObject(jObject6), ConsoleColor.Red);
										}
									}
									else
									{
										Util.WriteLog("BiliBili", name, jobName, Util.GetJObject(jObject6, "message"), ConsoleColor.Red);
									}
								}
								else
								{
									if (Util.GetJObject(jObject6, "code") == "202101")
									{
										throw new Exception(Util.GetJObject(jObject6, "message"));
									}
									if (Util.GetJObject(jObject6, "code") == "-100")
									{
										Util.WriteLog(jobGroup[1], name, jobName, typeName + "【库存" + (conDic.TryGetValue(task_id, out var value3) ? value3.ToString() : "未知") + "】" + Util.GetJObject(jObject6, "code") + Util.GetJObject(jObject6, "message") + "(" + time + ")", ConsoleColor.Red);
										break;
									}
									Util.WriteLog(jobGroup[1], name, jobName, typeName + "【库存" + (conDic.TryGetValue(task_id, out var value4) ? value4.ToString() : "未知") + "】" + Util.GetJObject(jObject6, "code") + Util.GetJObject(jObject6, "message") + "(" + time + ")", ConsoleColor.Yellow);
								}
							}
							else
							{
								if (conDic[task_id + "Refresh"] != 0 || (conDic.TryGetValue(task_id, out value) && conDic[task_id] > 0))
								{
									Util.WriteLog(jobGroup[1], name, jobName, typeName + "没有库存啦！", ConsoleColor.Red);
									break;
								}
								Util.WriteLog(jobGroup[1], name, jobName, typeName + "等待刷新库存！", ConsoleColor.Yellow);
								await Task.Delay(times / 2);
							}
						}
						cts.Cancel();
					}
				}
				catch (Exception ex)
				{
					Util.WriteLog(jobGroup[1], name, jobName, ex.Message, ConsoleColor.Red);
				}
				value = i++;
			}
			Util.WriteLog(jobGroup[1], name, jobName, "结束");
		}
		catch (Exception ex2)
		{
			Util.WriteLog(jobGroup[1], name, jobName, ex2.Message, ConsoleColor.Red);
			BusBiliCookies.AddCookieStatus(bagUser, cookieId, ex2.Message);
		}
	}

	private static async Task Receive3(string[] jobGroup, string jobName, DataRow drCookie, DataTable dtCookieTask, int times, ConcurrentBag<JToken> bagUser, ConcurrentBag<JToken> bagCdkey, int seconds, IJobExecutionContext context)
	{
		string cookieId = drCookie["Fid"].ToString() ?? "";
		string key = drCookie["FKey"].ToString() ?? "";
		string cookie = drCookie["FCookie"].ToString() ?? "";
		string name = drCookie["FName"].ToString() ?? "";
		string text = drCookie["FHeaders"].ToString() ?? "";
		string csrf = drCookie["FCsrf"].ToString() ?? "";
		string proxyAddress = drCookie["FProxyAddress"].ToString() ?? "";
		string proxyUserName = drCookie["FProxyUserName"].ToString() ?? "";
		string proxyPassword = drCookie["FProxyPassword"].ToString() ?? "";
		string browserStatus = drCookie["FBrowserStatus"].ToString() ?? "";
		try
		{
			Util.WriteLog(jobGroup[1], name, jobName, "开始");
			JObject jObjectHeader = JObject.Parse(text);
			HttpClientHandler defaultHandler = null;
			if (proxyAddress != "")
			{
				defaultHandler = new HttpClientHandler
				{
					Proxy = new WebProxy
					{
						Address = new Uri(proxyAddress),
						Credentials = new NetworkCredential(proxyUserName, proxyPassword)
					}
				};
			}
			Dictionary<string, string> dic = HttpClientFactory.FormataHeader(text, cookie);
			HttpClientFactory httpClientFactory = new HttpClientFactory(key, dic, defaultHandler);
			string typeName = jobGroup[1];
			string name2 = name;
			string jobName2 = jobName;
			Util.WriteLog(typeName, name2, jobName2, await httpClientFactory.GetIp());
			bool validateRun = false;
			string imgKey = "";
			string subKey = "";
			DateTime? geetestTime = null;
			for (int i = 0; i < dtCookieTask.Rows.Count; i++)
			{
				if (!QzUtil.IsExecute(seconds, context))
				{
					break;
				}
				jobName2 = Util.GetJObject(dtCookieTask.Rows[i], "FType");
				if (!(jobName2 == "方式一"))
				{
					continue;
				}
				name2 = Util.GetJObject(dtCookieTask.Rows[i], "FTaskId");
				typeName = Util.GetJObject(dtCookieTask.Rows[i], "FActivityId");
				string activity_name = Util.GetJObject(dtCookieTask.Rows[i], "FActName");
				string task_name = Util.GetJObject(dtCookieTask.Rows[i], "FTaskName");
				string reward_name = Util.GetJObject(dtCookieTask.Rows[i], "FAwardName");
				string awardId = Util.GetJObject(dtCookieTask.Rows[i], "FAwardId");
				string userId = Util.GetJObject(dtCookieTask.Rows[i], "FUserId");
				string areaId = Util.GetJObject(dtCookieTask.Rows[i], "FAreaId");
				string gaia_vtoken = "";
				string receive_from = Util.GetJObject(dtCookieTask.Rows[i], "FReceiveFrom");
				dic["Origin"] = "https://www.bilibili.com";
				dic["Referer"] = "https://www.bilibili.com/blackboard/new-award-exchange.html?task_id=" + name2;
				dic["Accept-Language"] = "zh-CN,zh;q=0.9";
				while (imgKey == "" || subKey == "")
				{
					JObject jObject = await httpClientFactory.Get("https://api.bilibili.com/x/web-interface/nav", dic);
					if (Util.GetJObject(jObject, "code") == "0")
					{
						imgKey = MyRegex().Split(Util.GetJObject(Util.GetJObject<JToken>(jObject["data"], "wbi_img"), "img_url")).ToList().Last()
							.Replace(".png", "");
						subKey = MyRegex().Split(Util.GetJObject(Util.GetJObject<JToken>(jObject["data"], "wbi_img"), "sub_url")).ToList().Last()
							.Replace(".png", "");
						continue;
					}
					switch (browserStatus)
					{
					case "0":
						throw new Exception("浏览器登录失效，无法更新！|" + Util.GetJObject(jObject, "message"));
					case "2":
					case "":
						Util.WriteLog("BiliBili", name, jobName, "正在重试尝试|" + Util.GetJObject(jObject, "message"), ConsoleColor.Red);
						await Task.Delay(1000);
						continue;
					}
					Util.WriteLog(jobGroup[1], name, jobName, "Cookie失效,正在更新Cookie！（" + Util.GetJObject(jObject, "message") + "）", ConsoleColor.Yellow);
					string text2 = drCookie["Fid"].ToString() ?? "";
					string jObject2 = Util.GetJObject(jObjectHeader, "User-Agent");
					string text3 = AppSettings.GetVal("TokenPrefix") + Token.GetTokenValue(BusSysUser.Instance.User.Id.ToString());
					string val = AppSettings.GetVal("TokenKey");
					string val2 = AppSettings.GetVal("Url", "Kestrel:Endpoints:Http");
					string text4 = Directory.GetCurrentDirectory() + "\\Util\\Chromium\\Bili-" + key;
					string fileName = text4 + "\\Chromium.exe";
					ProcessStartInfo processStartInfo = new ProcessStartInfo();
					processStartInfo.FileName = fileName;
					processStartInfo.Arguments = "-a " + text3 + " -c \"" + BusSysUser.Instance.User.Cookie + "\" -t Cookie -i " + text2 + " -k " + key + " -s 60 -u " + val2 + " --token " + val + " --ua \"" + jObject2 + "\" -m \"https://www.bilibili.com/\"";
					ProcessStartInfo startInfo = processStartInfo;
					Process.Start(startInfo)?.WaitForExit();
					await Task.Delay(1000);
					string sSql = " SELECT FCookie,FCsrf,FBrowserStatus FROM TCookies WHERE Fid=" + cookieId;
					DataTable dataTable = SQLHelper.BiliLocalDB.RunSqlDt(sSql);
					browserStatus = Util.GetJObject(dataTable.Rows[0], "FBrowserStatus");
					if (browserStatus != "1")
					{
						throw new Exception("浏览器登录失效！");
					}
					csrf = Util.GetJObject(dataTable.Rows[0], "FCsrf");
					dic["Cookie"] = Util.GetJObject(dataTable.Rows[0], "FCookie");
					i--;
				}
				bool taskRun = true;
				int validateTimes = 0;
				int reward_period_stock_num = 0;
				string status = "-1";
				DateTime lastSearch = DateTime.Now;
				JObject jObject3 = new JObject();
				while (taskRun && QzUtil.IsExecute(seconds, context))
				{
					string p = "task_id=" + name2 + "&activity_id=" + typeName + "&activity_name=" + activity_name + "&task_name=" + task_name.Split(" - ")[0] + "&reward_name=" + reward_name + "&gaia_vtoken=" + gaia_vtoken + "&receive_from=" + receive_from + "&csrf=" + csrf;
					while (!validateRun && QzUtil.IsExecute(seconds, context))
					{
						Util.WriteLog(jobGroup[1], name, jobName, "正在提前打码！", ConsoleColor.Yellow);
						validateTimes++;
						if (validateTimes > 4)
						{
							throw new Exception("打码重试次数过多！");
						}
						JObject jToken = await httpClientFactory.Post("https://api.bilibili.com/x/activity_components/mission/receive?" + BusBiliUtil.GetWRid(imgKey, subKey), p, dic);
						if (Util.GetJObject(jToken, "code") == "202100")
						{
							string v_voucher = Util.GetJObject(jToken, "data");
							jObject3 = await httpClientFactory.Post("https://api.bilibili.com/x/gaia-vgate/v2/register", "csrf=" + csrf + "&v_voucher=" + v_voucher, new Dictionary<string, string> { { "Content-Type", "application/x-www-form-urlencoded" } });
							if (!(Util.GetJObject(jObject3, "code") == "0"))
							{
								Util.WriteLog("BiliBili", name, jobName, Util.GetJObject(jObject3, "message"), ConsoleColor.Red);
								continue;
							}
							string jObject4 = Util.GetJObject(jObject3["data"], "content");
							jObject3 = await BiliGeetest.Geetest4(v_voucher, jObject4, proxyAddress, proxyUserName, proxyPassword);
							if (!(Util.GetJObject(jObject3, "code") == "0"))
							{
								Util.WriteLog("BiliBili", name, jobName, "验证失败|" + JsonConvert.SerializeObject(jObject3), ConsoleColor.Red);
								continue;
							}
							string text5 = HttpUtility.UrlEncode(Util.GetJObject(jObject3["data"], "token"));
							string text6 = HttpUtility.UrlEncode(Util.GetJObject(jObject3["data"], "content"));
							string text7 = HttpUtility.UrlEncode(Util.GetJObject(jObject3["data"], "dm_track"));
							string text8 = "token=" + text5 + "&content=" + text6 + "&dm_track=" + text7 + "&csrf=" + csrf;
							JObject jObject5 = await httpClientFactory.Post("https://api.bilibili.com/x/gaia-vgate/v2/validate", text8, dic);
							if (!(Util.GetJObject(jObject5, "code") == "0"))
							{
								Util.WriteLog("BiliBili", name, jobName, Util.GetJObject(jObject5, "message"), ConsoleColor.Red);
								continue;
							}
							if (!(Util.GetJObject(jObject5["data"], "is_valid") == "1"))
							{
								Util.WriteLog("BiliBili", name, jobName, "验证失败，正在重试！", ConsoleColor.Red);
								continue;
							}
							Util.WriteLog(jobGroup[1], name, jobName, "打码成功，valid验证成功！");
							geetestTime = DateTime.Now;
						}
						else
						{
							if (Util.GetJObject(jToken, "code") == "202101")
							{
								throw new Exception(Util.GetJObject(jToken, "message"));
							}
							Util.WriteLog(jobGroup[1], name, jobName, "无需打码！", ConsoleColor.Yellow);
							await Task.Delay(1000);
							if (times % 10 != 2)
							{
								geetestTime = DateTime.Now;
							}
						}
						validateRun = true;
					}
					DateTime now = DateTime.Now;
					DateTime dateTime = new DateTime(now.Year, now.Month, now.Day, 17, 59, 56);
					DateTime dateTime2 = new DateTime(now.Year, now.Month, now.Day, 18, 0, 3);
					if (now >= dateTime && now <= dateTime2)
					{
						status = "0";
					}
					else if (status == "-1" || (DateTime.Now - lastSearch).TotalMilliseconds > 1000.0)
					{
						string text9 = "task_id=" + name2 + "&web_location=888.81821";
						lastSearch = DateTime.Now;
						jObject3 = await httpClientFactory.Get("https://api.bilibili.com/x/activity_components/mission/info?" + BusBiliUtil.GetWRid(imgKey, subKey, text9), dic);
						if (Util.GetJObject(jObject3, "code") == "0")
						{
							JToken jObject6 = Util.GetJObject<JToken>(jObject3["data"], "stock_info");
							int jObject7 = Util.GetJObject<int>(jObject6, "total_stock");
							int jObject8 = Util.GetJObject<int>(jObject6, "day_stock");
							reward_period_stock_num = (Util.GetJObject<bool>(jObject6, "day_stock_limit") ? jObject8 : jObject7);
							status = Util.GetJObject(jObject3["data"], "status");
						}
						else
						{
							Util.WriteLog(jobGroup[1], name, jobName, Util.GetJObject(jObject3, "message"), ConsoleColor.Red);
							await Task.Delay(1000);
							status = "-1";
						}
					}
					if (status == "-1")
					{
						await Task.Delay(new Random().Next(100, 1100));
						continue;
					}
					if (reward_period_stock_num == 0)
					{
						Util.WriteLog(jobGroup[1], name, jobName, task_name + "没有库存啦！", ConsoleColor.Red);
						break;
					}
					if (status == "0")
					{
						jObject3 = await httpClientFactory.Post("https://api.bilibili.com/x/activity_components/mission/receive?" + BusBiliUtil.GetWRid(imgKey, subKey), p, dic);
						await Task.Delay(times);
						task_name = HttpUtility.UrlDecode(task_name);
						if (Util.GetJObject(jObject3, "code") == "0")
						{
							Util.WriteLog(jobGroup[1], name, jobName, task_name + "【库存" + reward_period_stock_num + "】已抢到！", ConsoleColor.Green);
							PushCdkey(jobName2, userId, areaId, cookieId, awardId, typeName, jObject3["data"], bagCdkey);
							break;
						}
						if (Util.GetJObject(jObject3, "code") == "202031" || Util.GetJObject(jObject3, "code") == "75086")
						{
							Util.WriteLog(jobGroup[1], name, jobName, task_name + "已领取！", ConsoleColor.Green);
							break;
						}
						if (Util.GetJObject(jObject3, "code") == "75255" || Util.GetJObject(jObject3, "code") == "75154")
						{
							Util.WriteLog(jobGroup[1], name, jobName, task_name + "没有库存啦！", ConsoleColor.Red);
							break;
						}
						if (Util.GetJObject(jObject3, "code") == "202100")
						{
							int num = (int)(geetestTime.HasValue ? (DateTime.Now - geetestTime.Value).TotalSeconds : (-1.0));
							if ((times % 10 == 1 || times % 10 == 2) && num != -1 && num < 40)
							{
								Util.WriteLog(jobGroup[1], name, jobName, "需要验证，但已为你智能跳过！（" + num + "秒前打码）", ConsoleColor.Yellow);
								continue;
							}
							Util.WriteLog(jobGroup[1], name, jobName, "正在打码！", ConsoleColor.Yellow);
							string v_voucher = Util.GetJObject(jObject3, "data");
							jObject3 = await httpClientFactory.Post("https://api.bilibili.com/x/gaia-vgate/v2/register", "csrf=" + csrf + "&v_voucher=" + v_voucher, new Dictionary<string, string> { { "Content-Type", "application/x-www-form-urlencoded" } });
							if (Util.GetJObject(jObject3, "code") == "0")
							{
								string jObject9 = Util.GetJObject(jObject3["data"], "content");
								jObject3 = await BiliGeetest.Geetest4(v_voucher, jObject9, proxyAddress, proxyUserName, proxyPassword);
								if (Util.GetJObject(jObject3, "code") == "0")
								{
									string text10 = HttpUtility.UrlEncode(Util.GetJObject(jObject3["data"], "token"));
									string text11 = HttpUtility.UrlEncode(Util.GetJObject(jObject3["data"], "content"));
									string text12 = HttpUtility.UrlEncode(Util.GetJObject(jObject3["data"], "dm_track"));
									string text13 = "token=" + text10 + "&content=" + text11 + "&dm_track=" + text12 + "&csrf=" + csrf;
									JObject jObject10 = await httpClientFactory.Post("https://api.bilibili.com/x/gaia-vgate/v2/validate", text13, dic);
									if (Util.GetJObject(jObject10, "code") == "0")
									{
										if (Util.GetJObject(jObject10["data"], "is_valid") == "1")
										{
											Util.WriteLog(jobGroup[1], name, jobName, "打码成功，valid验证成功！");
											geetestTime = DateTime.Now;
											validateRun = true;
										}
										else
										{
											Util.WriteLog("BiliBili", name, jobName, "验证失败，正在重试！", ConsoleColor.Red);
										}
									}
									else
									{
										Util.WriteLog("BiliBili", name, jobName, Util.GetJObject(jObject10, "message"), ConsoleColor.Red);
									}
								}
								else
								{
									Util.WriteLog("BiliBili", name, jobName, "验证失败|" + JsonConvert.SerializeObject(jObject3), ConsoleColor.Red);
								}
							}
							else
							{
								Util.WriteLog("BiliBili", name, jobName, Util.GetJObject(jObject3, "message"), ConsoleColor.Red);
							}
						}
						else
						{
							if (Util.GetJObject(jObject3, "code") == "202101")
							{
								throw new Exception(Util.GetJObject(jObject3, "message"));
							}
							Util.WriteLog(jobGroup[1], name, jobName, task_name + "【库存" + reward_period_stock_num + "】" + Util.GetJObject(jObject3, "code") + "-" + Util.GetJObject(jObject3, "message"), ConsoleColor.Yellow);
						}
					}
					else
					{
						if (status == "6")
						{
							Util.WriteLog(jobGroup[1], name, jobName, task_name + "已领取！【库存" + reward_period_stock_num + "】", ConsoleColor.Green);
							break;
						}
						if (status != "0")
						{
							Util.WriteLog(jobGroup[1], name, jobName, task_name + "等待刷新(" + Util.GetJObject(jObject3["data"], "message") + ")！【库存" + reward_period_stock_num + "】", ConsoleColor.Yellow);
							await Task.Delay(new Random().Next(1001, 1100));
						}
					}
				}
			}
			Util.WriteLog(jobGroup[1], name, jobName, "结束");
		}
		catch (Exception ex)
		{
			Util.WriteLog(jobGroup[1], name, jobName, ex.Message, ConsoleColor.Red);
			BusBiliCookies.AddCookieStatus(bagUser, cookieId, ex.Message);
		}
	}

	private static async Task Receive4(string[] jobGroup, string jobName, DataRow drCookie, DataTable dtCookieTask, ConcurrentBag<JToken> bagUser, ConcurrentBag<JToken> bagCdkey, int seconds, IJobExecutionContext context)
	{
		string cookieId = drCookie["Fid"].ToString() ?? "";
		string key = drCookie["FKey"].ToString() ?? "";
		string cookie = drCookie["FCookie"].ToString() ?? "";
		string name = drCookie["FName"].ToString() ?? "";
		string text = drCookie["FHeaders"].ToString() ?? "";
		string csrf = drCookie["FCsrf"].ToString() ?? "";
		string proxyAddress = drCookie["FProxyAddress"].ToString() ?? "";
		string proxyUserName = drCookie["FProxyUserName"].ToString() ?? "";
		string proxyPassword = drCookie["FProxyPassword"].ToString() ?? "";
		string browserStatus = drCookie["FBrowserStatus"].ToString() ?? "";
		try
		{
			Util.WriteLog(jobGroup[1], name, jobName, "开始");
			JObject jObjectHeader = JObject.Parse(text);
			HttpClientHandler defaultHandler = null;
			if (proxyAddress != "")
			{
				defaultHandler = new HttpClientHandler
				{
					Proxy = new WebProxy
					{
						Address = new Uri(proxyAddress),
						Credentials = new NetworkCredential(proxyUserName, proxyPassword)
					}
				};
			}
			Dictionary<string, string> dic = HttpClientFactory.FormataHeader(text, cookie);
			HttpClientFactory httpClientFactory = new HttpClientFactory(key, dic, defaultHandler);
			string typeName = jobGroup[1];
			string name2 = name;
			string jobName2 = jobName;
			Util.WriteLog(typeName, name2, jobName2, await httpClientFactory.GetIp());
			string imgKey = "";
			string subKey = "";
			DateTime? geetestTime = null;
			for (int i = 0; i < dtCookieTask.Rows.Count && QzUtil.IsExecute(seconds, context); i++)
			{
				jobName2 = Util.GetJObject(dtCookieTask.Rows[i], "FType");
				if (!(jobName2 == "方式一"))
				{
					continue;
				}
				name2 = Util.GetJObject(dtCookieTask.Rows[i], "FTaskId");
				typeName = Util.GetJObject(dtCookieTask.Rows[i], "FActivityId");
				string activity_name = Util.GetJObject(dtCookieTask.Rows[i], "FActName");
				string task_name = Util.GetJObject(dtCookieTask.Rows[i], "FTaskName");
				string awardId = Util.GetJObject(dtCookieTask.Rows[i], "FAwardId");
				string userId = Util.GetJObject(dtCookieTask.Rows[i], "FUserId");
				string areaId = Util.GetJObject(dtCookieTask.Rows[i], "FAreaId");
				string reward_name = Util.GetJObject(dtCookieTask.Rows[i], "FAwardName");
				string gaia_vtoken = "";
				string receive_from = Util.GetJObject(dtCookieTask.Rows[i], "FReceiveFrom");
				dic["Origin"] = "https://www.bilibili.com";
				dic["Referer"] = "https://www.bilibili.com/blackboard/new-award-exchange.html?task_id=" + name2;
				dic["Accept-Language"] = "zh-CN,zh;q=0.9";
				if (imgKey == "" || subKey == "")
				{
					JObject jObject = await httpClientFactory.Get("https://api.bilibili.com/x/web-interface/nav", dic);
					if (!(Util.GetJObject(jObject, "code") == "0"))
					{
						if (browserStatus == "0")
						{
							throw new Exception("浏览器登录失效，无法更新！");
						}
						if (browserStatus == "2")
						{
							throw new Exception("导入的Cookie，无法更新Cookie！");
						}
						Util.WriteLog(jobGroup[1], name, jobName, "Cookie失效,正在更新Cookie！（" + Util.GetJObject(jObject, "message") + "）", ConsoleColor.Yellow);
						string text2 = drCookie["Fid"].ToString() ?? "";
						string jObject2 = Util.GetJObject(jObjectHeader, "User-Agent");
						string text3 = AppSettings.GetVal("TokenPrefix") + Token.GetTokenValue(BusSysUser.Instance.User.Id.ToString());
						string val = AppSettings.GetVal("TokenKey");
						string val2 = AppSettings.GetVal("Url", "Kestrel:Endpoints:Http");
						string text4 = Directory.GetCurrentDirectory() + "\\Util\\Chromium\\Bili-" + key;
						string fileName = text4 + "\\Chromium.exe";
						ProcessStartInfo processStartInfo = new ProcessStartInfo();
						processStartInfo.FileName = fileName;
						processStartInfo.Arguments = "-a " + text3 + " -c \"" + BusSysUser.Instance.User.Cookie + "\" -t Cookie -i " + text2 + " -k " + key + " -s 60 -u " + val2 + " --token " + val + " --ua \"" + jObject2 + "\" -m \"https://www.bilibili.com/\"";
						ProcessStartInfo startInfo = processStartInfo;
						Process.Start(startInfo)?.WaitForExit();
						await Task.Delay(1000);
						string sSql = " SELECT FCookie,FCsrf,FBrowserStatus FROM TCookies WHERE Fid=" + cookieId;
						DataTable dataTable = SQLHelper.BiliLocalDB.RunSqlDt(sSql);
						browserStatus = Util.GetJObject(dataTable.Rows[0], "FBrowserStatus");
						if (browserStatus != "1")
						{
							throw new Exception("浏览器登录失效！");
						}
						csrf = Util.GetJObject(dataTable.Rows[0], "FCsrf");
						dic["Cookie"] = Util.GetJObject(dataTable.Rows[0], "FCookie");
						i--;
						continue;
					}
					imgKey = MyRegex().Split(Util.GetJObject(Util.GetJObject<JToken>(jObject["data"], "wbi_img"), "img_url")).ToList().Last()
						.Replace(".png", "");
					subKey = MyRegex().Split(Util.GetJObject(Util.GetJObject<JToken>(jObject["data"], "wbi_img"), "sub_url")).ToList().Last()
						.Replace(".png", "");
				}
				bool taskRun = true;
				new JObject();
				while (taskRun && QzUtil.IsExecute(seconds, context))
				{
					string p = "task_id=" + name2 + "&activity_id=" + typeName + "&activity_name=" + activity_name + "&task_name=" + task_name.Split(" - ")[0] + "&reward_name=" + reward_name + "&gaia_vtoken=" + gaia_vtoken + "&receive_from=" + receive_from + "&csrf=" + csrf;
					string text5 = "task_id=" + name2 + "&web_location=888.81821";
					JObject jObject3 = await httpClientFactory.Get("https://api.bilibili.com/x/activity_components/mission/info?" + BusBiliUtil.GetWRid(imgKey, subKey, text5), dic);
					await Task.Delay(2000);
					if (Util.GetJObject(jObject3, "code") == "0")
					{
						JToken jObject4 = Util.GetJObject<JToken>(jObject3["data"], "stock_info");
						int jObject5 = Util.GetJObject<int>(jObject4, "total_stock");
						int jObject6 = Util.GetJObject<int>(jObject4, "day_stock");
						int reward_period_stock_num = (Util.GetJObject<bool>(jObject4, "day_stock_limit") ? jObject6 : jObject5);
						string jObject7 = Util.GetJObject(jObject3["data"], "status");
						if (reward_period_stock_num == 0)
						{
							Util.WriteLog(jobGroup[1], name, jobName, task_name + "没有库存啦！", ConsoleColor.Red);
							break;
						}
						if (jObject7 == "7")
						{
							Util.WriteLog(jobGroup[1], name, jobName, task_name + "暂无领取资格！", ConsoleColor.Red);
							break;
						}
						if (Util.GetJObject(jObject3["data"], "status") == "0")
						{
							jObject3 = await httpClientFactory.Post("https://api.bilibili.com/x/activity_components/mission/receive?" + BusBiliUtil.GetWRid(imgKey, subKey), p, dic);
							task_name = HttpUtility.UrlDecode(task_name);
							if (Util.GetJObject(jObject3, "code") == "0")
							{
								Util.WriteLog(jobGroup[1], name, jobName, task_name + "【库存" + reward_period_stock_num + "】已抢到！", ConsoleColor.Green);
								PushCdkey(jobName2, userId, areaId, cookieId, awardId, typeName, jObject3["data"], bagCdkey);
								break;
							}
							if (Util.GetJObject(jObject3, "code") == "202031" || Util.GetJObject(jObject3, "code") == "75086")
							{
								Util.WriteLog(jobGroup[1], name, jobName, task_name + "已领取！", ConsoleColor.Green);
								break;
							}
							if (Util.GetJObject(jObject3, "code") == "75255" || Util.GetJObject(jObject3, "code") == "75154")
							{
								Util.WriteLog(jobGroup[1], name, jobName, task_name + "没有库存啦！", ConsoleColor.Red);
								break;
							}
							if (Util.GetJObject(jObject3, "code") == "202100")
							{
								int num = (int)(geetestTime.HasValue ? (DateTime.Now - geetestTime.Value).TotalSeconds : (-1.0));
								if (num != -1 && num < 60)
								{
									Util.WriteLog(jobGroup[1], name, jobName, "需要验证，但已为你智能跳过！（" + num + "秒前打码）", ConsoleColor.Yellow);
									continue;
								}
								Util.WriteLog(jobGroup[1], name, jobName, "正在打码！", ConsoleColor.Yellow);
								string v_voucher = Util.GetJObject(jObject3, "data");
								jObject3 = await httpClientFactory.Post("https://api.bilibili.com/x/gaia-vgate/v2/register", "csrf=" + csrf + "&v_voucher=" + v_voucher, new Dictionary<string, string> { { "Content-Type", "application/x-www-form-urlencoded" } });
								if (Util.GetJObject(jObject3, "code") == "0")
								{
									string jObject8 = Util.GetJObject(jObject3["data"], "content");
									jObject3 = await BiliGeetest.Geetest4(v_voucher, jObject8, proxyAddress, proxyUserName, proxyPassword);
									if (Util.GetJObject(jObject3, "code") == "0")
									{
										string text6 = HttpUtility.UrlEncode(Util.GetJObject(jObject3["data"], "token"));
										string text7 = HttpUtility.UrlEncode(Util.GetJObject(jObject3["data"], "content"));
										string text8 = HttpUtility.UrlEncode(Util.GetJObject(jObject3["data"], "dm_track"));
										string text9 = "token=" + text6 + "&content=" + text7 + "&dm_track=" + text8 + "&csrf=" + csrf;
										JObject jObject9 = await httpClientFactory.Post("https://api.bilibili.com/x/gaia-vgate/v2/validate", text9, dic);
										if (Util.GetJObject(jObject9, "code") == "0")
										{
											if (Util.GetJObject(jObject9["data"], "is_valid") == "1")
											{
												Util.WriteLog(jobGroup[1], name, jobName, "打码成功，valid验证成功！");
												geetestTime = DateTime.Now;
											}
											else
											{
												Util.WriteLog("BiliBili", name, jobName, "验证失败，正在重试！", ConsoleColor.Red);
											}
										}
										else
										{
											Util.WriteLog("BiliBili", name, jobName, Util.GetJObject(jObject9, "message"), ConsoleColor.Red);
										}
									}
									else
									{
										Util.WriteLog("BiliBili", name, jobName, "验证失败|" + JsonConvert.SerializeObject(jObject3), ConsoleColor.Red);
									}
								}
								else
								{
									Util.WriteLog("BiliBili", name, jobName, Util.GetJObject(jObject3, "message"), ConsoleColor.Red);
								}
							}
							else
							{
								if (Util.GetJObject(jObject3, "code") == "202101")
								{
									throw new Exception(Util.GetJObject(jObject3, "message"));
								}
								Util.WriteLog(jobGroup[1], name, jobName, task_name + "【库存" + reward_period_stock_num + "】" + Util.GetJObject(jObject3, "code") + "-" + Util.GetJObject(jObject3, "message"), ConsoleColor.Yellow);
							}
						}
						else
						{
							if (Util.GetJObject(jObject3["data"], "status") == "6")
							{
								Util.WriteLog(jobGroup[1], name, jobName, task_name + "已领取！【库存" + reward_period_stock_num + "】", ConsoleColor.Green);
								break;
							}
							if (Util.GetJObject(jObject3["data"], "status") != "0")
							{
								Util.WriteLog(jobGroup[1], name, jobName, task_name + "等待刷新(" + Util.GetJObject(jObject3["data"], "message") + ")！【库存" + reward_period_stock_num + "】", ConsoleColor.Yellow);
								await Task.Delay(new Random().Next(1001, 1100));
							}
						}
						continue;
					}
					Util.WriteLog(jobGroup[1], name, jobName, Util.GetJObject(jObject3, "message"), ConsoleColor.Red);
					break;
				}
			}
			Util.WriteLog(jobGroup[1], name, jobName, "结束");
		}
		catch (Exception ex)
		{
			Util.WriteLog(jobGroup[1], name, jobName, ex.Message, ConsoleColor.Red);
			BusBiliCookies.AddCookieStatus(bagUser, cookieId, ex.Message);
		}
	}

	private static void PushCdkey(string areaType, string userId, string areaId, string cookieId, string awardId, string activityId, JToken? info, ConcurrentBag<JToken> bagCdkey)
	{
		if (info == null)
		{
			return;
		}
		string text = "绑定账号";
		string text2 = "0";
		if (areaType == "方式一")
		{
			JToken jToken = info["extra_info"];
			if (jToken != null && jToken.Type != JTokenType.Null)
			{
				text = Util.GetJObject(jToken, "cdkey_content");
				text2 = Util.GetJObject(jToken, "cdkey_id");
			}
			long seconds = long.Parse(Util.GetJObject(info, "receive_time", DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString()));
			string text3 = DateTimeOffset.FromUnixTimeSeconds(seconds).LocalDateTime.ToString("yyyy-MM-dd HH:mm:ss");
			bagCdkey.Add(new JObject
			{
				["FStatus"] = -1,
				["FUserId"] = userId,
				["FAreaId"] = areaId,
				["FCookieId"] = cookieId,
				["FName"] = info["award_name"],
				["FCdkey"] = text,
				["FDate"] = text3,
				["FAwardId"] = awardId,
				["FActivityId"] = activityId,
				["FUniqueId"] = Util.GetJObject(info, "unique_id"),
				["FUniqueKey"] = Util.GetJObject(info, "unique_id"),
				["FCdkeyId"] = text2
			});
		}
	}

	private static async Task<JObject> BulletScreen(string csrf, string roomNo, HttpClientFactory httpClientFactory, string msg)
	{
		Dictionary<string, string> dictionary = new Dictionary<string, string>();
		Random random = new Random();
		StringBuilder stringBuilder = new StringBuilder();
		for (int i = 0; i < 30; i++)
		{
			stringBuilder.Append("0123456789"[random.Next("0123456789".Length)]);
		}
		dictionary.Add("Content-Type", "multipart/form-data; boundary=---------------------------" + stringBuilder);
		string text = "bubble=0&color=16777215&mode=1&room_type=0&jumpfrom=0&reply_mid=0&reply_attr=0&replay_dmid=&fontsize=25&statistics={\"appId\":100,\"platform\":5}&rnd=" + new DateTimeOffset(DateTime.UtcNow).ToUnixTimeSeconds() + "&roomid=" + roomNo + "&csrf=" + csrf + "&csrf_token=" + csrf;
		text = text + "&msg=" + msg;
		return await httpClientFactory.Post("https://api.live.bilibili.com/msg/send", text, dictionary);
	}

	private static async Task GetStock(string subKey, string imgKey, string type, string taskId, ConcurrentDictionary<string, int> conDic, int seconds, IJobExecutionContext context, CancellationToken token)
	{
		DateTime now = DateTime.Now;
		DateTime? stock = ((!(DateTime.Parse(now.ToString("yyyy-MM-dd 23:30:00")) < now)) ? new DateTime?(DateTime.Parse(now.AddHours(1.0).ToString("yyyy-MM-dd HH:00:02"))) : new DateTime?(DateTime.Parse(now.AddDays(1.0).ToString("yyyy-MM-dd 00:00:02"))));
		subKey = ((subKey == "") ? "7cd084941338484aae1ad9425b84077c" : subKey);
		imgKey = ((imgKey == "") ? "4932caff0ff746eab6f01bf08b70ac45" : imgKey);
		if (conDic.TryGetValue(taskId + "Copy", out var _))
		{
			return;
		}
		conDic[taskId + "Copy"] = 1;
		HttpClientFactory httpClientFactory1 = new HttpClientFactory();
		while (!token.IsCancellationRequested && QzUtil.IsExecute(seconds, context))
		{
			if (type == "方式一")
			{
				string text = "task_id=" + taskId + "&web_location=888.81821";
				string url = "https://api.bilibili.com/x/activity_components/mission/info?" + BusBiliUtil.GetWRid(imgKey, subKey, text);
				JObject jObject = await httpClientFactory1.Get(url);
				if (Util.GetJObject(jObject, "code") == "0")
				{
					JToken jObject2 = Util.GetJObject<JToken>(jObject["data"], "stock_info");
					int jObject3 = Util.GetJObject<int>(jObject2, "total_stock");
					int jObject4 = Util.GetJObject<int>(jObject2, "day_stock");
					int num = (Util.GetJObject<bool>(jObject2, "day_stock_limit") ? jObject4 : jObject3);
					string jObject5 = Util.GetJObject(jObject["data"], "status");
					if (Util.GetJObject(jObject["data"], "message").Contains("已达上限") || jObject5 == "7" || jObject5 == "11")
					{
						num = 0;
					}
					if (num > 0 || DateTime.Now > stock)
					{
						conDic[taskId + "Refresh"] = 1;
					}
					conDic[taskId] = num;
					if (conDic[taskId] == 0 && conDic[taskId + "Refresh"] == 1)
					{
						break;
					}
				}
			}
			await Task.Delay(500, token);
		}
	}

	private static async Task GetPublicStock(string subKey, string imgKey, string type, string taskId, ConcurrentDictionary<string, int> conDic, int seconds, IJobExecutionContext context, CancellationToken token)
	{
		DateTime now = DateTime.Now;
		DateTime? stock = ((!(DateTime.Parse(now.ToString("yyyy-MM-dd 23:30:00")) < now)) ? new DateTime?(DateTime.Parse(now.AddHours(1.0).ToString("yyyy-MM-dd HH:00:02"))) : new DateTime?(DateTime.Parse(now.AddDays(1.0).ToString("yyyy-MM-dd 00:00:02"))));
		subKey = ((subKey == "") ? "7cd084941338484aae1ad9425b84077c" : subKey);
		imgKey = ((imgKey == "") ? "4932caff0ff746eab6f01bf08b70ac45" : imgKey);
		HttpClientFactory httpClientFactory1 = new HttpClientFactory();
		while (!token.IsCancellationRequested && QzUtil.IsExecute(seconds, context))
		{
			if (type == "方式一")
			{
				string text = "task_id=" + taskId + "&web_location=888.81821";
				string url = "https://api.bilibili.com/x/activity_components/mission/info?" + BusBiliUtil.GetWRid(imgKey, subKey, text);
				JObject jObject = await httpClientFactory1.Get(url);
				if (Util.GetJObject(jObject, "code") == "0")
				{
					JToken jObject2 = Util.GetJObject<JToken>(jObject["data"], "stock_info");
					int jObject3 = Util.GetJObject<int>(jObject2, "total_stock");
					int jObject4 = Util.GetJObject<int>(jObject2, "day_stock");
					int num = (Util.GetJObject<bool>(jObject2, "day_stock_limit") ? jObject4 : jObject3);
					string jObject5 = Util.GetJObject(jObject["data"], "status");
					if (Util.GetJObject(jObject["data"], "message").Contains("已达上限") || jObject5 == "7" || jObject5 == "11")
					{
						num = 0;
					}
					if (num > 0 || DateTime.Now > stock)
					{
						conDic[taskId + "Refresh"] = 1;
					}
					conDic[taskId] = num;
					if (conDic[taskId] == 0 && conDic[taskId + "Refresh"] == 1)
					{
						break;
					}
				}
			}
			await Task.Delay(500, token);
		}
	}

	[GeneratedRegex("wbi/", RegexOptions.IgnoreCase, "zh-CN")]
	[GeneratedCode("System.Text.RegularExpressions.Generator", "8.0.10.46610")]
	private static Regex MyRegex()
	{
		return _003CRegexGenerator_g_003EF096B0AF7CDBBC9952D9841FCC0106FE8A95A05E0F5D575C676BD2604719D3EFE__MyRegex_1.Instance;
	}
}
