using System;
using System.Data;
using API.BusService.System;
using API.Common;
using API.DataAccess.System;
using API.Models.Comm;
using Microsoft.AspNetCore.Mvc;

namespace API.Controllers.System;

public class SysDictionaryController : Controller
{
	[HttpPost]
	public Response PublicGetSysDictionaryList([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectSearch, "type");
			string jObject2 = Util.GetJObject(model.jObjectSearch, "search");
			string jObject3 = Util.GetJObject(model.jObjectSearch, "groupName");
			DataTable sysDictionaryList = SysDictionary.GetSysDictionaryList(jObject2, jObject, jObject3);
			response.data = Util.GetTableResponse(sysDictionaryList);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response GetSysDictionaryList([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectSearch, "search");
			string jObject2 = Util.GetJObject(model.jObjectSearch, "groupID");
			string jObject3 = Util.GetJObject(model.jObjectSearch, "groupName");
			string jObject4 = Util.GetJObject(model.jObjectSearch, "enable", "1");
			DataTable sysDictionaryList = SysDictionary.GetSysDictionaryList(jObject2, jObject3, jObject, jObject4, "1", BusSysUser.Instance.User.Rights, model.limit, model.offset, model.prop, model.order);
			response.data = Util.GetTableResponse(sysDictionaryList);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response DictionarySave([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			SysDictionary.DictionarySave(model.jObjectParam, BusSysUser.Instance.User.Name, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Rights, model.curTime);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response EnableDictionary([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			SysDictionary.EnableDictionary(model.jObjectParam, BusSysUser.Instance.User.Name, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Rights, model.curTime);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response DelDictionary([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "id");
			SysDictionary.DelDictionary(jObject, BusSysUser.Instance.User.Name, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Rights, model.curTime);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}
}
