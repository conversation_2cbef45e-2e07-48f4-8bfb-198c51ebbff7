using System;
using System.Data;
using API.Common;
using API.Models.Comm;
using Microsoft.Data.SqlClient;
using Newtonsoft.Json.Linq;

namespace API.DataAccess.System;

public class SysUserGroup
{
	public static void SetExpirationTime(string id, string expirationTime, int index, string curTime, User user)
	{
		SqlConnection sqlConnection = SQLHelper.LocalDB.InitCnn();
		SqlCommand sqlCommand = sqlConnection.CreateCommand();
		sqlCommand.Transaction = sqlConnection.BeginTransaction();
		try
		{
			long num = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
			string sSql = "SELECT Fid,FCookiesCount,FUserId,FOrganizationId FROM TSysUserGroup WHERE Fid=" + id;
			DataTable dataTable = SQLHelper.RunSqlDt(sSql, sqlCommand);
			if (dataTable.Rows.Count == 1)
			{
				DataCURD.Save(new JObject
				{
					["Fid"] = id,
					["FExpirationTime"] = expirationTime,
					["FSort"] = num
				}, "TSysUserGroup", "更新到期时间", "Fid", user.Id, user.Name, curTime, sqlCommand);
				string jObject = Util.GetJObject(dataTable.Rows[0], "FCookiesCount");
				string jObject2 = Util.GetJObject(dataTable.Rows[0], "FUserId");
				string jObject3 = Util.GetJObject(dataTable.Rows[0], "FOrganizationId");
				jObject = ((!(jObject != "0")) ? "" : (" TOP " + jObject));
				sSql = " SELECT STUFF((SELECT " + jObject + " ','+CAST(Fid AS NVARCHAR(100)) FROM TSysUserCookies WHERE FOrganizationId=2 AND FUserId IN( " + jObject2 + ") FOR XML PATH('')),1,1,'') ";
				string text = SQLHelper.RunSqlStr(sSql, sqlCommand);
				sSql = " UPDATE TSysUserCookies SET FDate='" + curTime + "' WHERE FUserId IN (" + jObject2 + ") AND FOrganizationId=2 AND Fid IN (" + text + ")";
				int num2 = SQLHelper.RunSqlText(sSql, sqlCommand);
				if (num2 <= index)
				{
					DataCURD.Save(new JObject
					{
						["FDate"] = curTime,
						["FExpirationTime"] = expirationTime
					}, "TSysUserCookies", "更新到期时间", "FDate", user.Id, user.Name, curTime, sqlCommand);
					DataCURD.Delete("TSysUserGroupCookiesCount", "删除算费", "FGroupId", id, user.Id, user.Name, curTime, sqlCommand);
					if (jObject3 != "")
					{
						sSql = " UPDATE TSysUser SET FBattery=" + num + " WHERE Fid IN (" + jObject2 + ")";
						SQLHelper.RunSqlText(sSql, sqlCommand);
						DataCURD.Save(new JObject
						{
							["FBattery"] = num,
							["FOrganizationId"] = jObject3
						}, "TSysUser", "更新分区信息", "FBattery", user.Id, user.Name, curTime, sqlCommand);
					}
					sqlCommand.Transaction.Commit();
					return;
				}
				throw new Exception("更新条数不正确！");
			}
			throw new Exception("数据不唯一！");
		}
		catch (Exception ex)
		{
			sqlCommand.Transaction.Rollback();
			throw new Exception(ex.Message);
		}
		finally
		{
			sqlConnection.Close();
			sqlConnection.Dispose();
		}
	}

	public static void SaveAreaCookie(string id, string userId, string key, string area, string curTime, User user)
	{
		SqlConnection sqlConnection = SQLHelper.LocalDB.InitCnn();
		SqlCommand sqlCommand = sqlConnection.CreateCommand();
		sqlCommand.Transaction = sqlConnection.BeginTransaction();
		try
		{
			string sSql = " SELECT COUNT(*) FROM TSysUserCookiesArea WHERE Fid!=" + id + " AND FKey='" + key + "' AND FAreaId=" + area;
			string text = SQLHelper.RunSqlStr(sSql, sqlCommand);
			if (text != "0")
			{
				throw new Exception("当前分区已存在！");
			}
			JObject jObject = ((!(id == "0")) ? new JObject
			{
				["Fid"] = id,
				["FAreaId"] = area
			} : new JObject
			{
				["Fid"] = id,
				["FKey"] = key,
				["FUserId"] = userId,
				["FAreaId"] = area,
				["FMileage"] = 0,
				["FAricles"] = 0,
				["FExpirationTime"] = curTime
			});
			DataCURD.Save(jObject, "TSysUserCookiesArea", "保存分区账号", "Fid", user.Id, user.Name, curTime, sqlCommand);
			sqlCommand.Transaction.Commit();
		}
		catch (Exception ex)
		{
			sqlCommand.Transaction.Rollback();
			throw new Exception(ex.Message);
		}
		finally
		{
			sqlConnection.Close();
			sqlConnection.Dispose();
		}
	}

	public static void DelAreaCookie(string id, string userId, string curTime, User user)
	{
		SqlConnection sqlConnection = SQLHelper.LocalDB.InitCnn();
		SqlCommand sqlCommand = sqlConnection.CreateCommand();
		sqlCommand.Transaction = sqlConnection.BeginTransaction();
		try
		{
			DataCURD.Delete("TSysUserCookiesArea", "删除分区账号", "Fid,FUserId", id + "," + userId, user.Id, user.Name, curTime, sqlCommand);
			sqlCommand.Transaction.Commit();
		}
		catch (Exception ex)
		{
			sqlCommand.Transaction.Rollback();
			throw new Exception(ex.Message);
		}
		finally
		{
			sqlConnection.Close();
			sqlConnection.Dispose();
		}
	}

	public static void SetOption(string id, string aricles, string mileage, string expirationTime, User user, string curTime)
	{
		SqlConnection sqlConnection = SQLHelper.LocalDB.InitCnn();
		SqlCommand sqlCommand = sqlConnection.CreateCommand();
		sqlCommand.Transaction = sqlConnection.BeginTransaction();
		try
		{
			JObject jObject = new JObject();
			jObject["Fid"] = id;
			if (aricles != "")
			{
				jObject["FAricles"] = aricles;
			}
			if (mileage != "")
			{
				jObject["FMileage"] = mileage;
			}
			if (expirationTime != "")
			{
				jObject["FExpirationTime"] = expirationTime;
			}
			id = DataCURD.Save(jObject, "TSysUserCookiesArea", "设置分区账号信息", "Fid", user.Id, user.Name, curTime, sqlCommand);
			if (expirationTime != "")
			{
				string sSql = " SELECT FKey FROM TSysUserCookiesArea WHERE Fid=" + id;
				string text = SQLHelper.RunSqlStr(sSql, sqlCommand);
				sSql = " SELECT MAX(FExpirationTime) FROM TSysUserCookiesArea WHERE FKey='" + text + "' ";
				expirationTime = SQLHelper.RunSqlStr(sSql, sqlCommand);
				DataCURD.Save(new JObject
				{
					["FKey"] = text,
					["FExpirationTime"] = expirationTime
				}, "TSysUserCookies", "更新时间", "FKey", user.Id, user.Name, curTime, sqlCommand);
			}
			sqlCommand.Transaction.Commit();
		}
		catch (Exception ex)
		{
			sqlCommand.Transaction.Rollback();
			throw new Exception(ex.Message);
		}
		finally
		{
			sqlConnection.Close();
			sqlConnection.Dispose();
		}
	}

	public static DataTable GetUserCookieAreaList(string key, string userId)
	{
		string text = " SELECT T2.FName AS FAreaName,T1.* FROM TSysUserCookiesArea T1 LEFT JOIN TSysOrganization T2 ON T2.Fid=T1.FAreaId WHERE 1=1 ";
		text = text + " AND T1.FUserId=" + userId + " AND T1.FKey='" + key + "'";
		return SQLHelper.LocalDB.RunSqlDt(text);
	}

	public static DataTable GetUserCookieList(string search, string userId, string id, int limit = 0, int offset = 0, string prop = "", string order = "")
	{
		string sSql;
		if (id != "" && userId != "")
		{
			sSql = " SELECT FExpirationTime,FPrice FROM TSysUserGroup WHERE Fid=" + id;
			DataTable dataTable = SQLHelper.LocalDB.RunSqlDt(sSql);
			sSql = " SELECT (SELECT COUNT(*) AS FCount FROM TSysUserCookies T1 WHERE T1.FUserId IN (" + userId + ") AND T1.FOrganizationId=2) AS FCount ";
			sSql = sSql + " ,ISNULL((SELECT SUM((FCurCount-FCount)*FPrice) FROM TSysUserGroupCookiesCount WHERE FGroupId=" + id + "),0) AS FMoney";
			sSql = sSql + " ,ISNULL((SELECT COUNT(*) FROM TSysUserCookies WHERE FExpirationTime<GETDATE() AND FUserId IN (" + userId + ") AND FOrganizationId=2),0) AS FExpirationCount";
			if (dataTable.Rows.Count == 1)
			{
				string jObject = Util.GetJObject(dataTable.Rows[0], "FPrice");
				string jObject2 = Util.GetJObject(dataTable.Rows[0], "FExpirationTime");
				sSql = sSql + " ,ISNULL((SELECT SUM(DATEDIFF(DAY,(CASE WHEN FExpirationTime>GETDATE() THEN FExpirationTime ELSE GETDATE()END) ,'" + jObject2 + "')* " + jObject + ") FROM TSysUserCookies ";
				sSql = sSql + " WHERE FExpirationTime<'" + jObject2 + "' AND FUserId IN (" + userId + ") AND FOrganizationId=2),0) AS FExpirationMoney";
			}
		}
		else
		{
			sSql = " SELECT T2.FUserCode,T1.* " + SQLHelper.total + " FROM TSysUserCookies T1 LEFT JOIN TSysUser T2 ON T2.Fid=T1.FUserId";
			sSql = sSql + " WHERE T1.FUserId IN (" + userId + ") AND T1.FOrganizationId=2";
			if (!string.IsNullOrEmpty(search))
			{
				sSql = sSql + " AND ( T1.FKey LIKE '%" + search + "%' OR T1.FIdentifying LIKE '%" + search + "%' )";
			}
			if (prop == "")
			{
				prop = "T1.FExpirationTime ASC,T2.FUserCode";
			}
		}
		return SQLHelper.LocalDB.RunSqlDt(sSql, limit, offset, prop, order);
	}

	public static string SaveSysUserGroup(JObject jObject, User user, string curTime)
	{
		SqlConnection sqlConnection = SQLHelper.LocalDB.InitCnn();
		SqlCommand sqlCommand = sqlConnection.CreateCommand();
		sqlCommand.Transaction = sqlConnection.BeginTransaction();
		try
		{
			string jObject2 = Util.GetJObject(jObject, "FName");
			string jObject3 = Util.GetJObject(jObject, "Fid", "0");
			string text = string.Join(',', Util.GetJObject<string[]>(jObject, "FUserId") ?? Array.Empty<string>());
			string text2 = string.Join(',', Util.GetJObject<string[]>(jObject, "FMenuId") ?? Array.Empty<string>());
			string text3 = string.Join(',', Util.GetJObject<string[]>(jObject, "FOrganizationId") ?? Array.Empty<string>());
			if (string.IsNullOrEmpty(jObject2))
			{
				throw new Exception("分组名称不能为空！");
			}
			if (text == "")
			{
				throw new Exception("分组用户不能为空！");
			}
			string sSql = " SELECT 1 FROM TSysUserGroup WHERE FName='" + jObject2 + "' AND Fid!=" + jObject3;
			string value = SQLHelper.LocalDB.RunSqlStr(sSql);
			if (!string.IsNullOrEmpty(value))
			{
				throw new Exception("分组名称已存在！");
			}
			jObject["FUserId"] = text;
			jObject["FMenuId"] = text2;
			jObject["FOrganizationId"] = text3;
			jObject3 = DataCURD.Save(jObject, "TSysUserGroup", "保存账号分组", "Fid", user.Id, user.Name, curTime, sqlCommand);
			sqlCommand.Transaction.Commit();
			return jObject3;
		}
		catch (Exception ex)
		{
			sqlCommand.Transaction.Rollback();
			throw new Exception(ex.Message);
		}
		finally
		{
			sqlConnection.Close();
			sqlConnection.Dispose();
		}
	}

	public static JArray GetSysUserGroupList(string search, string type)
	{
		string text = " SELECT 'group-' + CAST(T1.Fid AS NVARCHAR(100)) AS id,T1.FName AS label,T1.* FROM TSysUserGroup T1 WHERE 1=1 ";
		if (!string.IsNullOrEmpty(search))
		{
			text = text + " AND ( T1.FDesc LIKE '%" + search + "%' OR T1.FName LIKE '%" + search + "%' OR (SELECT COUNT(*) FROM TSysUser WHERE FUserCode LIKE '%" + search + "%' AND CHARINDEX(','+CAST(Fid AS NVARCHAR(100))+',',','+T1.FUserId+',')>0)>0)";
		}
		text += " ORDER BY T1.FSort DESC";
		DataTable o = SQLHelper.LocalDB.RunSqlDt(text);
		JArray jArray = JArray.FromObject(o);
		text = " SELECT T2.Fid AS id,T2.FUserCode AS label,COUNT(*) AS FCount,T2.Fid AS FUserId FROM TSysUserCookies T1 LEFT JOIN TSysUser T2 ON T2.Fid=T1.FUserId ";
		text += " WHERE T1.FOrganizationId=2";
		if (type == "已登录")
		{
			text += " AND T1.FIdentifying!=''";
		}
		text += " GROUP BY T2.Fid,T2.FUserCode";
		DataTable dataTable = SQLHelper.LocalDB.RunSqlDt(text);
		for (int i = 0; i < jArray.Count; i++)
		{
			JToken jToken = jArray[i];
			string jObject = Util.GetJObject(jToken, "FUserId", "0");
			DataRow[] array = dataTable.Select("id IN (" + jObject + ")");
			if (array != null && array.Length != 0)
			{
				jArray[i]["children"] = JArray.FromObject(array.CopyToDataTable());
			}
		}
		return jArray;
	}
}
