using System;
using System.Data;
using System.Threading.Tasks;
using API.BusService.System;
using API.Common;
using API.DataAccess.BiliBili;
using API.Models.Comm;
using Microsoft.AspNetCore.Mvc;
using Quartz;

namespace API.Controllers.BiliBili;

public class BiliQuartzGroupController(ISchedulerFactory schedulerFactory) : Controller()
{
	private readonly ISchedulerFactory _schedulerFactory = schedulerFactory;

	[HttpPost]
	public Response GetQuartzGroupList([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectSearch, "search");
			DataTable quartzGroupList = BiliQuartzGroup.GetQuartzGroupList("", jObject, BusSysUser.Instance.User.Id);
			response.data = Util.GetTableResponse(quartzGroupList);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response SaveQuartzGroup([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			BiliQuartzGroup.SaveQuartzGroup(model.jObjectParam, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime, null);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response DelQuartzGroup([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			BiliQuartzGroup.DelQuartzGroup(model.jObjectParam, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public async Task<Response> ShutdownGroup([FromBody] Request model)
	{
		Response mRet = new Response();
		try
		{
			await BiliQuartzGroup.ShutdownGroup(schedulerFactory, model.jObjectParam, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}

	[HttpPost]
	public async Task<Response> StartGroup([FromBody] Request model)
	{
		Response mRet = new Response();
		try
		{
			await BiliQuartzGroup.StartGroup(schedulerFactory, model.jObjectParam, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}
}
