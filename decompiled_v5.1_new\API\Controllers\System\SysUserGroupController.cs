using System;
using System.Data;
using API.Common;
using API.DataAccess.System;
using API.Models.Comm;
using Microsoft.AspNetCore.Mvc;

namespace API.Controllers.System;

public class SysUserGroupController : Controller
{
	[HttpPost]
	public Response SetExpirationTime([FromBody] Request model, User user)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "id");
			string jObject2 = Util.GetJObject(model.jObjectParam, "expirationTime");
			int index = int.Parse(Util.GetJObject(model.jObjectParam, "index"));
			SysUserGroup.SetExpirationTime(jObject, jObject2, index, model.curTime, user);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response SaveAreaCookie([FromBody] Request model, User user)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "id");
			string jObject2 = Util.GetJObject(model.jObjectParam, "userId");
			string jObject3 = Util.GetJObject(model.jObjectParam, "key");
			string jObject4 = Util.GetJObject(model.jObjectParam, "area");
			SysUserGroup.SaveAreaCookie(jObject, jObject2, jObject3, jObject4, model.curTime, user);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response DelAreaCookie([FromBody] Request model, User user)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "id");
			string jObject2 = Util.GetJObject(model.jObjectParam, "userId");
			SysUserGroup.DelAreaCookie(jObject, jObject2, model.curTime, user);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response SetOption([FromBody] Request model, User user)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "aricles");
			string jObject2 = Util.GetJObject(model.jObjectParam, "mileage");
			string jObject3 = Util.GetJObject(model.jObjectParam, "id");
			string jObject4 = Util.GetJObject(model.jObjectParam, "expirationTime");
			SysUserGroup.SetOption(jObject3, jObject, jObject2, jObject4, user, model.curTime);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response GetUserCookieAreaList([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectSearch, "userId", "0");
			string jObject2 = Util.GetJObject(model.jObjectSearch, "key");
			DataTable userCookieAreaList = SysUserGroup.GetUserCookieAreaList(jObject2, jObject);
			response.data = Util.GetTableResponse(userCookieAreaList);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response GetUserCookieList([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectSearch, "userId", "0");
			string jObject2 = Util.GetJObject(model.jObjectSearch, "search");
			string jObject3 = Util.GetJObject(model.jObjectSearch, "id");
			DataTable userCookieList = SysUserGroup.GetUserCookieList(jObject2, jObject, jObject3, model.limit, model.offset, model.prop, model.order);
			response.data = Util.GetTableResponse(userCookieList);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response GetSysUserGroupList([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectSearch, "search");
			string jObject2 = Util.GetJObject(model.jObjectSearch, "type");
			response.data = SysUserGroup.GetSysUserGroupList(jObject, jObject2);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response SaveSysUserGroup([FromBody] Request model, User user)
	{
		Response response = new Response();
		try
		{
			response.data = SysUserGroup.SaveSysUserGroup(model.jObjectParam, user, model.curTime);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response DelSysUserGroup([FromBody] Request model, User user)
	{
		Response response = new Response();
		try
		{
			DataCURD.Delete("TSysUserGroup", "删除分组", "Fid", Util.GetJObject(model.jObjectParam, "id"), user.Id, user.Name, model.curTime, SQLHelper.LocalDB.InitCnn());
			DataCURD.Delete("TSysUserGroupCookiesCount", "删除分组", "FGroupId", Util.GetJObject(model.jObjectParam, "id"), user.Id, user.Name, model.curTime, SQLHelper.LocalDB.InitCnn());
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}
}
