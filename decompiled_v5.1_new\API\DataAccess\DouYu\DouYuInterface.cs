using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using API.Common;
using Newtonsoft.Json.Linq;

namespace API.DataAccess.DouYu;

public class DouYuInterface
{
	public static object? GetInterfaceList(string name, string search, string field)
	{
		string sSql = " SELECT FResult FROM TInterface WHERE FName='" + name + "'";
		string text = SQLHelper.DouYuLocalDB.RunSqlStr(sSql);
		if (text.StartsWith('['))
		{
			IEnumerable<JToken> enumerable = JArray.Parse(text);
			if (search != "" && field != "")
			{
				enumerable = enumerable.Where((JToken token) => search.Split(',').Any((string s) => ((token[field] ?? ((JToken)"")).Value<string>() ?? "").Contains(s)));
			}
			return new
			{
				total = enumerable.Count(),
				rows = enumerable
			};
		}
		if (text.StartsWith('{'))
		{
			return JObject.Parse(text);
		}
		return null;
	}

	public static async Task UpdateInterface(string name, int userId, string userName, string curTime)
	{
		string sSql = " SELECT FUrl FROM TInterface WHERE FName='" + name + "'";
		string url = SQLHelper.DouYuLocalDB.RunSqlStr(sSql);
		if (url == "")
		{
			throw new Exception("无URL无法更新！");
		}
		HttpClientFactory httpClientFactory = new HttpClientFactory(name);
		JObject jToken = await httpClientFactory.Get(url);
		if (Util.GetJObject(jToken, "code") == "0")
		{
			DataCURD.Save(new JObject
			{
				["FUrl"] = url,
				["FResult"] = Util.GetJObject(Util.GetJObject<JToken>(jToken, "data"), "data")
			}, "TInterface", "更新" + name, "FUrl", userId, userName, curTime, SQLHelper.DouYuLocalDB.InitCnn());
			return;
		}
		throw new Exception(Util.GetJObject(jToken, "message"));
	}
}
