using System;
using Newtonsoft.Json.Linq;

namespace API.Models.Comm;

public class Request
{
	public JObject jObjectSearch { get; set; } = new JObject();

	public JObject jObjectParam { get; set; } = new JObject();

	public int page { get; set; }

	public int pageSize { get; set; }

	public int limit
	{
		get
		{
			if (page != 0)
			{
				return pageSize;
			}
			return 0;
		}
	}

	public int offset
	{
		get
		{
			if (page != 0)
			{
				return (page - 1) * pageSize;
			}
			return 0;
		}
	}

	public string prop { get; set; } = "";

	public string order { get; set; } = "";

	public string curTime { get; set; } = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss:fff");
}
