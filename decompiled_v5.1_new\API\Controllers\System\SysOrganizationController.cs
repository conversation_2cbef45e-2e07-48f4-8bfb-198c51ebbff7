using System;
using System.Data;
using API.BusService.System;
using API.Common;
using API.DataAccess.System;
using API.Models.Comm;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;

namespace API.Controllers.System;

public class SysOrganizationController : Controller
{
	[HttpPost]
	public Response PublicGetSysOrganizationList([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectSearch, "search");
			string jObject2 = Util.GetJObject(model.jObjectSearch, "enable", "1");
			DataTable sysOrganizationList = SysOrganization.GetSysOrganizationList(jObject, jObject2, model.limit, model.offset, model.prop, model.order);
			JArray sysOrganizationChild = BusSysOrganization.GetSysOrganizationChild(sysOrganizationList, 0);
			response.data = new
			{
				total = sysOrganizationChild.Count,
				rows = sysOrganizationChild
			};
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response GetSysOrganizationList([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectSearch, "search");
			string jObject2 = Util.GetJObject(model.jObjectSearch, "enable", "1");
			int jObject3 = Util.GetJObject<int>(model.jObjectSearch, "parentId");
			DataTable sysOrganizationList = SysOrganization.GetSysOrganizationList(jObject, jObject2, model.limit, model.offset, model.prop, model.order);
			JArray sysOrganizationChild = BusSysOrganization.GetSysOrganizationChild(sysOrganizationList, jObject3);
			response.data = new
			{
				total = sysOrganizationChild.Count,
				rows = sysOrganizationChild
			};
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}
}
