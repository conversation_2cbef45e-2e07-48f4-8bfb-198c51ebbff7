using System.Collections.Generic;
using System.Data;
using System.Linq;
using Newtonsoft.Json.Linq;

namespace API.BusService.System;

public class BusSysOrganization
{
	public static JArray GetSysOrganizationChild(DataTable dtAll, int parentId)
	{
		JArray jArray = new JArray();
		DataRow[] array = dtAll.Select("FParentId = " + parentId);
		int num = 0;
		while (array != null && num < array.Length)
		{
			DataTable dataTable = array.CopyToDataTable();
			int num2 = int.Parse(dataTable.Rows[num]["Fid"].ToString() ?? "-1");
			string text = dataTable.Rows[num]["FName"].ToString() ?? "";
			string text2 = dataTable.Rows[num]["FType"].ToString() ?? "";
			JObject jObject = new JObject
			{
				["value"] = num2,
				["label"] = text,
				["desc"] = text2
			};
			array = dtAll.Select("FParentId = " + parentId);
			if (array != null && array.Length != 0)
			{
				jObject["children"] = GetSysOrganizationChild2(dtAll, num2);
			}
			else
			{
				jObject["children"] = new JArray();
			}
			jArray.Add(jObject);
			num++;
		}
		return jArray;
	}

	public static JArray GetSysOrganizationChild2(DataTable dtAll, int parentId)
	{
		JArray jArray = new JArray();
		IEnumerable<DataRow> source = from Parent in dtAll.AsEnumerable()
			where Parent.Field<int>("FParentId") == parentId
			orderby Parent.Field<int>("FSort")
			select Parent;
		if (source.Any())
		{
			DataTable dataTable = source.CopyToDataTable();
			for (int num = 0; num < dataTable.Rows.Count; num++)
			{
				int num2 = int.Parse(dataTable.Rows[num]["Fid"].ToString() ?? "-1");
				string text = dataTable.Rows[num]["FName"].ToString() ?? "";
				string text2 = dataTable.Rows[num]["FType"].ToString() ?? "";
				string text3 = dataTable.Rows[num]["FAricles"].ToString() ?? "";
				string text4 = dataTable.Rows[num]["FMileage"].ToString() ?? "";
				JObject jObject = new JObject
				{
					["value"] = num2,
					["label"] = text,
					["desc"] = text2,
					["aricles"] = text3,
					["mileage"] = text4
				};
				source = from Parent in dtAll.AsEnumerable()
					where Parent.Field<int>("FParentId") == parentId
					orderby Parent.Field<int>("FSort")
					select Parent;
				if (source.Any())
				{
					jObject["children"] = GetSysOrganizationChild2(dtAll, num2);
				}
				jArray.Add(jObject);
			}
		}
		return jArray;
	}
}
