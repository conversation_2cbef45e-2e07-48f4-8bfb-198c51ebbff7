using System;
using System.Data;
using API.Common;
using Newtonsoft.Json.Linq;

namespace API.DataAccess.DouYu;

public class DouYuGeetest
{
	public static DataTable GetGeetestList(string search, int limit = 0, int offset = 0, string prop = "", string order = "")
	{
		string text = " SELECT CASE FMethod WHEN 1 THEN 'POST' ELSE 'GET' END AS FMethodName,*" + SQLHelper.total + " FROM TGeetest WHERE 1=1";
		if (search != "")
		{
			text = text + " AND ( FName LIKE '%" + search + "%' OR FUrl LIKE '%" + search + "%' )";
		}
		if (prop == "")
		{
			prop = "FEnable";
			order = "DESC";
		}
		return SQLHelper.DouYuLocalDB.RunSqlDt(text, limit, offset, prop, order);
	}

	public static void EnableGeetest(JObject jObject, int userId, string userName, string curTime)
	{
		string sSql = " UPDATE TGeetest SET FEnable=0";
		SQLHelper.DouYuLocalDB.RunSqlText(sSql);
		if (Util.GetJObject(jObject, "FEnable") == "1")
		{
			DataCURD.Save(jObject, "TGeetest", "启用禁用打码", "Fid", userId, userName, curTime, SQLHelper.DouYuLocalDB.InitCnn());
		}
	}

	public static JObject GetGeetestList()
	{
		DataTable geetestList = GetGeetestList("");
		if (geetestList.Rows.Count == 0)
		{
			throw new Exception("请配置打码信息");
		}
		JObject jObject = new JObject();
		for (int i = 0; i < geetestList.Columns.Count; i++)
		{
			jObject[geetestList.Columns[i].ColumnName] = geetestList.Rows[0][i].ToString();
		}
		return jObject;
	}
}
