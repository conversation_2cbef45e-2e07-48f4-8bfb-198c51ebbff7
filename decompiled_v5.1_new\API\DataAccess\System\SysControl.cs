using System.Data;
using API.Common;

namespace API.DataAccess.System;

public class SysControl
{
	public static DataTable GetSysControlByCatalog(string catalog)
	{
		string sSql = "SELECT FDescribe,FValue FROM TSysControl WHERE FCatalog='" + catalog + "'";
		return SQLHelper.LocalDB.RunSqlDt(sSql);
	}

	public static string GetSysControlValue(string catalog, string @default = "")
	{
		string sSql = " SELECT FValue FROM TSysControl WHERE FCatalog='" + catalog + "'";
		string text = SQLHelper.LocalDB.RunSqlStr(sSql);
		if (!(text == ""))
		{
			return text;
		}
		return @default;
	}
}
