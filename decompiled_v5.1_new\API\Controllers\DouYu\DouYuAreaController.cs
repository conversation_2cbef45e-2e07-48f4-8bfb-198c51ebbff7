using System;
using System.Data;
using System.Threading.Tasks;
using API.BusService.System;
using API.Common;
using API.DataAccess.DouYu;
using API.Models.Comm;
using Microsoft.AspNetCore.Mvc;

namespace API.Controllers.DouYu;

public class DouYuAreaController : Controller
{
	[HttpPost]
	public Response GetAreaList([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectSearch, "search");
			DataTable areaList = DouYuArea.GetAreaList("", BusSysUser.Instance.User.Organization.斗鱼, jObject, model.limit, model.offset, model.prop, model.order);
			response.data = Util.GetTableResponse(areaList);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response GetAreaActivityIdList([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectSearch, "areaId", "0");
			DataTable areaActivityIdList = DouYuArea.GetAreaActivityIdList(jObject);
			response.data = Util.GetTableResponse(areaActivityIdList);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public async Task<Response> SaveArea([FromBody] Request model)
	{
		Response mRet = new Response();
		try
		{
			await DouYuArea.SaveArea(model.jObjectParam, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}

	[HttpPost]
	public Response DelArea([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "id");
			DouYuArea.DelArea(jObject, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public async Task<Response> UpdateAreaInfo([FromBody] Request model)
	{
		Response mRet = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "Fid");
			string jObject2 = Util.GetJObject(model.jObjectParam, "FName");
			await DouYuArea.UpdateAreaInfo(jObject, jObject2, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}
}
