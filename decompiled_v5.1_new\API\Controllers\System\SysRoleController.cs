using System;
using System.Data;
using API.BusService.System;
using API.Common;
using API.DataAccess.System;
using API.Models.Comm;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;

namespace API.Controllers.System;

public class SysRoleController : Controller
{
	[HttpPost]
	public Response PublicGetSysRoleList([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectSearch, "search");
			string jObject2 = Util.GetJObject(model.jObjectSearch, "enable", "1");
			DataTable sysRoleList = SysRole.GetSysRoleList(jObject, jObject2, model.limit, model.offset, model.prop, model.order);
			response.data = Util.GetTableResponse(sysRoleList);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response GetSysRoleList([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectSearch, "search");
			string jObject2 = Util.GetJObject(model.jObjectSearch, "enable", "1");
			DataTable sysRoleList = SysRole.GetSysRoleList(jObject, jObject2, model.limit, model.offset, model.prop, model.order);
			response.data = Util.GetTableResponse(sysRoleList);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response EnableSysRole([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			SysRole.EnableSysRole(model.jObjectParam, BusSysUser.Instance.User.Name, BusSysUser.Instance.User.Id, model.curTime);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response SaveSysRole([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			SysRole.SaveSysRole(model.jObjectParam, BusSysUser.Instance.User.Name, BusSysUser.Instance.User.Id, model.curTime);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response DelSysRole([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "id");
			SysRole.DelSysRole(jObject, BusSysUser.Instance.User.Name, BusSysUser.Instance.User.Id, model.curTime);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response GetSysMenuTreeByRoleId([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectSearch, "id");
			JObject sysMenuTreeByRoleId = SysRole.GetSysMenuTreeByRoleId(jObject);
			response.data = sysMenuTreeByRoleId;
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response SaveRoleMenu([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			JArray jArray = (JArray)model.jObjectParam["list"];
			string menus = "";
			if (jArray != null)
			{
				string[] value = jArray.ToObject<string[]>() ?? Array.Empty<string>();
				menus = string.Join(",", value);
			}
			string jObject = Util.GetJObject(model.jObjectParam, "id");
			SysRole.SaveRoleMenu(jObject, menus, BusSysUser.Instance.User.Name, BusSysUser.Instance.User.Id, model.curTime);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response GetSysRoleUserList([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectSearch, "id");
			JObject sysRoleUserList = SysRole.GetSysRoleUserList(jObject);
			response.data = sysRoleUserList;
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response SaveSysRoleUser([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			JObject jObjectParam = model.jObjectParam;
			JArray jArray = (JArray)jObjectParam["list"];
			string users = "";
			if (jArray != null)
			{
				string[] value = jArray.ToObject<string[]>() ?? Array.Empty<string>();
				users = string.Join(",", value);
			}
			string jObject = Util.GetJObject(model.jObjectParam, "id");
			SysRole.SaveSysRoleUser(jObject, users, BusSysUser.Instance.User.Name, BusSysUser.Instance.User.Id, model.curTime);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}
}
