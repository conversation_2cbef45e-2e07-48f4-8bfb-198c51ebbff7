using System;
using System.Collections.Generic;
using System.Globalization;
using System.Net.WebSockets;
using System.Security.Cryptography;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Timers;
using API.Common;
using Newtonsoft.Json.Linq;

namespace API.BusService.DouYu;

public class BusDouYuUtil
{
	private static class TimeStamp
	{
		public static int Now()
		{
			return (int)DateTime.UtcNow.Subtract(new DateTime(1970, 1, 1)).TotalSeconds;
		}

		public static int FromTime(DateTime dateTime)
		{
			return (int)dateTime.Subtract(new DateTime(1970, 1, 1)).TotalSeconds;
		}

		public static DateTime GetDateTime(int Stamp)
		{
			return new DateTime(1970, 1, 1).AddSeconds(Stamp).ToLocalTime();
		}
	}

	private class DataConverter
	{
		public static byte[] Uint32ToBytes(uint[] Source, bool isLittleEndian = false)
		{
			int num = Source.Length * 4;
			byte[] array = new byte[num];
			int num2 = Source.Length;
			for (int i = 0; i < num2; i++)
			{
				int length = ((num - i * 4 < 4) ? 4 : (num % 4));
				byte[] bytes = BitConverter.GetBytes(Source[i]);
				if (BitConverter.IsLittleEndian != isLittleEndian)
				{
					Array.Reverse(bytes);
				}
				Array.Copy(bytes, 0, array, i * 4, length);
			}
			return array;
		}

		public static uint[] HexToUInt32(string hex, bool isLittleEndian = false)
		{
			StringBuilder stringBuilder = new StringBuilder(hex);
			int length = hex.Length;
			int num = ((length % 2 == 0) ? (length / 2) : (length / 2 + 1));
			int num2 = ((num % 4 == 0) ? (num / 4) : (num / 4 + 1));
			byte[] array = new byte[4];
			uint[] array2 = new uint[num2];
			for (int i = 0; i < length; i += 2)
			{
				int num3 = ((length - i == 1) ? 1 : 2);
				byte.TryParse(stringBuilder.ToString(i, num3), NumberStyles.HexNumber, null, out array[i % 8 / 2]);
				if (i > 0 && (i % 8 == 6 || length - i <= 2))
				{
					if (BitConverter.IsLittleEndian != isLittleEndian)
					{
						Array.Reverse(array);
					}
					array2[i / 8] = BitConverter.ToUInt32(array);
					Array.Clear(array, 0, 4);
				}
			}
			return array2;
		}

		public static string UInt32ToHex(uint[] SourceWordArray, bool isLittleEndian = false)
		{
			int num = SourceWordArray.Length * 4;
			StringBuilder stringBuilder = new StringBuilder();
			byte[] array = new byte[4];
			for (int i = 0; i < num; i++)
			{
				if (i % 4 == 0)
				{
					array = BitConverter.GetBytes(SourceWordArray[i / 4]);
					if (BitConverter.IsLittleEndian != isLittleEndian)
					{
						Array.Reverse(array);
					}
				}
				stringBuilder.Append(array[i % 4].ToString("x2"));
			}
			return stringBuilder.ToString();
		}

		public static uint[] LatinToUInt32(string sourceString, bool isLittleEndian = false)
		{
			int length = sourceString.Length;
			int num = length / 4;
			if (length % 4 != 0)
			{
				num++;
			}
			uint[] array = new uint[num];
			for (int i = 0; i < length; i++)
			{
				byte b = Encoding.Unicode.GetBytes(sourceString.Substring(i, 1))[0];
				array[i / 4] |= (uint)(b << (isLittleEndian ? (i % 4 * 8) : (24 - i % 4 * 8)));
			}
			return array;
		}

		public static string UInt32ToLatin(uint[] sourceArray, bool isLittleEndian)
		{
			StringBuilder stringBuilder = new StringBuilder();
			byte[] array = new byte[4];
			for (int i = 0; i < sourceArray.Length; i++)
			{
				if (i % 4 == 0)
				{
					array = BitConverter.GetBytes(sourceArray[i / 4]);
					if (BitConverter.IsLittleEndian != isLittleEndian)
					{
						Array.Reverse(array);
					}
				}
				stringBuilder.Append(Convert.ToChar(array[i % 4]));
			}
			return stringBuilder.ToString();
		}

		public static string Escape(string SourceString)
		{
			string text = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789@*_+-./";
			StringBuilder stringBuilder = new StringBuilder();
			char[] array = SourceString.ToCharArray();
			int num = 0;
			for (int i = 0; i < array.Length; i++)
			{
				if (text.Contains(array[i]))
				{
					stringBuilder.Append(array[i]);
					num++;
					continue;
				}
				int num2 = Convert.ToInt32(array[i]);
				if (num2 < 256)
				{
					StringBuilder stringBuilder2 = stringBuilder;
					StringBuilder.AppendInterpolatedStringHandler handler = new StringBuilder.AppendInterpolatedStringHandler(1, 1, stringBuilder2);
					handler.AppendLiteral("%");
					handler.AppendFormatted(num2, "X2");
					stringBuilder2.Append(ref handler);
					num += 3;
					continue;
				}
				for (int j = 0; j < 2; j++)
				{
					byte b = (byte)(num2 & 0xFF);
					num2 >>= 8;
					stringBuilder.Insert(num, b.ToString("X2"));
				}
				stringBuilder.Insert(num, "%u");
				num += 6;
			}
			return stringBuilder.ToString();
		}

		public static string Unescape(string SourceString)
		{
			int length = SourceString.Length;
			StringBuilder stringBuilder = new StringBuilder();
			if (SourceString.LastIndexOf('%') > length - 3 || SourceString.LastIndexOf("%u") > length - 6)
			{
				return SourceString;
			}
			int num = 0;
			while (num < length)
			{
				int result;
				if (SourceString.Substring(num, 1) != "%")
				{
					string value = SourceString.Substring(num, 1);
					stringBuilder.Append(value);
					num++;
				}
				else if (SourceString.Substring(num, 2) == "%u")
				{
					string value = SourceString.Substring(num + 2, 4);
					if (!int.TryParse(value, NumberStyles.HexNumber, null, out result))
					{
						return SourceString;
					}
					stringBuilder.Append(Convert.ToChar(result));
					num += 6;
				}
				else
				{
					string value = SourceString.Substring(num + 1, 2);
					if (!int.TryParse(value, NumberStyles.HexNumber, null, out result))
					{
						return SourceString;
					}
					stringBuilder.Append(Convert.ToChar(result));
					num += 3;
				}
			}
			return stringBuilder.ToString();
		}

		public static string EncodeURIComponent(string SourceString)
		{
			byte[] bytes = Encoding.UTF8.GetBytes(SourceString);
			StringBuilder stringBuilder = new StringBuilder();
			for (int i = 0; i < bytes.Length; i++)
			{
				if (bytes[i] == 33 || (bytes[i] > 38 && bytes[i] < 43) || (bytes[i] > 44 && bytes[i] < 47) || bytes[i] == 95 || bytes[i] == 126)
				{
					stringBuilder.Append(Convert.ToChar(bytes[i]));
					continue;
				}
				if (bytes[i] > 47 && bytes[i] < 58)
				{
					stringBuilder.Append(Convert.ToChar(bytes[i]));
					continue;
				}
				if (bytes[i] > 64 && bytes[i] < 91)
				{
					stringBuilder.Append(Convert.ToChar(bytes[i]));
					continue;
				}
				if (bytes[i] > 96 && bytes[i] < 123)
				{
					stringBuilder.Append(Convert.ToChar(bytes[i]));
					continue;
				}
				StringBuilder stringBuilder2 = stringBuilder;
				StringBuilder.AppendInterpolatedStringHandler handler = new StringBuilder.AppendInterpolatedStringHandler(1, 1, stringBuilder2);
				handler.AppendLiteral("%");
				handler.AppendFormatted(bytes[i], "X2");
				stringBuilder2.Append(ref handler);
			}
			return stringBuilder.ToString();
		}

		public static string DecodeURIComponent(string SourceString)
		{
			int num = 0;
			int length = SourceString.Length;
			List<byte> list = new List<byte>();
			if (SourceString.LastIndexOf('%') > length - 3)
			{
				return SourceString;
			}
			while (num < length)
			{
				if (SourceString.Substring(num, 1) == "%")
				{
					string s = SourceString.Substring(num + 1, 2);
					if (!byte.TryParse(s, NumberStyles.HexNumber, null, out var result))
					{
						return SourceString;
					}
					list.Add(result);
					num += 3;
				}
				else
				{
					string s = SourceString.Substring(num, 1);
					list.Add(Convert.ToByte(s.ToCharArray()[0]));
					num++;
				}
			}
			return Encoding.UTF8.GetString(list.ToArray());
		}
	}

	private class Crypto
	{
		private const uint Delta = 2654435769u;

		public static byte[] MD5Hash(byte[] data)
		{
			if (data != null)
			{
				return MD5.HashData(data);
			}
			return new byte[16];
		}

		public static uint[]? XTEA(uint[] v, uint[] key, int round)
		{
			if (v.Length == 2 && key.Length == 4)
			{
				uint[] v2 = v;
				if (round > 0)
				{
					EnXTEA(ref v2, key, Convert.ToUInt32(Math.Abs(round)));
				}
				else
				{
					DeXTEA(ref v2, key, Convert.ToUInt32(Math.Abs(round)));
				}
				return v2;
			}
			return null;
		}

		public static uint[]? TEA(uint[] v, uint[] key, bool encrytp)
		{
			if (v.Length == 2 && key.Length == 4)
			{
				uint[] v2 = v;
				if (encrytp)
				{
					EnTEA(ref v2, key);
				}
				else
				{
					DeTEA(ref v2, key);
				}
				return v2;
			}
			return null;
		}

		private static void DeXTEA(ref uint[] v, uint[] key, uint round = 32u)
		{
			if (v.Length == 2 && key.Length == 4)
			{
				uint num = v[0];
				uint num2 = v[1];
				uint num3 = 2654435769u * round;
				for (int i = 0; i < round; i++)
				{
					num2 -= (((num << 4) ^ (num >> 5)) + num) ^ (num3 + key[(num3 >> 11) & 3]);
					num3 -= 2654435769u;
					num -= (((num2 << 4) ^ (num2 >> 5)) + num2) ^ (num3 + key[num3 & 3]);
				}
				v[0] = num;
				v[1] = num2;
			}
		}

		private static void EnXTEA(ref uint[] v, uint[] key, uint round)
		{
			if (v.Length == 2 && key.Length == 4)
			{
				uint num = v[0];
				uint num2 = v[1];
				uint num3 = 0u;
				for (int i = 0; i < round; i++)
				{
					num += (((num2 << 4) ^ (num2 >> 5)) + num2) ^ (num3 + key[num3 & 3]);
					num3 += 2654435769u;
					num2 += (((num << 4) ^ (num >> 5)) + num) ^ (num3 + key[(num3 >> 11) & 3]);
				}
				v[0] = num;
				v[1] = num2;
			}
		}

		private static void EnTEA(ref uint[] v, uint[] key)
		{
			if (v.Length == 2 && key.Length == 4)
			{
				uint num = v[0];
				uint num2 = v[1];
				uint num3 = 0u;
				for (int i = 0; i < 32; i++)
				{
					num3 += 2654435769u;
					num += ((num2 << 4) + key[0]) ^ (num2 + num3) ^ ((num2 >> 5) + key[1]);
					num2 += ((num << 4) + key[2]) ^ (num + num3) ^ ((num >> 5) + key[3]);
				}
				v[0] = num;
				v[1] = num2;
			}
		}

		private static void DeTEA(ref uint[] v, uint[] key)
		{
			if (v.Length == 2 && key.Length == 4)
			{
				uint num = v[0];
				uint num2 = v[1];
				uint num3 = 3337565984u;
				for (int i = 0; i < 32; i++)
				{
					num2 -= ((num << 4) + key[2]) ^ (num + num3) ^ ((num >> 5) + key[3]);
					num -= ((num2 << 4) + key[0]) ^ (num2 + num3) ^ ((num2 >> 5) + key[3]);
					num3 -= 2654435769u;
				}
				v[0] = num;
				v[1] = num2;
			}
		}
	}

	private class DouyuSocketPack
	{
		public enum SenderType : short
		{
			Server = 690,
			Client = 689
		}

		private readonly string _body;

		private short _sender;

		public SenderType Sender
		{
			get
			{
				if (_sender == 690)
				{
					return SenderType.Server;
				}
				return SenderType.Client;
			}
			set
			{
				_sender = (short)Sender;
			}
		}

		public DouyuSocketPack(string Message)
		{
			_sender = 689;
			_body = ((Message.Substring(Message.Length - 1, 1) == "\0") ? Message : (Message + "\0"));
		}

		public DouyuSocketPack(byte[] Message)
		{
			_body = "\0";
			_sender = 689;
			if (Message.Length <= 9)
			{
				return;
			}
			byte[] array = new byte[4];
			Array.Copy(Message, 0, array, 0, 4);
			if (!BitConverter.IsLittleEndian)
			{
				Array.Reverse(array);
			}
			int num = BitConverter.ToInt32(array, 0);
			if (num == Message.Length)
			{
				array = new byte[2];
				Array.Copy(Message, 4, array, 0, 2);
				if (!BitConverter.IsLittleEndian)
				{
					Array.Reverse(array);
				}
				_sender = BitConverter.ToInt16(array, 0);
				array = new byte[num - 8];
				Array.Copy(Message, 8, array, 0, num - 8);
				_body = Encoding.UTF8.GetString(array);
			}
			else
			{
				if (num != Message.Length - 4)
				{
					return;
				}
				array = new byte[4];
				Array.Copy(Message, 4, array, 0, 4);
				if (!BitConverter.IsLittleEndian)
				{
					Array.Reverse(array);
				}
				if (num == BitConverter.ToInt32(array, 0))
				{
					array = new byte[2];
					Array.Copy(Message, 8, array, 0, 2);
					if (!BitConverter.IsLittleEndian)
					{
						Array.Reverse(array);
					}
					_sender = BitConverter.ToInt16(array, 0);
					array = new byte[num - 8];
					Array.Copy(Message, 12, array, 0, num - 8);
					_body = Encoding.UTF8.GetString(array);
				}
			}
		}

		public byte[] ToBytes()
		{
			int num = Encoding.UTF8.GetByteCount(_body) + 8;
			byte[] array = new byte[num + 4];
			byte[] bytes = BitConverter.GetBytes(num);
			if (!BitConverter.IsLittleEndian)
			{
				Array.Reverse(bytes);
			}
			bytes.CopyTo(array, 0);
			bytes.CopyTo(array, 4);
			bytes = BitConverter.GetBytes(_sender);
			if (!BitConverter.IsLittleEndian)
			{
				Array.Reverse(bytes);
			}
			bytes.CopyTo(array, 8);
			array[10] = 0;
			array[11] = 0;
			bytes = Encoding.UTF8.GetBytes(_body);
			bytes.CopyTo(array, 12);
			return array;
		}

		public static string EscapeSlashAt(string str)
		{
			return str.Replace("/", "@S").Replace("@", "@A");
		}

		public static string UnscapeSlashAt(string str)
		{
			return str.Replace("@S", "/").Replace("@A", "@");
		}
	}

	public class DouyuLiveChat
	{
		private enum ConnectionStatus
		{
			None,
			Connecting,
			Connected,
			Logging,
			LoggedIn,
			OK,
			Closing
		}

		private readonly string vdwdae325w_64we = "2201" + DateTime.Now.ToString("yyyyMMdd");

		private string wsProxyServer = "";

		private readonly ClientWebSocket wsWSProxy = new ClientWebSocket();

		private string wsProxyTimeSign = "";

		private int wsProxyTimeStamp;

		private ConnectionStatus wsProxyStats;

		private CancellationTokenSource canceltoken = new CancellationTokenSource();

		private readonly global::System.Timers.Timer timer = new global::System.Timers.Timer(1000.0);

		private readonly List<Task> wsTasks = new List<Task>();

		private readonly string roomId;

		private readonly string username;

		private readonly string ltkid;

		private readonly string stk;

		private readonly string jwt;

		private readonly string devid;

		private readonly string uid;

		private readonly HttpClientFactory httpClientFactory;

		public DouyuLiveChat(string roomId, string username, string ltkid, string stk, string devid, string jwt, string uid, HttpClientFactory httpClientFactory)
		{
			this.username = username;
			this.ltkid = ltkid;
			this.stk = stk;
			this.jwt = jwt;
			this.devid = devid;
			this.uid = uid;
			this.roomId = roomId;
			this.httpClientFactory = httpClientFactory;
			wsTasks.Add(WsProxyLogin());
			timer.Elapsed += OnTimedEvent;
			timer.Start();
		}

		public async Task SendBarrage(string message)
		{
			long value = new DateTimeOffset(DateTime.UtcNow).ToUnixTimeMilliseconds();
			string message2 = $"pe@=0/content@={message}/col@=0/type@=chatmessage/dy@={devid}/sender@={uid}/ifs@=0/nc@=0/dat@=0/rev@=0/tts@=0/admzq@=0/cst@={value}/\0";
			DouyuSocketPack douyuSocketPack = new DouyuSocketPack(message2);
			await WsSend(wsWSProxy, douyuSocketPack.ToBytes());
		}

		public void Dispose()
		{
			Disconnect();
			wsWSProxy?.Dispose();
		}

		private void Connect()
		{
			Disconnect();
			wsProxyTimeSign = "";
			wsProxyTimeStamp = 0;
			canceltoken = new CancellationTokenSource();
			wsTasks.Add(WsProxyLogin());
			timer.Start();
		}

		private async Task WsProxyLogin()
		{
			wsWSProxy.Options.AddSubProtocol("-");
			wsProxyStats = ConnectionStatus.Connecting;
			JObject jObject = await httpClientFactory.Get("https://www.douyu.com/lapi/live/gateway/web/" + roomId + "?isH5=1");
			if (jObject.ContainsKey("error") && Util.GetJObject(jObject, "error") == "0")
			{
				JArray jObject2 = Util.GetJObject<JArray>(jObject["data"], "wss");
				if (jObject2 != null && jObject2.Count > 0)
				{
					int count = jObject2.Count;
					int index = new Random().Next(0, count);
					wsProxyServer = $"wss://{jObject2[index]["domain"]}:{jObject2[index]["port"]}";
				}
			}
			Uri uri = new Uri(wsProxyServer);
			await wsWSProxy.ConnectAsync(uri, canceltoken.Token);
			wsProxyStats = ConnectionStatus.Logging;
			int num = TimeStamp.Now();
			string message = $"type@=loginreq/roomid@={roomId}/dfl@=sn@AA=105@ASss@AA=1/username@={username}/password@=/ltkid@={ltkid}/biz@=1/stk@={stk}/devid@={devid}/ct@=0/pt@=2/cvr@=0/tvr@=7/apd@=/jwt@={jwt}/rt@={num}/vk@={WsProxyLoginSign(num)}/ver@=20220825/aver@=218101901/dmbt@=chrome/dmbv@=126/er@=1/\0";
			DouyuSocketPack douyuSocketPack = new DouyuSocketPack(message);
			await WsSend(wsWSProxy, douyuSocketPack.ToBytes());
			await WsProxyH5ckreq();
		}

		private async Task WsProxyKeepLive()
		{
			if (wsWSProxy != null && wsWSProxy.State == WebSocketState.Open)
			{
				int value = (wsProxyTimeStamp = TimeStamp.Now());
				string message = $"type@=keeplive/vbw@=0/cdn@=/tick@={value}/kd@={wsProxyTimeSign}/\0";
				DouyuSocketPack douyuSocketPack = new DouyuSocketPack(message);
				await WsSend(wsWSProxy, douyuSocketPack.ToBytes());
			}
		}

		private async Task WsProxyH5ckreq()
		{
			if (wsWSProxy != null && wsWSProxy.State == WebSocketState.Open)
			{
				string message = $"type@=h5ckreq/rid@={roomId}/ti@={vdwdae325w_64we}/\0";
				DouyuSocketPack douyuSocketPack = new DouyuSocketPack(message);
				await WsSend(wsWSProxy, douyuSocketPack.ToBytes());
			}
		}

		private async Task WsSend(ClientWebSocket ws, byte[] buffer)
		{
			if (!canceltoken.Token.IsCancellationRequested && ws != null && ws.State == WebSocketState.Open)
			{
				await ws.SendAsync(buffer, WebSocketMessageType.Binary, endOfMessage: true, canceltoken.Token);
			}
		}

		private void Disconnect()
		{
			try
			{
				wsProxyStats = ConnectionStatus.Closing;
				timer?.Stop();
				canceltoken?.Cancel();
				wsWSProxy?.Abort();
				wsTasks.Clear();
				canceltoken?.Dispose();
				canceltoken = new CancellationTokenSource();
			}
			catch (WebSocketException ex)
			{
				Console.WriteLine(ex.ToString());
			}
		}

		private string WsProxyLoginSign(int timestamp)
		{
			StringBuilder stringBuilder = new StringBuilder();
			stringBuilder.Append(timestamp);
			stringBuilder.Append("r5*^5;}2#${XF[h+;'./.Q'1;,-]f'p[");
			stringBuilder.Append(devid);
			byte[] bytes = Encoding.Latin1.GetBytes(stringBuilder.ToString());
			byte[] array = Crypto.MD5Hash(bytes);
			stringBuilder.Clear();
			for (int i = 0; i < array.Length; i++)
			{
				stringBuilder.Append(array[i].ToString("x2"));
			}
			return stringBuilder.ToString();
		}

		private void OnTimedEvent(object? source, ElapsedEventArgs e)
		{
			int num = TimeStamp.Now();
			wsTasks?.RemoveAll((Task x) => x.IsCompleted && x.CreationOptions != TaskCreationOptions.LongRunning);
			if (wsWSProxy != null && wsWSProxy.State == WebSocketState.Open && num - wsProxyTimeStamp >= 40)
			{
				wsTasks?.Add(WsProxyKeepLive());
			}
			ClientWebSocket clientWebSocket = wsWSProxy;
			if ((clientWebSocket == null || clientWebSocket.State != WebSocketState.Open) && wsProxyStats == ConnectionStatus.OK)
			{
				Connect();
			}
		}
	}
}
