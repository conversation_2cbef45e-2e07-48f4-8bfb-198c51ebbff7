using System;
using System.Linq;
using System.Net;
using System.Web;
using API.BusService.System;
using API.Common;
using API.Models.Comm;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Caching.Memory;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace API.CommClass;

public class SecurityFilter : ActionFilterAttribute, IActionFilter, IFilterMetadata
{
	private readonly MemoryCache memoryCache = new MemoryCache(new MemoryCacheOptions());

	public override void OnActionExecuting(ActionExecutingContext actionExecutingContext)
	{
		Response response = new Response();
		HttpRequest request = actionExecutingContext.HttpContext.Request;
		HttpResponse response2 = actionExecutingContext.HttpContext.Response;
		string text = string.Empty;
		string timestamp = string.Empty;
		string nonce = string.Empty;
		string signature = string.Empty;
		string text2 = string.Empty;
		string text3 = string.Empty;
		string val = AppSettings.GetVal("TokenPrefix");
		actionExecutingContext.HttpContext.Response.Headers.Append("Access-Control-Allow-Origin", "*");
		try
		{
			if (actionExecutingContext.ActionDescriptor is ControllerActionDescriptor controllerActionDescriptor)
			{
				text2 = controllerActionDescriptor.ControllerName;
				text3 = controllerActionDescriptor.ActionName;
				if (text2 == "Login")
				{
					actionExecutingContext.ActionArguments.Add("ipAddress", GetIpAddress(actionExecutingContext));
					base.OnActionExecuting(actionExecutingContext);
					return;
				}
			}
			if (request.Headers.Authorization.FirstOrDefault() != null)
			{
				text = HttpUtility.UrlDecode(request.Headers.Authorization.FirstOrDefault()) ?? "";
			}
			if (request.Headers["timestamp"].FirstOrDefault() != null)
			{
				timestamp = HttpUtility.UrlDecode(request.Headers["timestamp"].FirstOrDefault()) ?? "";
			}
			if (request.Headers["nonce"].FirstOrDefault() != null)
			{
				nonce = HttpUtility.UrlDecode(request.Headers["nonce"].FirstOrDefault()) ?? "";
			}
			if (request.Headers["signature"].FirstOrDefault() != null)
			{
				signature = HttpUtility.UrlDecode(request.Headers["signature"].FirstOrDefault()) ?? "";
			}
			string text4;
			if (actionExecutingContext.ActionArguments.Count == 0 || !actionExecutingContext.ActionArguments.TryGetValue("model", out object value))
			{
				text4 = "{}";
			}
			else
			{
				JsonSerializerSettings settings = new JsonSerializerSettings
				{
					DateFormatString = "yyyy-MM-dd HH:mm:ss"
				};
				text4 = JsonConvert.SerializeObject(value, settings);
			}
			if (text == "")
			{
				throw new Exception("非法操作，请重新登录");
			}
			string token = text;
			if (!Token.DoValidateSign(timestamp, text, nonce, signature, text4))
			{
				throw new Exception("非法操作，请重新登录");
			}
			if (!Token.DoValidateNonce(text, nonce, memoryCache))
			{
				throw new Exception("重复提交，请重新登录");
			}
			if (!Token.DoValidateEnable(token, val))
			{
				throw new Exception("登录过期，请重新登录");
			}
			if ((!(text2 == "Common") || !(text3 == "Upload")) && text2 == "Common")
			{
				JObject jObject = JObject.Parse(text4);
				string text5 = ((jObject["jObjectParam"] ?? throw new Exception("非法操作，请重新登录"))["userId"] ?? throw new Exception("非法操作，请重新登录")).ToString();
				if (Token.GetUserId(token, val) != text5)
				{
					throw new Exception("帐户错误，请重新登录");
				}
				BusSysUser.Instance.User = new User
				{
					Id = int.Parse(text5),
					Name = Util.GetJObject(jObject, "userName", "匿名")
				};
				actionExecutingContext.ActionArguments["user"] = BusSysUser.Instance.User;
				base.OnActionExecuting(actionExecutingContext);
				return;
			}
			string text6 = request.HttpContext.Session.GetString("userId");
			if (Token.GetUserId(token, val) != text6)
			{
				throw new Exception("帐户错误，请重新登录");
			}
			User user = new User
			{
				Id = int.Parse(text6),
				Name = (request.HttpContext.Session.GetString("userName") ?? throw new Exception("帐户错误，请重新登录")),
				Rights = "," + request.HttpContext.Session.GetString("rights") + ",",
				Organization = (JObject.Parse(request.HttpContext.Session.GetString("organization") ?? throw new Exception("帐户错误，请重新登录")).ToObject<Organization>() ?? throw new Exception("帐户错误，请重新登录")),
				Address = (request.HttpContext.Session.GetString("address") ?? throw new Exception("当前登录已失效，请重新登录")),
				Cookie = (request.HttpContext.Session.GetString("cookie") ?? throw new Exception("帐户错误，请重新登录"))
			};
			actionExecutingContext.ActionArguments["user"] = user;
			BusSysUser.Instance.User = user;
		}
		catch (Exception ex)
		{
			response2.StatusCode = 401;
			response.code = 401;
			response.message = ex.Message;
			actionExecutingContext.Result = new JsonResult(response);
		}
		base.OnActionExecuting(actionExecutingContext);
	}

	private static string GetIpAddress(ActionExecutingContext actionExecutingContext)
	{
		string text = actionExecutingContext.HttpContext.Connection.RemoteIpAddress?.ToString();
		if (IPAddress.TryParse(text, out IPAddress address) && !IPAddress.IsLoopback(address))
		{
			return text;
		}
		text = actionExecutingContext.HttpContext.Request.Headers["X-Forwarded-For"].FirstOrDefault();
		if (IPAddress.TryParse(text, out address) && !IPAddress.IsLoopback(address))
		{
			return text;
		}
		text = actionExecutingContext.HttpContext.Request.Headers["X-Real-IP"].FirstOrDefault();
		if (IPAddress.TryParse(text, out address) && !IPAddress.IsLoopback(address))
		{
			return text;
		}
		return "";
	}
}
