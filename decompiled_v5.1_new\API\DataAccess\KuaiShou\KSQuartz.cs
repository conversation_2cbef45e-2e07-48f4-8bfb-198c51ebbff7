using System;
using System.Data;
using System.Threading.Tasks;
using API.Common;
using API.Quartz;
using Microsoft.Data.SqlClient;
using Newtonsoft.Json.Linq;
using Quartz;

namespace API.DataAccess.KuaiShou;

public class KSQuartz
{
	public static DataTable GetQuartzList(string id, string search, int userId, int limit = 0, int offset = 0, string prop = "", string order = "")
	{
		string text = " SELECT '' AS FStatus,CASE T1.FSeconds WHEN 0 THEN '无限' ELSE CAST(T1.FSeconds AS NVARCHAR(100))+'秒' END AS FSecondsName";
		text += " ,T1.Fid,T1.FExecTime,T1.FGroupId,T1.FDifference,T1.FSeconds,T1.FJobName,T1.FCookieId,T1.FParam,T1.FEnable";
		text += " ,CASE FDifference WHEN 0 THEN '' ELSE CAST(T1.FDifference AS NVARCHAR(100))+'秒' END AS FDifferenceName";
		text += " ,STUFF((SELECT ','+ CAST(B1.FName AS VARCHAR(100)) FROM TCookies B1 WHERE CHARINDEX(','+CAST(B1.Fid AS NVARCHAR(100))+',',','+T1.FCookieId+',')>0 FOR XML PATH('')),1,1,'') AS FCookieName";
		text = text + SQLHelper.total + " FROM TQuartz T1";
		text = text + " WHERE T1.FUserId=" + userId;
		if (id != "")
		{
			text = text + " AND T1.Fid=" + id;
		}
		if (search != "")
		{
			text = text + " AND T1.FJobName LIKE '%" + search + "%'";
		}
		if (prop == "")
		{
			prop = "T1.FEnable DESC, CASE WHEN T1.FExecTime > CAST(GETDATE() AS TIME) THEN 1 ELSE 0 END DESC,T1.FExecTime ";
			order = "ASC";
		}
		return SQLHelper.KSLocalDB.RunSqlDt(text, limit, offset, prop, order);
	}

	public static DataTable GetQuartzJobList()
	{
		JArray jArray = new JArray
		{
			new JObject
			{
				["Fid"] = "正常领取奖励",
				["FName"] = "正常领取奖励"
			},
			new JObject
			{
				["Fid"] = "定时投稿",
				["FName"] = "定时投稿"
			}
		};
		return jArray.ToObject<DataTable>() ?? new DataTable();
	}

	public static async Task SaveQuartz(ISchedulerFactory schedulerFactory, JObject jObject, int userId, string userName, string curTime)
	{
		SqlConnection conn = SQLHelper.KSLocalDB.InitCnn();
		SqlCommand pCmd = conn.CreateCommand();
		pCmd.Transaction = conn.BeginTransaction();
		try
		{
			IScheduler scheduler = await schedulerFactory.GetScheduler();
			string oldJobName = Util.GetJObject(jObject, "FJobName");
			jObject["FUserId"] = userId;
			jObject["FEnable"] = 1;
			string id = DataCURD.Save(jObject, "TQuartz", "保存定时任务", "Fid", userId, userName, curTime, pCmd);
			string jObject2 = Util.GetJObject(jObject, "FExecTime");
			int jObject3 = Util.GetJObject<int>(jObject, "FDifference");
			int seconds = Util.GetJObject<int>(jObject, "FSeconds");
			string jobName = Util.GetJObject(jObject, "FJobName");
			string jObject4 = Util.GetJObject(jObject, "FCookieId");
			string jObject5 = Util.GetJObject(jObject, "FParam");
			DateTime dateTime = DateTime.ParseExact(jObject2, "HH:mm:ss", null);
			string cronExpression = dateTime.Second + " " + dateTime.Minute + " " + dateTime.Hour + " ? * *";
			JObject jObjectQuartz = new JObject
			{
				["cookieId"] = jObject4,
				["param"] = jObject5,
				["difference"] = jObject3
			};
			if (await scheduler.CheckExists(new JobKey(oldJobName, "定时任务-快手-" + id)))
			{
				if (await QzUtil.ExecutingJob(scheduler, "定时任务-快手-" + id, oldJobName))
				{
					throw new Exception("任务正在执行，请先停止任务！");
				}
				if (!(await scheduler.DeleteJob(new JobKey(oldJobName, "定时任务-快手-" + id))))
				{
					throw new Exception("当前任务无法编辑，请稍后再试！");
				}
			}
			await QzUtil.ScheduleJobPlus<QzKuaiShou>(scheduler, "定时任务-快手-" + id, jobName, cronExpression, seconds, jObjectQuartz, userId);
			pCmd.Transaction.Commit();
		}
		catch (Exception ex)
		{
			pCmd.Transaction.Rollback();
			throw new Exception(ex.Message);
		}
		finally
		{
			conn.Close();
			conn.Dispose();
		}
	}

	public static async Task DelQuartz(ISchedulerFactory schedulerFactory, JObject jObject, int userId, string userName, string curTime)
	{
		SqlConnection conn = SQLHelper.KSLocalDB.InitCnn();
		SqlCommand pCmd = conn.CreateCommand();
		pCmd.Transaction = conn.BeginTransaction();
		try
		{
			IScheduler scheduler = await schedulerFactory.GetScheduler();
			string id = Util.GetJObject(jObject, "Fid");
			string jobName = Util.GetJObject(jObject, "FJobName");
			DataCURD.Delete("TQuartz", "删除定时任务", "Fid", id, userId, userName, curTime, pCmd);
			if (await scheduler.CheckExists(new JobKey(jobName, "定时任务-快手-" + id)))
			{
				if (await QzUtil.ExecutingJob(scheduler, "定时任务-快手-" + id, jobName))
				{
					throw new Exception("任务正在执行，请先停止任务！");
				}
				if (!(await scheduler.DeleteJob(new JobKey(jobName, "定时任务-快手-" + id))))
				{
					throw new Exception("当前任务无法删除，请稍后再试！");
				}
			}
			pCmd.Transaction.Commit();
		}
		catch (Exception ex)
		{
			pCmd.Transaction.Rollback();
			throw new Exception(ex.Message);
		}
		finally
		{
			conn.Close();
			conn.Dispose();
		}
	}

	public static async Task<int> EnableQuartz(ISchedulerFactory schedulerFactory, JObject jObject, int userId, string userName, string curTime)
	{
		string id = Util.GetJObject(jObject, "Fid");
		string enable = Util.GetJObject(jObject, "FEnable");
		DataTable dt = GetQuartzList(id, "", userId);
		if (dt.Rows.Count == 0)
		{
			throw new Exception("当前数据不正确！");
		}
		bool groupStatus = true;
		IScheduler scheduler = await schedulerFactory.GetScheduler();
		string jobName = dt.Rows[0]["FJobName"].ToString() ?? "";
		bool flag = await scheduler.CheckExists(new JobKey(jobName, "定时任务-快手-" + id));
		if (groupStatus)
		{
			if (enable == "1")
			{
				if (!flag)
				{
					string s = dt.Rows[0]["FExecTime"].ToString() ?? "";
					int num = int.Parse(dt.Rows[0]["FDifference"].ToString() ?? "0");
					int seconds = int.Parse(dt.Rows[0]["FSeconds"].ToString() ?? "0");
					string text = dt.Rows[0]["FCookieId"].ToString() ?? "";
					string text2 = dt.Rows[0]["FParam"].ToString() ?? "";
					DateTime dateTime = DateTime.ParseExact(s, "HH:mm:ss", null);
					string cronExpression = dateTime.Second + " " + dateTime.Minute + " " + dateTime.Hour + " ? * *";
					JObject jObject2 = new JObject
					{
						["cookieId"] = text,
						["param"] = text2,
						["difference"] = num
					};
					await QzUtil.ScheduleJobPlus<QzKuaiShou>(scheduler, "定时任务-快手-" + id, jobName, cronExpression, seconds, jObject2, userId);
				}
			}
			else if (flag)
			{
				if (await QzUtil.ExecutingJob(scheduler, "定时任务-快手-" + id, jobName))
				{
					throw new Exception("任务正在执行，请先停止任务！");
				}
				if (!(await scheduler.DeleteJob(new JobKey(jobName, "定时任务-快手-" + id))))
				{
					throw new Exception("当前任务无法停用，请稍后再试！");
				}
			}
		}
		DataCURD.Save(new JObject
		{
			["Fid"] = id,
			["FEnable"] = enable
		}, "TQuartz", ((enable == "1") ? "启用" : "停用") + "定时任务", "Fid", userId, userName, curTime, SQLHelper.KSLocalDB.InitCnn());
		return int.Parse(enable);
	}

	public static DataTable GetQuartzExecList(int userId)
	{
		string text = " SELECT T1.FJobName";
		text += " ,T1.Fid,T1.FExecTime,T1.FDifference,T1.FSeconds,T1.FCookieId,T1.FParam,T1.FEnable";
		text += " FROM TQuartz T1";
		text = text + " WHERE T1.FEnable=1 AND T1.FUserId=" + userId;
		text += " ORDER BY FExecTime";
		return SQLHelper.KSLocalDB.RunSqlDt(text);
	}
}
