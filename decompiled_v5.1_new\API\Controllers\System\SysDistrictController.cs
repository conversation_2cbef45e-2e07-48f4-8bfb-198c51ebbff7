using System;
using System.Data;
using API.Common;
using API.DataAccess.System;
using API.Models.Comm;
using Microsoft.AspNetCore.Mvc;

namespace API.Controllers.System;

public class SysDistrictController : Controller
{
	[HttpPost]
	public Response PublicGetSysDistrictList([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectSearch, "type");
			string jObject2 = Util.GetJObject(model.jObjectSearch, "parentCode");
			DataTable sysDistrictList = SysDistrict.GetSysDistrictList(jObject, jObject2);
			response.data = Util.GetTableResponse(sysDistrictList);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}
}
