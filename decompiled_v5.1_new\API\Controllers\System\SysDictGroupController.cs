using System;
using System.Data;
using API.BusService.System;
using API.Common;
using API.DataAccess.System;
using API.Models.Comm;
using Microsoft.AspNetCore.Mvc;

namespace API.Controllers.System;

public class SysDictGroupController : Controller
{
	[HttpPost]
	public Response GetSysDictGroupList([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectSearch, "search");
			string jObject2 = Util.GetJObject(model.jObjectSearch, "enable", "1");
			string jObject3 = Util.GetJObject(model.jObjectSearch, "isSys", "1");
			DataTable sysDictGroupList = SysDictGroup.GetSysDictGroupList(jObject, jObject2, jObject3, BusSysUser.Instance.User.Rights, model.limit, model.offset, model.prop, model.order);
			response.data = Util.GetTableResponse(sysDictGroupList);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response DictGroupSave([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			SysDictGroup.DoDictGroupSave(model.jObjectParam, BusSysUser.Instance.User.Name, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Rights, model.curTime);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response DelDictGroup([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "id");
			response.message = SysDictGroup.DoDelDictGroup(jObject, BusSysUser.Instance.User.Name, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Rights, model.curTime);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}
}
