using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using API.BusService.BiliBili;
using API.BusService.DouYu;
using API.BusService.KuaiShou;
using API.Common;
using API.Models.Comm;
using Newtonsoft.Json.Linq;
using Quartz;
using Quartz.Impl.Matchers;
using Quartz.Impl.Triggers;

namespace API.Quartz;

public class QzUtil
{
	public static async Task Delay(int minValue = 2500, int maxValue = 5000)
	{
		await Task.Delay(new Random().Next(minValue, maxValue));
	}

	public static async Task<string> Validate(int userId, string ipAddress, string organizationName, string areaId = "0")
	{
		try
		{
			JObject jObjectParam = new JObject
			{
				["userId"] = userId,
				["ipAddress"] = ipAddress,
				["organizationName"] = organizationName,
				["areaId"] = areaId
			};
			Request model = new Request
			{
				jObjectParam = jObjectParam
			};
			return (await Util.Request("/Common/ValidateCookie", model))?.ToString() ?? "";
		}
		catch (Exception ex)
		{
			throw new Exception(ex.Message);
		}
	}

	public static async Task<string> Validate2(int userId, string ipAddress, string identifying)
	{
		try
		{
			JObject jObjectParam = new JObject
			{
				["userId"] = userId,
				["ipAddress"] = ipAddress,
				["identifying"] = identifying
			};
			Request model = new Request
			{
				jObjectParam = jObjectParam
			};
			return (await Util.Request("/Common/ValidateCookie2", model))?.ToString() ?? "";
		}
		catch (Exception ex)
		{
			throw new Exception(ex.Message);
		}
	}

	public static async Task<JArray> GetSysControlByCatalog(int userId, string catalog)
	{
		try
		{
			JObject jObjectParam = new JObject
			{
				["userId"] = userId,
				["catalog"] = catalog
			};
			Request model = new Request
			{
				jObjectParam = jObjectParam
			};
			object obj = await Util.Request("/Common/GetSysControlByCatalog", model);
			if (obj != null)
			{
				return JArray.FromObject(obj);
			}
			return new JArray();
		}
		catch (Exception ex)
		{
			throw new Exception(ex.Message);
		}
	}

	public static bool IsExecute(int seconds, IJobExecutionContext? context)
	{
		bool flag = true;
		bool flag2 = true;
		if (context != null)
		{
			flag = !context.CancellationToken.IsCancellationRequested;
		}
		if (seconds != 0 && context != null)
		{
			flag2 = DateTime.Now <= context.FireTimeUtc.ToLocalTime().AddSeconds(seconds);
		}
		return flag && flag2;
	}

	public static string WriteStatus(IJobExecutionContext context, int seconds)
	{
		if (!context.CancellationToken.IsCancellationRequested)
		{
			if (!(DateTime.Now >= context.FireTimeUtc.ToLocalTime().AddSeconds(seconds)) || seconds == 0)
			{
				return "已执行完毕！";
			}
			return "已到达设定时间！";
		}
		return "已取消！";
	}

	public static async Task ScheduleJob<T>(IScheduler scheduler, string jobGroup, string jobName, string cronExpression, int seconds, JObject @params, int userId) where T : IJob
	{
		if (!(await scheduler.CheckExists(new JobKey(jobName, jobGroup))))
		{
			TriggerBuilder triggerBuilder = TriggerBuilder.Create();
			ITrigger trigger = ((!(cronExpression == "")) ? triggerBuilder.WithCronSchedule(cronExpression).Build() : triggerBuilder.StartNow().Build());
			IJobDetail jobDetail = JobBuilder.Create<T>().WithIdentity(jobName, jobGroup).Build();
			jobDetail.JobDataMap.Add("jobGroup", jobGroup);
			jobDetail.JobDataMap.Add("jobName", jobName);
			jobDetail.JobDataMap.Add("seconds", seconds);
			jobDetail.JobDataMap.Add("params", @params);
			jobDetail.JobDataMap.Add("userId", userId);
			await scheduler.ScheduleJob(jobDetail, trigger);
			return;
		}
		throw new Exception("当前任务已存在！");
	}

	public static async Task<bool> ScheduleJobPlus<T>(IScheduler scheduler, string jobGroup, string jobName, string cronExpression, int seconds, JObject @params, int userId)
	{
		if (!(await scheduler.CheckExists(new JobKey(jobName, jobGroup))))
		{
			TriggerBuilder triggerBuilder = TriggerBuilder.Create();
			ITrigger trigger = ((!(cronExpression == "")) ? triggerBuilder.WithCronSchedule(cronExpression).Build() : triggerBuilder.StartNow().Build());
			Type typeFromHandle = typeof(T);
			Type type = null;
			Type[] nestedTypes = typeFromHandle.GetNestedTypes(BindingFlags.Public);
			Type[] array = nestedTypes;
			foreach (Type type2 in array)
			{
				if (type2.Name == jobName)
				{
					type = type2;
					break;
				}
			}
			if (type == null)
			{
				Util.WriteLog(jobGroup.Split('-')[1], jobGroup.Split('-')[0], "加载任务", jobGroup.Split('-')[1] + " " + jobName + " 当前任务不存在！", ConsoleColor.Red);
				return false;
			}
			IJobDetail jobDetail = JobBuilder.Create(type).WithIdentity(jobName, jobGroup).Build();
			jobDetail.JobDataMap.Add("jobGroup", jobGroup);
			jobDetail.JobDataMap.Add("jobName", jobName);
			jobDetail.JobDataMap.Add("seconds", seconds);
			jobDetail.JobDataMap.Add("params", @params);
			jobDetail.JobDataMap.Add("userId", userId);
			await scheduler.ScheduleJob(jobDetail, trigger);
			return true;
		}
		throw new Exception("当前任务已存在！");
	}

	public static async Task<bool> ExecutingJob(IScheduler scheduler, string jobGroup, string jobName)
	{
		IEnumerable<IJobExecutionContext> source = (await scheduler.GetCurrentlyExecutingJobs()).Where((IJobExecutionContext x) => x.JobDetail.Key.Group == jobGroup && x.JobDetail.Key.Name == jobName);
		return source.Any();
	}

	public static async Task Uninstall(ISchedulerFactory schedulerFactory)
	{
		string val = AppSettings.GetVal("ServerUrl");
		if (!(val != ""))
		{
			return;
		}
		IScheduler scheduler = await schedulerFactory.GetScheduler();
		foreach (string group in await scheduler.GetJobGroupNames())
		{
			foreach (JobKey jobKey in await scheduler.GetJobKeys(GroupMatcher<JobKey>.GroupContains(group)))
			{
				await scheduler.DeleteJob(jobKey);
				foreach (TriggerKey item in await scheduler.GetTriggerKeys(GroupMatcher<TriggerKey>.GroupContains(group)))
				{
					await scheduler.UnscheduleJob(item);
				}
				Util.WriteLog(jobKey.Group.Split('-')[1], jobKey.Group.Split('-')[0], jobKey.Name, jobKey.Group.Split('-')[1] + " " + jobKey.Name + " 卸载成功！", ConsoleColor.Yellow);
			}
		}
	}

	public static async Task Install(ISchedulerFactory schedulerFactory, Organization organization, string userId)
	{
		if (organization.BiliBili != "0")
		{
			await BusBiliQuartz.LoginQuartz(schedulerFactory, int.Parse(userId));
		}
		if (organization.斗鱼 != "0")
		{
			await BusDouYuQuartz.LoginQuartz(schedulerFactory, int.Parse(userId));
		}
		if (organization.快手 != "0")
		{
			await BusKSQuartz.LoginQuartz(schedulerFactory, int.Parse(userId));
		}
	}

	public static async Task<JArray> GetQuartzList(ISchedulerFactory schedulerFactory)
	{
		JArray jArray = new JArray();
		IScheduler scheduler = await schedulerFactory.GetScheduler();
		using (IEnumerator<JobKey> enumerator = (await scheduler.GetJobKeys(GroupMatcher<JobKey>.AnyGroup())).GetEnumerator())
		{
			JobKey job;
			JArray jArray2;
			JObject jObject;
			int num;
			JObject jObject2;
			for (; enumerator.MoveNext(); jObject["sort"] = num, jObject2 = jObject, jObject2["state"] = await ExecutingJob(scheduler, job.Group, job.Name), jArray2.Add(jObject))
			{
				job = enumerator.Current;
				IReadOnlyCollection<ITrigger> readOnlyCollection = await scheduler.GetTriggersOfJob(job);
				string text = "未知";
				foreach (ITrigger item in readOnlyCollection)
				{
					if (!item.HasMillisecondPrecision)
					{
						text = ((CronTriggerImpl)item).CronExpressionString;
						break;
					}
				}
				string[] array = (text ?? "").Split(' ');
				TimeSpan? timeSpan = null;
				if (array.Length > 3)
				{
					_ = array[2] + ":" + array[1] + ":" + array[0];
					timeSpan = new TimeSpan(int.Parse(array[2]), int.Parse(array[1]), int.Parse(array[0]));
				}
				jArray2 = jArray;
				jObject = new JObject
				{
					["id"] = job.Group.Split('-')[2],
					["taskName"] = job.Name,
					["group"] = job.Group.Split('-')[1],
					["time"] = timeSpan
				};
				if (timeSpan.HasValue)
				{
					TimeSpan value = new TimeSpan(DateTime.Now.Hour, DateTime.Now.Minute, DateTime.Now.Second);
					TimeSpan? timeSpan2 = timeSpan;
					if (!(value > timeSpan2))
					{
						num = 0;
						continue;
					}
				}
				num = 1;
			}
		}
		string[] fields = new string[2] { "sort", "time" };
		return new JArray(jArray.OrderBy((JToken item) => fields.Select((string field) => item[field]).Aggregate((JToken jToken, JToken next) => jToken?.ToString() + "," + next)).ToList());
	}
}
