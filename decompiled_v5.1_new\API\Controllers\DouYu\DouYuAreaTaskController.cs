using System;
using System.Data;
using System.Threading.Tasks;
using API.BusService.System;
using API.Common;
using API.DataAccess.DouYu;
using API.Models.Comm;
using Microsoft.AspNetCore.Mvc;

namespace API.Controllers.DouYu;

public class DouYuAreaTaskController : Controller
{
	[HttpPost]
	public Response GetAreaTaskList([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectSearch, "search");
			string jObject2 = Util.GetJObject(model.jObjectSearch, "areaId", "0");
			DataTable areaTaskList = DouYuAreaTask.GetAreaTaskList(jObject2, jObject, model.limit, model.offset, model.prop, model.order);
			response.data = Util.GetTableResponse(areaTaskList);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public async Task<Response> SaveAreaTask([FromBody] Request model)
	{
		Response mRet = new Response();
		try
		{
			await DouYuAreaTask.SaveAreaTask(model.jObjectParam, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}

	[HttpPost]
	public Response DelAreaTask([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			DouYuAreaTask.DelAreaTask(model.jObjectParam, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response EnableSwitch([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			response.data = DouYuAreaTask.EnableSwitch(model.jObjectParam, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
		}
		catch (Exception ex)
		{
			string jObject = Util.GetJObject(model.jObjectParam, "FEnable");
			response.data = ((!(jObject == "1")) ? 1 : 0);
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}
}
