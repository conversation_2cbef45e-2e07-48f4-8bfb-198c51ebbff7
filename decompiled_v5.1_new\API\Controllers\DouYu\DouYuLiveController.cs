using System;
using System.IO;
using API.BusService.System;
using API.Common;
using API.DataAccess.DouYu;
using API.Models.Comm;
using Microsoft.AspNetCore.Mvc;

namespace API.Controllers.DouYu;

public class DouYuLiveController : Controller
{
	[HttpPost]
	public Response DelLive([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "src");
			Util.FileDelete(Directory.GetCurrentDirectory() + "\\WWWRoot\\" + jObject);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response SaveLive([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			DouYuLive.SaveLive(model.jObjectParam, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response GetLiveConfig([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "cookieId");
			string jObject2 = Util.GetJObject(model.jObjectParam, "areaId");
			response.data = DouYuLive.GetLiveConfig(jObject, jObject2, BusSysUser.Instance.User.Id);
			response.message = Directory.GetCurrentDirectory() + "\\Util\\FFmpeg\\ffmpeg.exe -i \"输入路径\" -c:v libx264 -crf 20 -c:a aac -strict experimental -b:a 192k \"输出路径\"";
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}
}
