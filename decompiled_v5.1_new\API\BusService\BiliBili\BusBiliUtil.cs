using System;
using System.Collections.Generic;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using API.Common;
using Newtonsoft.Json.Linq;

namespace API.BusService.BiliBili;

public class BusBiliUtil
{
	private static readonly int[] MixinKeyEncTab = new int[64]
	{
		46, 47, 18, 2, 53, 8, 23, 32, 15, 50,
		10, 31, 58, 3, 45, 35, 27, 43, 5, 49,
		33, 9, 42, 19, 29, 28, 14, 39, 12, 38,
		41, 13, 37, 48, 7, 16, 24, 55, 40, 61,
		26, 17, 0, 1, 60, 51, 30, 4, 22, 25,
		54, 21, 56, 59, 6, 63, 57, 62, 11, 36,
		20, 34, 44, 52
	};

	public static async Task<string> CrateCookie(HttpClientFactory httpClientFactory)
	{
		string b_lsid = GetLSID();
		string uuid = GetUUID();
		string b_nut = DateTimeOffset.Now.ToUnixTimeSeconds().ToString();
		string text = Hash("ts" + b_nut, "XgwSnGZ1p", "HMACSHA256");
		string bili_ticket = "";
		string bili_ticket_expires = "";
		JObject jObject = await httpClientFactory.Post("https://api.bilibili.com/bapis/bilibili.api.ticket.v1.Ticket/GenWebTicket?key_id=ec02&hexsign=" + text + "&context[ts]=" + b_nut + "&csrf=", "");
		if (Util.GetJObject(jObject, "code") == "0")
		{
			bili_ticket = Util.GetJObject(jObject["data"], "ticket");
			bili_ticket_expires = (Util.GetJObject<int>(jObject["data"], "created_at") + Util.GetJObject<int>(jObject["data"], "ttl")).ToString();
		}
		jObject = await httpClientFactory.Get("https://api.bilibili.com/x/frontend/finger/spi");
		string jObject2 = Util.GetJObject(jObject["data"], "b_3");
		string jObject3 = Util.GetJObject(jObject["data"], "b_4");
		string text2 = "buvid3=" + jObject2 + "; buvid4=" + jObject3 + "; b_nut=" + b_nut + "; b_lsid=" + b_lsid + "; _uuid=" + uuid + "; ";
		text2 = text2 + "bili_ticket=" + bili_ticket + "; bili_ticket_expires=" + bili_ticket_expires + "; ";
		text2 += "share_source_origin=copy_web; bsource=share_source_copylink_web; CURRENT_FNVAL=4048; ";
		text2 = text2 + "buvid_fp=" + Util.CalcMD5(bili_ticket).ToLower() + "; ";
		return text2 + "header_theme_version=CLOSE; enable_web_push=DISABLE; enable_feed_channel=ENABLE; home_feed_column=5; buvid_fp_plain=undefined";
	}

	public static string GetLSID()
	{
		decimal timestamp = DateTimeOffset.Now.ToUnixTimeMilliseconds();
		string text = O(timestamp);
		return A(8) + "_" + text;
	}

	public static string GetUUID()
	{
		double num = (double)DateTimeOffset.Now.ToUnixTimeMilliseconds() % Math.Pow(10.0, 5.0);
		return A(8) + "-" + A(4) + "-" + A(4) + "-" + A(4) + "-" + A(12) + S(num.ToString(), 5) + "infoc";
	}

	private static string O(decimal timestamp)
	{
		decimal num = Math.Ceiling(timestamp);
		long num2 = (long)num;
		return Convert.ToString(num2, 16).ToUpper();
	}

	private static string S(string e, int t)
	{
		string text = "";
		if (e.Length < t)
		{
			for (int i = 0; i < t - e.Length; i++)
			{
				text += "0";
			}
		}
		return text + e;
	}

	private static string A(int e)
	{
		string text = "";
		for (int i = 0; i < e; i++)
		{
			byte[] value = Guid.NewGuid().ToByteArray();
			int seed = BitConverter.ToInt32(value, 0);
			Random random = new Random(seed);
			decimal timestamp = 16m * decimal.Parse(random.NextDouble().ToString());
			text += O(timestamp);
		}
		return S(text, e);
	}

	public static string Sypder(string text, ICollection<int>? rules, string key)
	{
		string text2 = text;
		if (rules != null)
		{
			using IEnumerator<int> enumerator = rules.GetEnumerator();
			while (enumerator.MoveNext())
			{
				switch (enumerator.Current)
				{
				case 0:
					text2 = Hash(text2, key, "HMACMD5");
					break;
				case 1:
					text2 = Hash(text2, key, "HMACSHA1");
					break;
				case 2:
					text2 = Hash(text2, key, "HMACSHA256");
					break;
				case 3:
					text2 = Hash(text2, key, "HMACSHA224");
					break;
				case 4:
					text2 = Hash(text2, key, "HMACSHA512");
					break;
				case 5:
					text2 = Hash(text2, key, "HMACSHA384");
					break;
				}
			}
		}
		return text2;
	}

	public static string Hash(string text, string key, string algorithmName)
	{
		HMAC hMAC = HMAC.Create(algorithmName);
		if (hMAC != null)
		{
			hMAC.Key = Encoding.UTF8.GetBytes(key);
			byte[] array = hMAC.ComputeHash(Encoding.UTF8.GetBytes(text));
			return BitConverter.ToString(array).Replace("-", "").ToLower();
		}
		return "";
	}

	public static async Task<JObject> GetValidate(JObject geetest, string gt, string challenge, string referer)
	{
		string jObject = Util.GetJObject(geetest, "FName");
		string jObject2 = Util.GetJObject(geetest, "FMethod");
		string jObject3 = Util.GetJObject(geetest, "FUrl");
		string jObject4 = Util.GetJObject(geetest, "FGt");
		string jObject5 = Util.GetJObject(geetest, "FChallenge");
		string keyField = Util.GetJObject(geetest, "FKey");
		string keyValue = Util.GetJObject(geetest, "FKeyValue");
		if (keyValue == "")
		{
			throw new Exception("请配置" + jObject + "的秘钥信息！");
		}
		string jObject6 = Util.GetJObject(geetest, "FType");
		string jObject7 = Util.GetJObject(geetest, "FTypeValue");
		string jObject8 = Util.GetJObject(geetest, "FParamValue");
		string successCodeField = Util.GetJObject(geetest, "FSuccess");
		string successCode = Util.GetJObject(geetest, "FSuccessCode");
		string resultValidate = Util.GetJObject(geetest, "FResultValidate");
		string text = keyField + "=" + keyValue + "&" + jObject4 + "=" + gt + "&" + jObject5 + "=" + challenge;
		if (jObject6 != "")
		{
			text = text + "&" + jObject6 + "=" + jObject7;
		}
		if (jObject8 != "")
		{
			try
			{
				JObject jObject9 = JObject.Parse(jObject8);
				foreach (KeyValuePair<string, JToken> item in jObject9)
				{
					text = text + "&" + item.Key + "=" + ((item.Key == "referer") ? referer : Util.GetJObject(jObject9, item.Key));
				}
			}
			catch
			{
			}
		}
		HttpClientFactory httpClientFactory = new HttpClientFactory("", new Dictionary<string, string> { { "Content-Type", "application/x-www-form-urlencoded" } });
		JObject jObject10 = ((!(jObject2 == "0")) ? (await httpClientFactory.Post(jObject3, text)) : (await httpClientFactory.Get(jObject3 + "?" + text)));
		if (Util.GetJObject(jObject10, successCodeField) == successCode)
		{
			string[] array = resultValidate.Split('.');
			JToken jToken = jObject10;
			string text2 = "";
			for (int i = 0; i < array.Length; i++)
			{
				if (array.Length - 1 == i)
				{
					text2 = Util.GetJObject(jToken, array[i]);
				}
				else
				{
					jToken = Util.GetJObject<JToken>(jToken, array[i]);
				}
			}
			jObject10["Validate"] = text2;
			jObject10["KeyField"] = keyField;
			jObject10["KeyValue"] = keyValue;
			return jObject10;
		}
		return jObject10;
	}

	public static string GetWRid(string imgKey, string subKey)
	{
		string s = imgKey + subKey;
		StringBuilder stringBuilder = new StringBuilder();
		for (int i = 0; i < 32; i++)
		{
			stringBuilder.Append(CharAt(s, MixinKeyEncTab[i]));
		}
		long num = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
		return "w_rid=" + Util.CalcMD5("wts=" + num + stringBuilder.ToString()).ToLower() + "&wts=" + num;
	}

	public static string GetWRid(string imgKey, string subKey, string @params)
	{
		string s = imgKey + subKey;
		StringBuilder stringBuilder = new StringBuilder();
		for (int i = 0; i < 32; i++)
		{
			stringBuilder.Append(CharAt(s, MixinKeyEncTab[i]));
		}
		long num = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
		return @params + "&wts=" + num + "&w_rid=" + Util.CalcMD5(@params + "&wts=" + num + stringBuilder.ToString()).ToLower();
	}

	public static string GetWRidPlus(string imgKey, string subKey, string @params)
	{
		string s = imgKey + subKey;
		StringBuilder stringBuilder = new StringBuilder();
		for (int i = 0; i < 32; i++)
		{
			stringBuilder.Append(CharAt(s, MixinKeyEncTab[i]));
		}
		return @params + "&w_rid=" + Util.CalcMD5(@params + stringBuilder.ToString()).ToLower();
	}

	public static string CharAt(string s, int index)
	{
		if (index >= s.Length || index < 0)
		{
			return "";
		}
		return s.Substring(index, 1);
	}
}
