using System;
using API.BusService.System;
using API.Common;
using API.DataAccess.System;
using API.Models.Comm;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;

namespace API.Controllers.System;

public class SysMenuController : Controller
{
	[HttpPost]
	public Response GetMenuList()
	{
		Response response = new Response();
		try
		{
			response.data = SysMenu.GetMenuList();
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response SaveMenu([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			SysMenu.SaveMenu(model.jObjectParam, BusSysUser.Instance.User.Name, BusSysUser.Instance.User.Id, model.curTime);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response DeleteMenu([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "id");
			SysMenu.DeleteMenu(jObject, BusSysUser.Instance.User.Name, BusSysUser.Instance.User.Id, model.curTime);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response EnableMenu([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "id");
			string jObject2 = Util.GetJObject(model.jObjectParam, "enable");
			JObject jObject3 = new JObject
			{
				["Fid"] = jObject,
				["FEnable"] = jObject2
			};
			DataCURD.Save(jObject3, "TSysMenu", "Fid", "Fid", BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime, SQLHelper.LocalDB.InitCnn());
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	public Response SortMenu([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			JObject jObjectParam = model.jObjectParam;
			string jObject = Util.GetJObject(jObjectParam, "draggingNodeId");
			string jObject2 = Util.GetJObject(jObjectParam, "dropNodeId");
			string jObject3 = Util.GetJObject(jObjectParam, "dropType");
			SysMenu.SortMenu(jObject, jObject2, jObject3, BusSysUser.Instance.User.Name, BusSysUser.Instance.User.Id, model.curTime);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response GetMenuId([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectSearch, "parentId", "0");
			response.data = SysMenu.GetMenuId(jObject);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}
}
