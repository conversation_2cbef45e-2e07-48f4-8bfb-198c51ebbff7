using System;
using System.Data;
using API.Common;
using API.DataAccess.BiliBili;
using API.Models.Comm;
using Microsoft.AspNetCore.Mvc;

namespace API.Controllers.BiliBili;

public class BiliGroupController : Controller
{
	[HttpPost]
	public Response GetCookiesList([FromBody] Request model, User user)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectSearch, "search");
			string jObject2 = Util.GetJObject(model.jObjectSearch, "areaId");
			DataTable cookiesList = BiliGroup.GetCookiesList(jObject2, jObject, user.Id, model.limit, model.offset, model.prop, model.order);
			response.data = Util.GetTableResponse(cookiesList);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response Complete(User user)
	{
		Response response = new Response();
		try
		{
			BiliGroup.Complete(user.Id);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response SetOption([FromBody] Request model, User user)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "aricles");
			string jObject2 = Util.GetJObject(model.jObjectParam, "mileage");
			string jObject3 = Util.GetJObject(model.jObjectParam, "id");
			string jObject4 = Util.GetJObject(model.jObjectParam, "key");
			string jObject5 = Util.GetJObject(model.jObjectParam, "areaId");
			BiliGroup.SetOption(jObject3, jObject4, jObject5, jObject, jObject2, user.Id);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response Matching(User user)
	{
		Response response = new Response();
		try
		{
			BiliGroup.Matching(user.Id);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response Del(User user)
	{
		Response response = new Response();
		try
		{
			BiliGroup.Del(user.Id);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}
}
