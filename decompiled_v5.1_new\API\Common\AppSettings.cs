using System.IO;
using Microsoft.Extensions.Configuration;

namespace API.Common;

public class AppSettings
{
	private static readonly IConfigurationBuilder _configurationBuilder = new ConfigurationBuilder().SetBasePath(Directory.GetCurrentDirectory()).AddJsonFile("appsettings.json");

	private static readonly IConfigurationRoot _configurationRoot = _configurationBuilder.Build();

	public static string GetVal(string item, string type = "AppSettings")
	{
		item = type + ":" + item;
		string text = _configurationRoot[item] ?? "";
		if (text != "")
		{
			return text;
		}
		return item switch
		{
			"AppSettings:TokenPrefix" => "uuid", 
			"AppSettings:TokenKey" => "jmjbG85090999EWO4EyItpA4", 
			"AppSettings:TokenTimeOut" => "31536000", 
			"AppSettings:IdleTimeout" => "31536000", 
			"AppSettings:Log" => "false", 
			"VueConfig:AppName" => "抢码小工具", 
			"VueConfig:AppVer" => "5.1", 
			_ => text, 
		};
	}
}
