using System;
using System.Data;
using System.Threading.Tasks;
using API.Common;
using API.Models.Comm;
using Newtonsoft.Json.Linq;

namespace API.DataAccess.BiliBili;

public class BiliBulletSceen
{
	public static async Task UpateBulletSceen(User user)
	{
		Request model = new Request
		{
			jObjectParam = new JObject { ["userId"] = user.Id },
			jObjectSearch = new JObject { ["type"] = "直播间','弹幕礼物观看" }
		};
		object obj = await Util.Request("/Common/GetBulletSceenList", model);
		if (obj != null)
		{
			JObject jObject = JObject.FromObject(obj);
			JArray jArray = (JArray)(jObject["rows"] ?? new JArray());
			DataTable dataTable = jArray.ToObject<DataTable>();
			if (dataTable != null)
			{
				dataTable.Columns.Remove("FSys");
				DataColumn column = new DataColumn("FSys")
				{
					DefaultValue = 1
				};
				dataTable.Columns.Add(column);
				DataCURD.Delete("TBulletSceen", "删除系统弹幕", "FSys", "1", user.Id, user.Name, DateTime.Now.ToString(), SQLHelper.BiliLocalDB.InitCnn());
				SQLHelper.BiliLocalDB.SqlBulkCopyByDataTable("TBulletSceen", dataTable);
			}
		}
	}

	public static DataTable GetBulletSceenList(string search, string trigger, string listen, int limit = 0, int offset = 0, string prop = "", string order = "")
	{
		string text = " SELECT *,CASE FSys WHEN 1 THEN '系统默认' ELSE '' END AS FSysName";
		text += " ,STUFF((SELECT ','+ CAST(B1.FName AS VARCHAR(100)) FROM TArea B1 WHERE CHARINDEX(','+CAST(B1.Fid AS NVARCHAR(100))+',',','+FAreaId+',')>0 FOR XML PATH('')),1,1,'') AS FAreaName";
		text += " ,STUFF((SELECT ','+ CAST(B1.FName AS VARCHAR(100)) FROM TCookies B1 WHERE CHARINDEX(','+CAST(B1.Fid AS NVARCHAR(100))+',',','+FCookieId+',')>0 FOR XML PATH('')),1,1,'') AS FCookieName";
		text = text + SQLHelper.total + " FROM TBulletSceen WHERE 1=1";
		if (trigger != "")
		{
			text = text + " AND FTrigger='" + trigger + "'";
		}
		if (listen != "")
		{
			text = text + " AND FListen='" + listen + "'";
		}
		if (search != "")
		{
			text = text + " AND ( FMsg LIKE '%" + search + "%' OR FKeyword1 LIKE '%" + search + "%' OR FKeyword2 LIKE '%" + search + "%' OR FKeyword3 LIKE '%" + search + "%')";
		}
		if (prop == "")
		{
			prop = "FEnable DESC,FSys ASC,Fid ";
			order = "DESC";
		}
		return SQLHelper.BiliLocalDB.RunSqlDt(text, limit, offset, prop, order);
	}
}
