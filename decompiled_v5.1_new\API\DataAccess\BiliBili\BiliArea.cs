using System;
using System.Collections.Generic;
using System.Data;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using API.BusService.System;
using API.Common;
using API.Models.Comm;
using Microsoft.Data.SqlClient;
using Newtonsoft.Json.Linq;

namespace API.DataAccess.BiliBili;

public class BiliArea
{
	public static string GetAreaName(string id)
	{
		string sSql = " SELECT FName FROM TArea WHERE Fid=" + id;
		return SQLHelper.BiliLocalDB.RunSqlStr(sSql);
	}

	public static DataTable GetAreaList(string id, string organization, string search = "", int limit = 0, int offset = 0, string prop = "", string order = "")
	{
		string text = " SELECT *,'第'+CAST(DATEDIFF(DAY,FArticlesDate,GETDATE())+1  AS NVARCHAR(100)) +'天' AS FMsg " + SQLHelper.total + " FROM TArea WHERE 1=1 ";
		if (organization != "")
		{
			text = text + " AND Fid IN (" + organization + ")";
		}
		if (id != "")
		{
			text = text + " AND Fid IN(" + id + ")";
		}
		if (search != "")
		{
			text = text + " AND FName LIKE '%" + search + "%'";
		}
		if (prop == "")
		{
			prop = "FSort";
			order = "ASC";
		}
		return SQLHelper.BiliLocalDB.RunSqlDt(text, limit, offset, prop, order);
	}

	public static DataTable GetAreaActivityList(string id)
	{
		string text = " SELECT FActivityUrl,FActivityId,FType,Fid";
		text += " FROM (SELECT FActivityId1, FActivityId2, FActivityId3, FActivityUrl, FType,Fid FROM TArea ) T ";
		text += " UNPIVOT ( FActivityId FOR Supplier IN (FActivityId1 ,FActivityId2,FActivityId3)) P WHERE FActivityId!='' AND FActivityId!='0'";
		if (id != "")
		{
			text = text + " AND Fid IN (" + id + ")";
		}
		return SQLHelper.BiliLocalDB.RunSqlDt(text);
	}

	public static DataTable GetAreaActivityIdList(string areaId)
	{
		string text = " SELECT FActivityId1 AS value,'里程' AS label FROM TArea WHERE Fid=" + areaId + " AND FActivityId1!='' UNION ALL";
		text = text + " SELECT FActivityId2 AS value,'抽奖' AS label  FROM TArea WHERE Fid=" + areaId + " AND FActivityId2!='' UNION ALL";
		text = text + " SELECT FActivityId3 AS value,'活动' AS label  FROM TArea WHERE Fid=" + areaId + " AND FActivityId3!=''";
		return SQLHelper.BiliLocalDB.RunSqlDt(text);
	}

	public static async Task SaveArea(JObject jObject, int userId, string userName, string curTime)
	{
		string mode = Util.GetJObject(jObject, "mode");
		string id = Util.GetJObject(jObject, "Fid");
		jObject["FUpdateTime"] = curTime;
		string jObject2 = Util.GetJObject(jObject, "FArticlesTag");
		string jObject3 = Util.GetJObject(jObject, "FArticlesMissionId");
		if (jObject2 != "" && jObject3 == "")
		{
			DataTable cookiesList = BiliCookies.GetCookiesList("", "", BusSysUser.Instance.User.Id, "");
			if (cookiesList.Rows.Count > 0)
			{
				DataRow dataRow = cookiesList.Rows[0];
				string name = dataRow["FKey"].ToString() ?? "";
				string cookie = dataRow["FCookie"].ToString() ?? "";
				string header = dataRow["FHeaders"].ToString() ?? "";
				string text = dataRow["FProxyAddress"].ToString() ?? "";
				string userName2 = dataRow["FProxyUserName"].ToString() ?? "";
				string password = dataRow["FProxyPassword"].ToString() ?? "";
				HttpClientHandler defaultHandler = null;
				if (text != "")
				{
					defaultHandler = new HttpClientHandler
					{
						Proxy = new WebProxy
						{
							Address = new Uri(text),
							Credentials = new NetworkCredential(userName2, password)
						}
					};
				}
				Dictionary<string, string> dictionary = HttpClientFactory.FormataHeader(header, cookie);
				dictionary.Remove("Accept-Language");
				HttpClientFactory httpClientFactory = new HttpClientFactory(name, dictionary, defaultHandler);
				JObject jObject4 = await httpClientFactory.Get("https://member.bilibili.com/x/vupre/web/topic/search?keywords=" + jObject2 + "&page_size=50&offset=0");
				if (Util.GetJObject(jObject4, "code") == "0")
				{
					JArray jArray = Util.GetJObject<JArray>(Util.GetJObject<JToken>(jObject4["data"], "result"), "topics") ?? new JArray();
					if (jArray.Count > 0)
					{
						jObject["FArticlesMissionId"] = Util.GetJObject(jArray[0], "mission_id");
					}
				}
			}
		}
		string sSql;
		if (mode == "add")
		{
			sSql = " SELECT 1 FROM TArea WHERE Fid=" + id;
			string text2 = SQLHelper.BiliLocalDB.RunSqlStr(sSql);
			if (text2 == "1")
			{
				throw new Exception("当前分区已存在！");
			}
			jObject["Fid"] = 0;
			DataCURD.Save(jObject, "TArea", "新增分区", "Fid", userId, userName, curTime, SQLHelper.BiliLocalDB.InitCnn());
			sSql = " UPDATE TArea SET Fid=" + id + " WHERE Fid=0";
			SQLHelper.BiliLocalDB.RunSqlText(sSql);
		}
		else
		{
			DataCURD.Save(jObject, "TArea", "编辑分区", "Fid", userId, userName, curTime, SQLHelper.BiliLocalDB.InitCnn());
		}
		string jObject5 = Util.GetJObject(jObject, "FTotalKey");
		string jObject6 = Util.GetJObject(jObject, "FTaskUrl");
		if (jObject6 != "" && jObject5 != "")
		{
			jObject = new JObject
			{
				["FTaskUrl"] = jObject6,
				["FTaskKey"] = jObject5,
				["FAreaType"] = Util.GetJObject(jObject, "FType"),
				["FSort"] = Util.GetJObject(jObject, "FSort"),
				["FReceiveFrom"] = Util.GetJObject(jObject, "FReceiveFrom"),
				["FPrice"] = 0,
				["FCompulsory"] = 0,
				["FComplete"] = 0,
				["FDaily"] = 0,
				["FEnable"] = 1,
				["FAreaId"] = id
			};
			await BiliAreaTask.SaveAreaTask(jObject, userId, userName, curTime);
		}
		string text3 = "'" + Util.GetJObject(jObject, "FActivityId1") + "','" + Util.GetJObject(jObject, "FActivityId2") + "','" + Util.GetJObject(jObject, "FActivityId3") + "'";
		sSql = " DELETE TCdkey WHERE FAreaId=" + id + " AND FActivityId NOT IN (" + text3 + ")";
		SQLHelper.BiliLocalDB.RunSqlText(sSql);
	}

	public static void DelArea(string id, int userId, string userName, string curTime)
	{
		SqlConnection sqlConnection = SQLHelper.BiliLocalDB.InitCnn();
		SqlCommand sqlCommand = sqlConnection.CreateCommand();
		sqlCommand.Transaction = sqlConnection.BeginTransaction();
		try
		{
			DataCURD.Delete("TArea", "删除分区", "Fid", id, userId, userName, curTime, sqlCommand);
			DataCURD.Delete("TAreaTask", "删除分区任务", "FAreaId", id, userId, userName, curTime, sqlCommand);
			DataCURD.Delete("TCookiesTask", "删除Cookie与分区任务对照", "FAreaId", id, userId, userName, curTime, sqlCommand);
			DataCURD.Delete("TCdkey", "删除Cdkey", "FAreaId", id, userId, userName, curTime, sqlCommand);
			sqlCommand.Transaction.Commit();
		}
		catch (Exception ex)
		{
			sqlCommand.Transaction.Rollback();
			throw new Exception(ex.Message);
		}
		finally
		{
			sqlConnection.Close();
			sqlConnection.Dispose();
		}
	}

	public static JObject GetAreaInfo(Request request)
	{
		string jObject = Util.GetJObject(request.jObjectParam, "areaId");
		string sSql = " SELECT * FROM TSysOrganization WHERE Fid=2 OR FParentId=2 AND CHARINDEX(','+CAST(Fid AS NVARCHAR(100))+',',','+(SELECT FOrganizationId FROM TSysUser WHERE Fid=" + Util.GetJObject<int>(request.jObjectParam, "userId") + ")+',')>0";
		DataTable dataTable = SQLHelper.LocalDB.RunSqlDt(sSql);
		if (dataTable.Rows.Count != 1)
		{
			DataRow[] array = dataTable.Select("Fid=" + jObject);
			if (array.Length == 0)
			{
				throw new Exception("无权限！");
			}
		}
		sSql = " SELECT * FROM TArea WHERE Fid=" + jObject;
		DataTable dataTable2 = SQLHelper.BiliLocalDB.RunSqlDt(sSql);
		if (dataTable2.Rows.Count == 0)
		{
			throw new Exception("当前游戏无信息，请自己维护！");
		}
		sSql = " SELECT * FROM TAreaTask WHERE FAreaId=" + jObject;
		DataTable o = SQLHelper.BiliLocalDB.RunSqlDt(sSql);
		JArray jArray = JArray.FromObject(dataTable2);
		JObject jObject2 = (JObject)jArray[0];
		jObject2["Child"] = JArray.FromObject(o);
		return jObject2;
	}

	public static async Task UpdateAreaInfo(string areaId, string name, int userId, string userName, string curTime)
	{
		SqlConnection conn = SQLHelper.BiliLocalDB.InitCnn();
		SqlCommand pCmd = conn.CreateCommand();
		pCmd.Transaction = conn.BeginTransaction();
		try
		{
			JObject jObjectParam = new JObject
			{
				["userId"] = userId,
				["areaId"] = areaId
			};
			Request request = new Request
			{
				jObjectParam = jObjectParam,
				curTime = curTime
			};
			object obj = await Util.Request("/Common/BiliUpateAreaInfo", request);
			if (obj == null)
			{
				obj = GetAreaInfo(request);
			}
			jObjectParam = JObject.FromObject(obj);
			JArray jArray = (JArray)(jObjectParam["Child"] ?? new JArray());
			jObjectParam.Remove("Child");
			string text = jObjectParam["FUpdateTime"]?.ToString() ?? "";
			string sSql = " SELECT CASE CAST(FUpdateTime AS datetime) WHEN '" + text + "' THEN 1 ELSE 0 END FROM TArea WHERE Fid=" + areaId;
			string text2 = SQLHelper.RunSqlStr(sSql, pCmd);
			if (text2 == "1")
			{
				throw new Exception("已是最新版本！");
			}
			sSql = " IF( (SELECT 1 FROM TArea WHERE Fid=" + areaId + ") IS NULL) INSERT TArea (Fid,FName) VALUES (" + areaId + ",'" + name + "')";
			SQLHelper.RunSqlText(sSql, pCmd);
			DataCURD.Save(jObjectParam, "TArea", "同步分区", "Fid", userId, userName, curTime, pCmd);
			DataCURD.Delete("TAreaTask", "同步删除分区任务", "FAreaId", areaId, userId, userName, curTime, pCmd);
			for (int i = 0; i < jArray.Count; i++)
			{
				JObject jObject = (JObject)jArray[i];
				jObject["Fid"] = 0;
				DataCURD.Save(jObject, "TAreaTask", "同步分区任务", "Fid", userId, userName, curTime, pCmd);
			}
			DataCURD.Delete("TCdkey", "同步删除Cdkey信息", "FAreaId", areaId, userId, userName, curTime, pCmd);
			DataCURD.Delete("TCookiesTask", "同步删除Cookie任务信息", "FAreaId", areaId, userId, userName, curTime, pCmd);
			pCmd.Transaction.Commit();
		}
		catch (Exception ex)
		{
			pCmd.Transaction.Rollback();
			throw new Exception(ex.Message);
		}
		finally
		{
			conn.Close();
			conn.Dispose();
		}
	}
}
