<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <AssemblyName>API</AssemblyName>
    <GenerateAssemblyInfo>False</GenerateAssemblyInfo>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <LangVersion>12.0</LangVersion>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
  </PropertyGroup>
  
  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Quartz" Version="3.8.0" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="5.1.0" />
    <PackageReference Include="NPOI" Version="2.6.2" />
    <PackageReference Include="SixLabors.ImageSharp" Version="3.1.0" />
    <PackageReference Include="SixLabors.ImageSharp.Drawing" Version="2.1.0" />
    <PackageReference Include="SixLabors.Fonts" Version="2.0.4" />
    <PackageReference Include="log4net" Version="2.0.15" />
  </ItemGroup>
  
  <ItemGroup>
    <Reference Include="BiliveDanmakuAgent">
      <HintPath>D:\Dev\bibi\Relesez5.1\BiliveDanmakuAgent.dll</HintPath>
    </Reference>
    <Reference Include="Jint">
      <HintPath>D:\Dev\bibi\Relesez5.1\Jint.dll</HintPath>
    </Reference>
    <Reference Include="Acornima">
      <HintPath>D:\Dev\bibi\Relesez5.1\Acornima.dll</HintPath>
    </Reference>
  </ItemGroup>
</Project>