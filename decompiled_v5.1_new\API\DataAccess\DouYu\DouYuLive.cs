using System;
using System.Data;
using System.IO;
using API.Common;
using Microsoft.Data.SqlClient;
using Newtonsoft.Json.Linq;

namespace API.DataAccess.DouYu;

public class DouYuLive
{
	public static void SaveLive(JObject jObject, int userId, string userName, string curTime)
	{
		SqlConnection sqlConnection = SQLHelper.DouYuLocalDB.InitCnn();
		SqlCommand sqlCommand = sqlConnection.CreateCommand();
		sqlCommand.Transaction = sqlConnection.BeginTransaction();
		try
		{
			string jObject2 = Util.GetJObject(jObject, "cookieId");
			string jObject3 = Util.GetJObject(jObject, "areaId");
			JArray jArray = Util.GetJObject<JArray>(jObject, "livePath") ?? new JArray();
			string text = "";
			text = ((jArray.Count <= 0) ? Util.GetJObject(jObject, "livePath").Split(",")[0] : Util.GetJObject(jArray[0], "name"));
			string path = Directory.GetCurrentDirectory() + "\\WWWRoot\\" + text;
			if (!File.Exists(path))
			{
				text = "";
			}
			JArray jArray2 = Util.GetJObject<JArray>(jObject, "liveConfig") ?? new JArray();
			JArray jArray3 = Util.GetJObject<JArray>(jObject, "liveBulletScreen") ?? new JArray();
			DataCURD.Delete("TCookiesLive", "删除直播地址", "FUserId,FCookieId,FAreaId", userId + "," + jObject2 + "," + jObject3, userId, userName, curTime, sqlCommand);
			DataCURD.Delete("TCookiesLiveConfig", "删除弹幕礼物配置", "FUserId,FCookieId,FAreaId", userId + "," + jObject2 + "," + jObject3, userId, userName, curTime, sqlCommand);
			DataCURD.Delete("TCookiesLiveBulletScreen", "删除弹幕内容配置", "FUserId,FCookieId,FAreaId", userId + "," + jObject2 + "," + jObject3, userId, userName, curTime, sqlCommand);
			JObject jObject4 = new JObject
			{
				["Fid"] = 0,
				["FUserId"] = userId,
				["FCookieId"] = jObject2,
				["FAreaId"] = jObject3,
				["FLivePath"] = text
			};
			DataCURD.Save(jObject4, "TCookiesLive", "新增直播地址", "Fid", userId, userName, curTime, sqlCommand);
			foreach (JToken item in jArray2)
			{
				string jObject5 = Util.GetJObject(item, "FRoomNo");
				if (jObject5 != "")
				{
					JObject jObject6 = new JObject
					{
						["Fid"] = 0,
						["FUserId"] = userId,
						["FCookieId"] = jObject2,
						["FAreaId"] = jObject3,
						["FRoomNo"] = jObject5,
						["FGiftName"] = Util.GetJObject(item, "FGiftName"),
						["FGiftCode"] = Util.GetJObject(item, "FGiftCode"),
						["FGiftPrice"] = Util.GetJObject(item, "FGiftPrice", "0"),
						["FGiftNum"] = Util.GetJObject(item, "FGiftNum", "0"),
						["FGiftSkinId"] = Util.GetJObject(item, "FGiftSkinId", "0"),
						["FBulletScreenNum"] = Util.GetJObject(item, "FBulletScreenNum", "0"),
						["FWatchMinutes"] = Util.GetJObject(item, "FWatchMinutes", "0")
					};
					DataCURD.Save(jObject6, "TCookiesLiveConfig", "新增弹幕礼物配置", "Fid", userId, userName, curTime, sqlCommand);
				}
			}
			foreach (JToken item2 in jArray3)
			{
				string jObject7 = Util.GetJObject(item2, "FContent");
				if (jObject7 != "")
				{
					JObject jObject8 = new JObject
					{
						["Fid"] = 0,
						["FUserId"] = userId,
						["FCookieId"] = jObject2,
						["FAreaId"] = jObject3,
						["FContent"] = jObject7
					};
					DataCURD.Save(jObject8, "TCookiesLiveBulletScreen", "新增弹幕内容配置", "Fid", userId, userName, curTime, sqlCommand);
				}
			}
			sqlCommand.Transaction.Commit();
		}
		catch (Exception ex)
		{
			sqlCommand.Transaction.Rollback();
			throw new Exception(ex.Message);
		}
		finally
		{
			sqlConnection.Close();
			sqlConnection.Dispose();
		}
	}

	public static string GetLivePathList(string cookieId, string areaId, int userId)
	{
		string sSql = " SELECT FLivePath FROM TCookiesLive WHERE FUserId=" + userId + " AND FCookieId=" + cookieId + " AND FAreaId=" + areaId;
		return SQLHelper.DouYuLocalDB.RunSqlStr(sSql);
	}

	public static DataTable GetLivePathAll(string cookieId, string areaId, int userId, string liveLog = "")
	{
		string text = " SELECT T1.Fid,T2.Fid AS FCid2,T2.FCid3,T2.FName AS FAreaName,T1.FCookieId,T1.FLivePath,T1.FRtmp,T1.FError,T1.FLiveLog FROM TCookiesLive T1 ";
		text += " LEFT JOIN TArea T2 ON T2.Fid=T1.FAreaId";
		text = text + " WHERE T1.FUserId=" + userId + " AND T1.FCookieId IN (" + cookieId + ") AND T1.FAreaId=" + areaId;
		text += " UPDATE TCookiesLive SET FRtmp='',FError=''";
		if (liveLog != "")
		{
			text = text + ",FLiveLog='" + liveLog + "'";
		}
		text = text + " WHERE FUserId=" + userId + " AND FCookieId IN (" + cookieId + ") AND FAreaId=" + areaId;
		return SQLHelper.DouYuLocalDB.RunSqlDt(text);
	}

	public static DataTable GetLiveConfigList(string cookieId, string areaId, int userId)
	{
		string text = " SELECT 0 AS Fid,FRoomNo,FGiftName,FGiftCode,FGiftPrice,FGiftNum,FGiftSkinId,FBulletScreenNum,FWatchMinutes";
		text = text + " FROM TCookiesLiveConfig WHERE FUserId=" + userId + " AND FCookieId=" + cookieId + " AND FAreaId=" + areaId;
		text += " ORDER BY FRoomNo";
		return SQLHelper.DouYuLocalDB.RunSqlDt(text);
	}

	public static DataTable GetLiveGiftList(string cookieId, string areaId, int userId)
	{
		string text = " SELECT FCookieId,FRoomNo,FGiftName,FGiftCode,FGiftNum,FGiftPrice,FGiftSkinId FROM TCookiesLiveConfig ";
		text = text + " WHERE FCookieId IN (" + cookieId + ") AND FAreaId=" + areaId + " AND FUserId=" + userId + " AND FGiftNum>0";
		return SQLHelper.DouYuLocalDB.RunSqlDt(text);
	}

	public static DataTable GetLiveBulletScreenList(string cookieId, string areaId, int userId)
	{
		string text = " SELECT 0 AS Fid,FContent";
		text = text + " FROM TCookiesLiveBulletScreen WHERE FUserId=" + userId + " AND FCookieId=" + cookieId + " AND FAreaId=" + areaId;
		return SQLHelper.DouYuLocalDB.RunSqlDt(text);
	}

	public static DataTable GetLiveBulletScreenAll(string cookieId, string areaId, int userId)
	{
		string text = " SELECT T1.FCookieId,T1.FRoomNo,T2.FContent FROM TCookiesLiveConfig T1,";
		text += " (SELECT FCookieId,FContent, ROW_NUMBER() OVER (PARTITION BY FCookieId  ORDER BY NEWID()) AS RowNumber  ";
		text = text + " FROM TCookiesLiveBulletScreen WHERE FUserId=" + userId + " AND FAreaId=" + areaId + " AND FCookieId IN (" + cookieId + ")) T2 ";
		text = text + " WHERE T1.FUserId=" + userId + " AND T1.FAreaId=" + areaId + " AND T1.FCookieId IN (" + cookieId + ") AND T1.FCookieId=T2.FCookieId  ";
		text += " AND T2.RowNumber<=T1.FBulletScreenNum ";
		text += " ORDER BY FCookieId,FRoomNo,NEWID()";
		return SQLHelper.DouYuLocalDB.RunSqlDt(text);
	}

	public static JObject GetLiveConfig(string cookieId, string areaId, int userId)
	{
		JObject jObject = new JObject { ["livePath"] = "" };
		string livePathList = GetLivePathList(cookieId, areaId, userId);
		if (livePathList != "")
		{
			string directoryName = Path.GetDirectoryName(Directory.GetCurrentDirectory() + "\\WWWRoot\\" + livePathList);
			JArray jArray = new JArray();
			if (directoryName != null)
			{
				string[] files = Directory.GetFiles(directoryName);
				string[] array = files;
				foreach (string text in array)
				{
					string text2 = text.Replace(Directory.GetCurrentDirectory() + "\\WWWRoot\\", "");
					jArray.Add(new JObject
					{
						["src"] = text2,
						["name"] = text2
					});
				}
			}
			jObject["livePath"] = jArray;
		}
		jObject["liveConfig"] = JArray.FromObject(GetLiveConfigList(cookieId, areaId, userId));
		jObject["liveBulletScreen"] = JArray.FromObject(GetLiveBulletScreenList(cookieId, areaId, userId));
		return jObject;
	}

	public static DataTable GetWatchMinutesAll(string cookieId, string areaId, int userId)
	{
		string text = " SELECT FCookieId,FRoomNo,FWatchMinutes+1 AS FWatchMinutes FROM TCookiesLiveConfig WHERE FWatchMinutes!=0 AND FUserId=" + userId;
		text = text + " AND FCookieId IN (" + cookieId + ") AND FAreaId =" + areaId;
		return SQLHelper.DouYuLocalDB.RunSqlDt(text);
	}
}
