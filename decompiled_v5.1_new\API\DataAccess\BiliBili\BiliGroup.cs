using System.Data;
using API.Common;

namespace API.DataAccess.BiliBili;

public class BiliGroup
{
	public static void SetOption(string id, string key, string areaId, string aricles, string mileage, int userId)
	{
		string text = " UPDATE TCookieArea SET FAreaId=FAreaId";
		string text2 = "";
		if (aricles != "")
		{
			text2 = text2 + " ,FAricles=" + aricles;
		}
		if (mileage != "")
		{
			text2 = text2 + ",FMileage=" + mileage;
		}
		text += text2;
		text = text + " WHERE  FUserId=" + userId;
		text = ((!(id != "")) ? (text + " AND FKey IN ('" + key.Replace(",", "','") + "') AND FAreaId=" + areaId) : (text + " AND Fid IN (" + id + ")"));
		SQLHelper.BiliLocalDB.RunSqlText(text);
	}

	public static DataTable GetCookiesList(string areaId, string search, int userId, int limit = 0, int offset = 0, string prop = "", string order = "")
	{
		areaId = ((areaId == "") ? "0" : areaId);
		string text = " SELECT T2.Fid,T1.FSort,T1.FKey,T1.FName,T3.FName AS FAreaName,T2.FMileage,T2.FAricles,T2.FExpirationTime ";
		text += " ,CASE ISNULL(T5.FCount,0) WHEN 0 THEN '请更新任务！' ELSE '' END AS FMessage";
		text = text + SQLHelper.total + " FROM TCookies T1";
		text += " INNER JOIN TCookieArea T2 ON T2.FKey=T1.FKey AND T2.FUserId=T1.FUserId";
		text += " LEFT JOIN TArea T3 ON T3.Fid=T2.FAreaId";
		text = text + " LEFT JOIN ( SELECT FCookieId,COUNT(*) AS FCount FROM TCookiesTask WHERE FAreaId=" + areaId + " GROUP BY FCookieId) T5 ON T5.FCookieId=T1.Fid";
		text = text + " WHERE T1.FEnable=1 AND T1.FUserId=" + userId + " AND T2.FAreaId=" + areaId;
		if (search != "")
		{
			text = text + " AND ( T1.FName LIKE '%" + search + "%')";
		}
		if (prop == "")
		{
			prop = "T1.FSort";
			order = "ASC";
		}
		return SQLHelper.BiliLocalDB.RunSqlDt(text, limit, offset, prop, order);
	}

	public static void Del(int userId)
	{
		string sSql = " DELETE TCookieArea WHERE FKey IN (SELECT FKey FROM TCookies WHERE FUserId=" + userId + ")";
		SQLHelper.BiliLocalDB.RunSqlText(sSql);
	}

	public static void Matching(int userId)
	{
		string text = " UPDATE TCookieArea SET FAricles=TT.FAricles,FMileage=0 FROM (";
		text += " SELECT B3.FKey,B1.FAreaId,1 AS FAricles FROM TCookiesTask B1";
		text += " LEFT JOIN TCookies B3 ON B3.Fid=B1.FCookieId  AND B1.FUserId=B3.FUserId";
		text = text + " LEFT JOIN TAreaTask B2 ON B2.Fid=B1.FAreaTaskId WHERE B2.FType='投稿' AND B1.FReceiveStatus IN (1,3) AND B1.FUserId=" + userId;
		text += " GROUP BY B3.FKey,B1.FAreaId) TT WHERE TT.FKey=TCookieArea.FKey AND TT.FAreaId=TCookieArea.FAreaId";
		text += " UPDATE TCookieArea SET FMileage=TT.FMileage FROM (";
		text += " SELECT B3.FKey,B1.FAreaId,1 AS FMileage FROM TCookiesTask B1";
		text += " LEFT JOIN TCookies B3 ON B3.Fid=B1.FCookieId  AND B1.FUserId=B3.FUserId";
		text = text + " LEFT JOIN TAreaTask B2 ON B2.Fid=B1.FAreaTaskId WHERE B2.FType='里程' AND B1.FReceiveStatus IN (1,3) AND B1.FUserId=" + userId;
		text += " GROUP BY B3.FKey,B1.FAreaId) TT WHERE TT.FKey=TCookieArea.FKey AND TT.FAreaId=TCookieArea.FAreaId";
		text += " UPDATE TCookieArea SET FAricles=0 FROM (";
		text += " SELECT T3.FKey,T1.FAreaId FROM TCookiesTask T1 LEFT JOIN TAreaTask T2 ON T2.Fid=T1.FAreaTaskId";
		text = text + " LEFT JOIN TCookies T3 ON T3.Fid=T1.FCookieId WHERE T2.FType='投稿' AND T1.FUserId=" + userId;
		text += " GROUP BY T3.FKey,T1.FAreaId HAVING COUNT(*)=SUM((CASE T1.FReceiveStatus WHEN 0 THEN 1 ELSE 0 END))) TT";
		text += " WHERE TT.FKey=TCookieArea.FKey AND TT.FAreaId=TCookieArea.FAreaId";
		text += " UPDATE TCookieArea SET FMileage=0 FROM (";
		text += " SELECT T3.FKey,T1.FAreaId FROM TCookiesTask T1 LEFT JOIN TAreaTask T2 ON T2.Fid=T1.FAreaTaskId";
		text = text + " LEFT JOIN TCookies T3 ON T3.Fid=T1.FCookieId WHERE T2.FType='里程' AND T1.FUserId=" + userId;
		text += " GROUP BY T3.FKey,T1.FAreaId HAVING COUNT(*)=SUM((CASE T1.FReceiveStatus WHEN 0 THEN 1 ELSE 0 END))) TT";
		text += " WHERE TT.FKey=TCookieArea.FKey AND TT.FAreaId=TCookieArea.FAreaId";
		text += " UPDATE TCookieArea SET FAricles=0 FROM (";
		text += " SELECT T1.Fid,ISNULL(T2.FCount,0) AS FCount FROM TArea T1 LEFT JOIN ";
		text += " (SELECT FAreaId,COUNT(*) AS FCount FROM TAreaTask  WHERE FType='投稿' GROUP BY FAreaId) T2 ON T2.FAreaId=T1.Fid";
		text += " WHERE ISNULL(T2.FCount,0)=0 ) TT WHERE TT.Fid=TCookieArea.FAreaId";
		text += " UPDATE TCookieArea SET FMileage=0 FROM (";
		text += " SELECT T1.Fid,ISNULL(T2.FCount,0) AS FCount FROM TArea T1 LEFT JOIN ";
		text += " (SELECT FAreaId,COUNT(*) AS FCount FROM TAreaTask  WHERE FType='里程' GROUP BY FAreaId) T2 ON T2.FAreaId=T1.Fid";
		text += " WHERE ISNULL(T2.FCount,0)=0 ) TT WHERE TT.Fid=TCookieArea.FAreaId";
		SQLHelper.BiliLocalDB.RunSqlText(text);
	}

	public static void Complete(int userId)
	{
		string text = " DELETE TCookieArea WHERE FKey NOT IN ( SELECT FKey FROM TCookies WHERE FUserId=" + userId + ")";
		text += " INSERT TCookieArea(FUserId,FKey,FAreaId,FMileage,FAricles,FExpirationTime)";
		text += " SELECT T1.FUserId,T1.FKey,T1.FAreaId,T1.FMileage,T1.FAricles,T1.FExpirationTime FROM ";
		text = text + " (SELECT " + userId + " AS FUserId,T1.FKey,T2.Fid AS FAreaId,1 AS FMileage,1 AS FAricles,T1.FExpirationTime FROM TCookies T1,TArea T2 WHERE T1.FUserId=" + userId + ") T1";
		text = text + " LEFT JOIN TCookieArea T2 ON T2.FUserId=T1.FUserId AND T2.FAreaId=T1.FAreaId AND T2.FKey=T1.FKey WHERE T2.Fid IS NULL AND T1.FUserId=" + userId;
		text += " UPDATE TCookieArea SET FExpirationTime=T1.FExpirationTime FROM ";
		text = text + " (SELECT 1 AS FUserId,T1.FKey,T2.Fid AS FAreaId,1 AS FMileage,1 AS FAricles,T1.FExpirationTime FROM TCookies T1,TArea T2 WHERE T1.FUserId=" + userId + ") T1";
		text = text + " WHERE T1.FUserId=TCookieArea.FUserId AND T1.FKey=TCookieArea.FKey AND T1.FAreaId=TCookieArea.FAreaId AND TCookieArea.FUserId=" + userId;
		SQLHelper.BiliLocalDB.RunSqlText(text);
	}
}
