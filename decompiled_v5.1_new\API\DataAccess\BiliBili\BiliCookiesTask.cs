using System;
using System.Data;
using API.Common;
using API.Models.Comm;
using Microsoft.Data.SqlClient;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace API.DataAccess.BiliBili;

public class BiliCookiesTask
{
	public static DataTable GetCookiesTaskWriteLog(string cookieId, string taskKey)
	{
		string text = " SELECT T2.FName AS FAreaName,T1.FTaskName,T3.FName AS FCookieName FROM TCookiesTask T1 ";
		text += " LEFT JOIN TArea T2 ON T2.Fid=T1.FAreaId";
		text += " LEFT JOIN TCookies T3 ON T3.Fid=T1.FCookieId";
		text = text + " WHERE T1.FCookieId=" + cookieId;
		text = text + " AND T1.FAreaTaskId IN (SELECT Fid FROM TAreaTask WHERE FTaskKey IN (" + taskKey + "))";
		return SQLHelper.BiliLocalDB.RunSqlDt(text);
	}

	public static void SaveTask(JObject jObject, int userId)
	{
		string jObject2 = Util.GetJObject(jObject, "areaId");
		string jObject3 = Util.GetJObject(jObject, "cookieId");
		DataTable areaTaskList = BiliAreaTask.GetAreaTaskList("", jObject2);
		string sSql = " SELECT FType FROM TArea WHERE Fid=" + jObject2;
		string text = SQLHelper.BiliLocalDB.RunSqlStr(sSql);
		JArray jArray = Util.GetJObject<JArray>(jObject, "rows") ?? new JArray();
		JArray jArray2 = new JArray();
		string text2 = "0";
		for (int i = 0; i < jArray.Count; i++)
		{
			JToken jToken = jArray[i];
			string jObject4 = Util.GetJObject(jToken, "key");
			DataRow[] array = areaTaskList.Select("FAreaId=" + jObject2 + " AND FTaskKey='" + jObject4 + "'");
			if (array == null || array.Length != 1)
			{
				break;
			}
			DataTable dataTable = array.CopyToDataTable();
			text2 = text2 + "," + dataTable.Rows[0]["Fid"].ToString();
			JObject jObject6;
			if (text == "方式一")
			{
				string text3 = Util.GetJObject(jToken, "status") switch
				{
					"6" => "3", 
					"11" => "1", 
					"2" => "1", 
					"0" => "1", 
					_ => "0", 
				};
				if (!Util.GetJObject<bool>(jToken, "task_finished") && text3 == "1")
				{
					text3 = "0";
				}
				JToken jObject5 = Util.GetJObject<JToken>(jToken, "reward_info");
				jObject6 = new JObject
				{
					["FUserId"] = userId,
					["FAreaId"] = jObject2,
					["FCookieId"] = jObject3,
					["FAreaTaskId"] = dataTable.Rows[0]["Fid"].ToString() ?? throw new Exception("参数不正确！"),
					["FReceiveFrom"] = "",
					["FTaskId"] = Util.GetJObject(jToken, "task_id"),
					["FTaskName"] = Util.GetJObject(jToken, "task_name") + ((Util.GetJObject(jToken, "task_desc") == "") ? "" : ("-" + Util.GetJObject(jToken, "task_desc"))),
					["FReceiveId"] = 0,
					["FReceiveStatus"] = text3,
					["FAwardId"] = Util.GetJObject(jObject5, "award_inner_id"),
					["FAwardName"] = Util.GetJObject(jObject5, "award_name"),
					["FActivityId"] = Util.GetJObject(jToken, "act_id"),
					["FActName"] = Util.GetJObject(jToken, "act_name"),
					["FActId"] = 0,
					["FGroupId"] = 0,
					["FStockTotal"] = 0,
					["FStockConsumed"] = 0,
					["FPeriodTotal"] = 0,
					["FPeriodConsumed"] = 0,
					["FPrice"] = dataTable.Rows[0]["FPrice"].ToString() ?? "0",
					["FCompulsory"] = dataTable.Rows[0]["FCompulsory"].ToString() ?? "0",
					["FComplete"] = "",
					["FDaily"] = dataTable.Rows[0]["FDaily"].ToString() ?? "0",
					["FSort"] = dataTable.Rows[0]["FSort"].ToString() ?? "0",
					["FEnable"] = dataTable.Rows[0]["FEnable"].ToString() ?? "0"
				};
				string jObject7 = Util.GetJObject(jObject5, "award_description");
				JToken jObject8 = Util.GetJObject<JToken>(jToken, "stock_info");
				string jObject9 = Util.GetJObject(jObject8, "total_stock");
				string jObject10 = Util.GetJObject(jObject8, "day_stock");
				string text4 = Util.Regex(jObject7, "本奖品共", "份");
				string text5 = Util.Regex(jObject7, "每日限量发放", "份");
				text4 = ((text4 != "") ? text4 : jObject9);
				text5 = ((text5 != "") ? text5 : jObject10);
				int num = int.Parse(text4) - int.Parse(text4) * int.Parse(jObject9) / 100;
				int num2 = int.Parse(text5) - int.Parse(text5) * int.Parse(jObject10) / 100;
				jObject6["FStockTotal"] = text4;
				jObject6["FStockConsumed"] = num;
				jObject6["FPeriodTotal"] = text5;
				jObject6["FPeriodConsumed"] = num2;
				if (dataTable.Rows[0]["FComplete"].ToString() == "1")
				{
					jObject6["FComplete"] = Util.GetJObject(jToken, "cur_value");
				}
				jArray2.Add(jObject6);
				continue;
			}
			JToken jObject11 = Util.GetJObject<JToken>(jToken, "task_info");
			JToken jObject12 = Util.GetJObject<JToken>(jObject11, "reward_info");
			JToken jObject13 = Util.GetJObject<JToken>(jToken, "act_info");
			JArray jObject14 = Util.GetJObject<JArray>(jObject11, "reward_stock_configs");
			jObject6 = new JObject
			{
				["FUserId"] = userId,
				["FAreaId"] = jObject2,
				["FCookieId"] = jObject3,
				["FAreaTaskId"] = dataTable.Rows[0]["Fid"].ToString() ?? throw new Exception("参数不正确！"),
				["FReceiveFrom"] = dataTable.Rows[0]["FReceiveFrom"].ToString() ?? throw new Exception("参数不正确！"),
				["FTaskId"] = Util.GetJObject(jObject11, "id"),
				["FTaskName"] = Util.GetJObject(jObject11, "task_name"),
				["FReceiveId"] = Util.GetJObject(jObject11, "receive_id"),
				["FReceiveStatus"] = Util.GetJObject(jObject11, "receive_status"),
				["FAwardId"] = Util.GetJObject(jObject12, "reward_id"),
				["FAwardName"] = Util.GetJObject(jObject12, "reward_name"),
				["FActivityId"] = Util.GetJObject(jObject12, "reward_act_id"),
				["FActName"] = Util.GetJObject(jObject13, "act_name"),
				["FActId"] = Util.GetJObject(jObject13, "id"),
				["FGroupId"] = 0,
				["FStockTotal"] = 0,
				["FStockConsumed"] = 0,
				["FPeriodTotal"] = 0,
				["FPeriodConsumed"] = 0,
				["FPrice"] = dataTable.Rows[0]["FPrice"].ToString() ?? "0",
				["FCompulsory"] = dataTable.Rows[0]["FCompulsory"].ToString() ?? "0",
				["FComplete"] = "",
				["FDaily"] = dataTable.Rows[0]["FDaily"].ToString() ?? "0",
				["FSort"] = dataTable.Rows[0]["FSort"].ToString() ?? "0",
				["FEnable"] = dataTable.Rows[0]["FEnable"].ToString() ?? "0"
			};
			if (dataTable.Rows[0]["FComplete"].ToString() == "1")
			{
				JArray jObject15 = Util.GetJObject<JArray>(jObject11, "group_list");
				if (jObject15 != null && jObject15.Count > 0)
				{
					jObject6["FComplete"] = Util.GetJObject(jObject15[0], "group_complete_num");
					jObject6["FGroupId"] = Util.GetJObject(jObject15[0], "group_id");
				}
			}
			if (jObject14 != null)
			{
				for (int j = 0; j < jObject14.Count; j++)
				{
					string jObject16 = Util.GetJObject(jObject14[j], "cycle_type");
					if (jObject16 == "1")
					{
						jObject6["FStockTotal"] = Util.GetJObject(jObject14[j], "total");
						jObject6["FStockConsumed"] = Util.GetJObject(jObject14[j], "consumed");
					}
					else if (jObject16 == "2")
					{
						jObject6["FPeriodTotal"] = Util.GetJObject(jObject14[j], "total");
						jObject6["FPeriodConsumed"] = Util.GetJObject(jObject14[j], "consumed");
					}
				}
			}
			jArray2.Add(jObject6);
		}
		if (jArray2.Count > 0)
		{
			DataTable dt = jArray2.ToObject<DataTable>();
			UpdateCookiesTask(dt, jObject3, jObject2, userId, (jArray2.Count == 1) ? text2 : "");
		}
	}

	public static void RestoreDefault(string cookieId, string areaId, int userId, string userName, string curTime)
	{
		SqlConnection sqlConnection = SQLHelper.BiliLocalDB.InitCnn();
		SqlCommand sqlCommand = sqlConnection.CreateCommand();
		sqlCommand.Transaction = sqlConnection.BeginTransaction();
		try
		{
			string text = " UPDATE TCookiesTask SET FCompulsory=TT.FCompulsory,FSort=TT.FSort,FEnable=TT.FEnable";
			text += " FROM TAreaTask TT WHERE TT.Fid=TCookiesTask.FAreaTaskId AND TT.FAreaId=TCookiesTask.FAreaId";
			text = text + " AND TCookiesTask.FUserId=" + userId + " AND TCookiesTask.FCookieId=" + cookieId;
			if (areaId != "")
			{
				text = text + " AND TT.FAreaId=" + areaId;
			}
			SQLHelper.BiliLocalDB.RunSqlText(text);
			DataCURD.WriteLog("TCookiesTask", "恢复默认值", "FAreaTaskId", "", JsonConvert.SerializeObject(new JObject
			{
				["type"] = "编辑",
				["data"] = new JArray()
			}), userId, userName, curTime, sqlCommand);
			sqlCommand.Transaction.Commit();
		}
		catch (Exception ex)
		{
			sqlCommand.Transaction.Rollback();
			throw new Exception(ex.Message);
		}
		finally
		{
			sqlConnection.Close();
			sqlConnection.Dispose();
		}
	}

	public static void SyncAll(string cookieId, string areaId, int userId, string userName, string curTime)
	{
		SqlConnection sqlConnection = SQLHelper.BiliLocalDB.InitCnn();
		SqlCommand sqlCommand = sqlConnection.CreateCommand();
		sqlCommand.Transaction = sqlConnection.BeginTransaction();
		try
		{
			string text = " UPDATE T1 SET T1.FCompulsory=TT.FCompulsory,T1.FSort=TT.FSort,T1.FEnable=TT.FEnable";
			text += " FROM TCookiesTask T1,TCookiesTask TT WHERE TT.FAreaTaskId=T1.FAreaTaskId AND TT.FAreaId=T1.FAreaId";
			text = text + " AND TT.FUserId=" + userId + " AND TT.FCookieId=" + cookieId;
			if (areaId != "")
			{
				text = text + " AND TT.FAreaId=" + areaId;
			}
			SQLHelper.BiliLocalDB.RunSqlText(text);
			text = " UPDATE T1 SET T1.FSort=TT.FSort";
			text += " FROM TAreaTask T1,TCookiesTask TT WHERE TT.FAreaTaskId=T1.Fid AND TT.FAreaId=T1.FAreaId";
			text = text + " AND TT.FUserId=" + userId + " AND TT.FCookieId=" + cookieId;
			if (areaId != "")
			{
				text = text + " AND TT.FAreaId=" + areaId;
			}
			SQLHelper.BiliLocalDB.RunSqlText(text);
			DataCURD.WriteLog("TCookiesTask", "同步所有", "FAreaTaskId", "", JsonConvert.SerializeObject(new JObject
			{
				["type"] = "编辑",
				["data"] = new JArray()
			}), userId, userName, curTime, sqlCommand);
			sqlCommand.Transaction.Commit();
		}
		catch (Exception ex)
		{
			sqlCommand.Transaction.Rollback();
			throw new Exception(ex.Message);
		}
		finally
		{
			sqlConnection.Close();
			sqlConnection.Dispose();
		}
	}

	public static void DelAll(string cookieId, string areaId, string id, int userId, string userName, string curTime)
	{
		SqlConnection sqlConnection = SQLHelper.BiliLocalDB.InitCnn();
		SqlCommand sqlCommand = sqlConnection.CreateCommand();
		sqlCommand.Transaction = sqlConnection.BeginTransaction();
		try
		{
			if (areaId == "")
			{
				throw new Exception("请选择分区！");
			}
			string text = "FUserId,FCookieId,FAreaId";
			string text2 = userId + "," + cookieId + "," + areaId;
			if (id != "")
			{
				text += ",Fid";
				text2 = text2 + "," + id;
			}
			DataCURD.Delete("TCookiesTask", "删除任务", text, text2, userId, userName, curTime, sqlCommand);
			sqlCommand.Transaction.Commit();
		}
		catch (Exception ex)
		{
			sqlCommand.Transaction.Rollback();
			throw new Exception(ex.Message);
		}
		finally
		{
			sqlConnection.Close();
			sqlConnection.Dispose();
		}
	}

	public static void UpdateCookiesTask(DataTable? dt, string cookieId, string areaId, int userId, string areaTaskId)
	{
		if (dt == null || dt.Rows.Count <= 0)
		{
			return;
		}
		SqlConnection sqlConnection = SQLHelper.BiliLocalDB.InitCnn();
		SqlCommand sqlCommand = sqlConnection.CreateCommand();
		sqlCommand.Transaction = sqlConnection.BeginTransaction();
		try
		{
			string text = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss:fff");
			SQLHelper.RunTableLockx("TCookiesTask", sqlCommand);
			string text2 = " UPDATE TCookiesTask SET FDate='" + text + "' WHERE FCookieId IN (" + cookieId + ") AND FAreaId=" + areaId + " AND FUserId=" + userId;
			if (areaTaskId != "")
			{
				text2 = text2 + " AND FAreaTaskId IN (" + areaTaskId + ")";
			}
			SQLHelper.RunSqlText(text2, sqlCommand);
			DataCURD.Delete("TCookiesTask", "定时任务批量操作", "FDate", text, userId, "定时任务", text, sqlCommand);
			SQLHelper.SqlBulkCopyByDataTable("TCookiesTask", dt, sqlCommand);
			sqlCommand.Transaction.Commit();
		}
		catch (Exception ex)
		{
			sqlCommand?.Transaction?.Rollback();
			throw new Exception(ex.Message);
		}
		finally
		{
			sqlConnection.Close();
			sqlConnection.Dispose();
		}
	}

	public static DataTable GetCookiesTaskList(string cookieId, string areaId, int userId, int limit = 0, int offset = 0, string prop = "", string order = "")
	{
		string text = " SELECT CASE T1.FCompulsory WHEN 1 THEN '√' ELSE '' END AS FCompulsoryName,CASE T1.FEnable WHEN 1 THEN '已启用' ELSE '已禁用' END FEnableName";
		text += " ,CASE T1.FDaily WHEN 1 THEN '√' ELSE '' END AS FDailyName,CASE T1.FComplete WHEN 1 THEN '√' ELSE '' END AS FCompleteName";
		text += " ,CASE T1.FReceiveStatus WHEN 1 THEN '√' WHEN 3 THEN '√√' WHEN 0 THEN '' ELSE '×' END AS FReceiveStatusName";
		text += " ,CASE  WHEN T1.FReceiveStatus = 3 THEN -5 WHEN T1.FEnable = 0 THEN -10 WHEN T1.FReceiveStatus NOT IN (0,1,3) THEN -20 ";
		text += "       WHEN T1.FStockTotal - T1.FStockConsumed = 0 THEN -8 WHEN T1.FCompulsory = 1 AND T1.FDaily = 0 THEN 20";
		text += "       WHEN T1.FReceiveStatus = 0 AND T1.FDaily = 0 THEN 5 WHEN T1.FReceiveStatus = 1 AND T1.FDaily = 0 THEN 10";
		text += "       WHEN T1.FDaily = 1 THEN 0 END AS FStatus";
		text += " ,T2.FName AS FAreaName";
		text = text + " ,T1.* " + SQLHelper.total + " FROM TCookiesTask T1 LEFT JOIN TArea T2 ON T2.Fid=T1.FAreaId";
		text = text + " WHERE 1=1 AND T1.FCookieId=" + cookieId + " AND T1.FUserId=" + userId;
		if (areaId != "")
		{
			text = text + " AND T1.FAreaId IN(" + areaId + ")";
		}
		if (prop == "")
		{
			prop = " FStatus DESC,T1.FSort";
			order = "ASC";
		}
		return SQLHelper.BiliLocalDB.RunSqlDt(text, limit, offset, prop, order);
	}

	public static void SetCompulsory(JObject jObject, int userId, string userName, string curTime)
	{
		DataCURD.Save(jObject, "TCookiesTask", "设置或取消强制任务", "Fid", userId, userName, curTime, SQLHelper.BiliLocalDB.InitCnn());
	}

	public static void SetSort(JObject jObject, int userId, string userName, string curTime)
	{
		DataCURD.Save(jObject, "TCookiesTask", "设置任务排序", "Fid", userId, userName, curTime, SQLHelper.BiliLocalDB.InitCnn());
	}

	public static int EnableSwitch(JObject jObject, int userId, string userName, string curTime)
	{
		string jObject2 = Util.GetJObject(jObject, "Fid");
		string jObject3 = Util.GetJObject(jObject, "FEnable");
		string text = Util.GetJObject(jObject, "FCompulsory");
		if (jObject3 != "1")
		{
			text = "0";
		}
		DataCURD.Save(new JObject
		{
			["Fid"] = jObject2,
			["FEnable"] = jObject3,
			["FCompulsory"] = text
		}, "TCookiesTask", "启动禁用任务", "Fid", userId, userName, curTime, SQLHelper.BiliLocalDB.InitCnn());
		return int.Parse(jObject3);
	}

	public static DataTable GetReceiveTaskList(string areaId, string cookieId, TaskType taskType, int userId)
	{
		string text = " SELECT T1.FAreaId,T1.FUserId,T1.FCookieId,T1.FActId,T1.FAwardId,T1.FActName,T1.FTaskId,T1.FTaskName,T1.FReceiveFrom,T1.FReceiveId,T1.FAwardName,T1.FGroupId";
		text += " ,T1.FReceiveStatus,T1.FDaily,T1.FCompulsory,T2.FTaskKey,T2.FTaskUrl,T3.FReceiveUrl,T3.FType,T1.FActivityId,T2.FReceiveFrom";
		text += " FROM TCookiesTask T1";
		text += " LEFT JOIN TAreaTask T2 ON T2.Fid=T1.FAreaTaskId";
		text += " LEFT JOIN TArea T3 ON T3.Fid=T1.FAreaId";
		text = text + " WHERE T1.FEnable=1 AND T1.FStockTotal!=T1.FStockConsumed AND T1.FUserId=" + userId;
		if (areaId != "" && areaId != "0")
		{
			text = text + " AND T1.FAreaId IN (" + areaId + ")";
		}
		if (cookieId != "")
		{
			text = text + " AND T1.FCookieId IN (" + cookieId + ")";
		}
		switch (taskType)
		{
		case TaskType.Normal:
			text += " AND T1.FDaily=0 AND T1.FReceiveStatus=1";
			break;
		case TaskType.Daily:
			text += " AND T1.FDaily=1";
			break;
		case TaskType.Compulsory:
			text += " AND T1.FDaily=0 AND T1.FCompulsory=1";
			break;
		}
		text += "ORDER BY T1.FCompulsory DESC,T1.FSort";
		return SQLHelper.BiliLocalDB.RunSqlDt(text);
	}
}
