using System;
using System.Threading.Tasks;
using API.BusService.System;
using API.Common;
using API.DataAccess.DouYu;
using API.Models.Comm;
using Microsoft.AspNetCore.Mvc;

namespace API.Controllers.DouYu;

public class DouYuInterfaceController : Controller
{
	[HttpPost]
	public Response GetInterfaceList([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectSearch, "name");
			string jObject2 = Util.GetJObject(model.jObjectSearch, "search");
			string jObject3 = Util.GetJObject(model.jObjectSearch, "field");
			response.data = DouYuInterface.GetInterfaceList(jObject, jObject2, jObject3);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public async Task<Response> UpdateInterface([FromBody] Request model)
	{
		Response mRet = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "name");
			await DouYuInterface.UpdateInterface(jObject, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}
}
