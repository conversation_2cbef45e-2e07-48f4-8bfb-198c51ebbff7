using System;
using System.Data;
using System.Diagnostics;
using System.IO;
using Microsoft.Data.SqlClient;
using Newtonsoft.Json.Linq;

namespace API.Common;

public class SQLHelper
{
	private static string? _connStr;

	public static readonly string total = ",COUNT(*) OVER() AS total";

	public static readonly string tablock = " WITH(TABLOCKX)";

	public static SQLHelper LocalDB
	{
		get
		{
			_connStr = AppSettings.GetVal("LocalDB").Replace("{CurDir}", Directory.GetCurrentDirectory());
			return new SQLHelper();
		}
	}

	public static SQLHelper BiliLocalDB
	{
		get
		{
			_connStr = AppSettings.GetVal("BiliLocalDB").Replace("{CurDir}", Directory.GetCurrentDirectory());
			return new SQLHelper();
		}
	}

	public static SQLHelper DouYuLocalDB
	{
		get
		{
			_connStr = AppSettings.GetVal("DouYuLocalDB").Replace("{CurDir}", Directory.GetCurrentDirectory());
			return new SQLHelper();
		}
	}

	public static SQLHelper KSLocalDB
	{
		get
		{
			_connStr = AppSettings.GetVal("KSLocalDB").Replace("{CurDir}", Directory.GetCurrentDirectory());
			return new SQLHelper();
		}
	}

	public static SQLHelper DouYinLocalDB
	{
		get
		{
			_connStr = AppSettings.GetVal("DouYinLocalDB").Replace("{CurDir}", Directory.GetCurrentDirectory());
			return new SQLHelper();
		}
	}

	public JObject RunExist(string name)
	{
		JObject jObject = new JObject { ["name"] = name };
		Stopwatch stopwatch = Stopwatch.StartNew();
		using SqlConnection sqlConnection = new SqlConnection(_connStr);
		try
		{
			sqlConnection.Open();
			SqlCommand sqlCommand = new SqlCommand("SELECT 1", sqlConnection);
			object obj = sqlCommand.ExecuteScalar();
			sqlConnection.Close();
			jObject["type"] = "success";
		}
		catch
		{
			jObject["type"] = "danger";
		}
		finally
		{
			sqlConnection.Close();
			sqlConnection.Dispose();
		}
		stopwatch.Stop();
		jObject["milliseconds"] = stopwatch.ElapsedMilliseconds;
		if (stopwatch.ElapsedMilliseconds > 1000 && Util.GetJObject(jObject, "type") == "success")
		{
			jObject["type"] = "warning";
		}
		return jObject;
	}

	public SqlConnection InitCnn()
	{
		SqlConnection sqlConnection = new SqlConnection(_connStr);
		sqlConnection.Open();
		return sqlConnection;
	}

	public static int RunTableLockx(string table, SqlCommand pCmd)
	{
		return RunSqlText("SELECT TOP 1 1 FROM " + table + " WITH (TABLOCKX)", pCmd);
	}

	public int RunSqlText(string sSql)
	{
		using SqlConnection sqlConnection = new SqlConnection(_connStr);
		try
		{
			sqlConnection.Open();
			SqlCommand sqlCommand = new SqlCommand(sSql, sqlConnection);
			int result = sqlCommand.ExecuteNonQuery();
			sqlConnection.Close();
			return result;
		}
		catch (Exception ex)
		{
			throw new Exception(ex.Message);
		}
		finally
		{
			sqlConnection.Close();
			sqlConnection.Dispose();
		}
	}

	public static int RunSqlText(string sSql, SqlCommand pCmd)
	{
		try
		{
			pCmd.CommandText = sSql;
			pCmd.CommandType = CommandType.Text;
			int result = pCmd.ExecuteNonQuery();
			pCmd.Parameters.Clear();
			return result;
		}
		catch (Exception ex)
		{
			throw new Exception(ex.Message);
		}
	}

	public int RunSqlText(string sSql, SqlParameter[] prams)
	{
		using SqlConnection sqlConnection = new SqlConnection(_connStr);
		try
		{
			sqlConnection.Open();
			SqlCommand sqlCommand = new SqlCommand
			{
				Connection = sqlConnection,
				CommandText = sSql,
				CommandType = CommandType.Text
			};
			if (prams != null)
			{
				foreach (SqlParameter value in prams)
				{
					sqlCommand.Parameters.Add(value);
				}
			}
			return sqlCommand.ExecuteNonQuery();
		}
		catch (Exception ex)
		{
			throw new Exception(ex.Message);
		}
		finally
		{
			sqlConnection.Close();
			sqlConnection.Dispose();
		}
	}

	public string RunSqlStr(string sSql)
	{
		using SqlConnection sqlConnection = new SqlConnection(_connStr);
		try
		{
			sqlConnection.Open();
			SqlCommand sqlCommand = new SqlCommand(sSql, sqlConnection);
			object obj = sqlCommand.ExecuteScalar();
			sqlConnection.Close();
			return obj?.ToString() ?? "";
		}
		catch (Exception ex)
		{
			throw new Exception(ex.Message);
		}
		finally
		{
			sqlConnection.Close();
			sqlConnection.Dispose();
		}
	}

	public static string RunSqlStr(string sSql, SqlCommand pCmd)
	{
		try
		{
			pCmd.CommandText = sSql;
			pCmd.CommandType = CommandType.Text;
			object obj = pCmd.ExecuteScalar();
			pCmd.Parameters.Clear();
			return obj?.ToString() ?? "";
		}
		catch (Exception ex)
		{
			throw new Exception(ex.Message);
		}
	}

	public string RunSqlStr(string sSql, SqlParameter[] prams)
	{
		using SqlConnection sqlConnection = new SqlConnection(_connStr);
		try
		{
			sqlConnection.Open();
			SqlCommand sqlCommand = new SqlCommand
			{
				Connection = sqlConnection,
				CommandText = sSql,
				CommandType = CommandType.Text
			};
			if (prams != null)
			{
				foreach (SqlParameter value in prams)
				{
					sqlCommand.Parameters.Add(value);
				}
			}
			return sqlCommand.ExecuteScalar()?.ToString() ?? "";
		}
		catch (Exception ex)
		{
			throw new Exception(ex.Message);
		}
		finally
		{
			sqlConnection.Close();
			sqlConnection.Dispose();
		}
	}

	public DataTable RunSqlDt(string sSql)
	{
		DataTable dataTable = new DataTable();
		using SqlConnection sqlConnection = new SqlConnection(_connStr);
		try
		{
			if (sqlConnection.State == ConnectionState.Closed)
			{
				sqlConnection.Open();
			}
			if (sqlConnection.State == ConnectionState.Open || sqlConnection.State == ConnectionState.Connecting)
			{
				SqlDataAdapter sqlDataAdapter = new SqlDataAdapter(sSql, sqlConnection);
				sqlDataAdapter.Fill(dataTable);
			}
			sqlConnection.Close();
			return dataTable;
		}
		catch (Exception ex)
		{
			throw new Exception(ex.Message);
		}
		finally
		{
			sqlConnection.Close();
			sqlConnection.Dispose();
		}
	}

	public static DataTable RunSqlDt(string sSql, SqlCommand pCmd)
	{
		try
		{
			pCmd.CommandText = sSql;
			pCmd.CommandType = CommandType.Text;
			SqlDataAdapter sqlDataAdapter = new SqlDataAdapter(pCmd);
			DataTable dataTable = new DataTable();
			sqlDataAdapter.Fill(dataTable);
			pCmd.Parameters.Clear();
			return dataTable;
		}
		catch (Exception ex)
		{
			throw new Exception(ex.Message);
		}
	}

	public DataTable RunSqlDt(string sSql, SqlParameter[] prams)
	{
		using SqlConnection sqlConnection = new SqlConnection(_connStr);
		try
		{
			sqlConnection.Open();
			DataTable dataTable = new DataTable();
			SqlCommand sqlCommand = new SqlCommand
			{
				Connection = sqlConnection,
				CommandText = sSql,
				CommandType = CommandType.Text
			};
			if (prams != null)
			{
				foreach (SqlParameter value in prams)
				{
					sqlCommand.Parameters.Add(value);
				}
			}
			SqlDataAdapter sqlDataAdapter = new SqlDataAdapter(sqlCommand);
			sqlDataAdapter.Fill(dataTable);
			return dataTable;
		}
		catch (Exception ex)
		{
			throw new Exception(ex.Message);
		}
		finally
		{
			sqlConnection.Close();
			sqlConnection.Dispose();
		}
	}

	public static DataTable RunSqlDt(string sSql, SqlParameter[] prams, SqlCommand pCmd)
	{
		try
		{
			DataTable dataTable = new DataTable();
			pCmd.CommandText = sSql;
			pCmd.CommandType = CommandType.Text;
			if (prams != null)
			{
				foreach (SqlParameter value in prams)
				{
					pCmd.Parameters.Add(value);
				}
			}
			SqlDataAdapter sqlDataAdapter = new SqlDataAdapter(pCmd);
			sqlDataAdapter.Fill(dataTable);
			pCmd.Parameters.Clear();
			return dataTable;
		}
		catch (Exception ex)
		{
			throw new Exception(ex.Message);
		}
	}

	public DataTable RunSqlDt(string sSql, int limit = 0, int offset = 0, string prop = "", string order = "")
	{
		using SqlConnection sqlConnection = new SqlConnection(_connStr);
		try
		{
			if (prop != null && prop != "")
			{
				sSql = sSql + " ORDER BY " + prop + " " + order.Replace("ending", "");
			}
			else if (limit != 0)
			{
				sSql += " ORDER BY Fid DESC";
			}
			if (limit != 0)
			{
				sSql = sSql + " OFFSET " + offset + " ROW FETCH NEXT " + limit + " ROW ONLY ";
			}
			SqlDataAdapter sqlDataAdapter = new SqlDataAdapter(sSql, sqlConnection);
			DataTable dataTable = new DataTable();
			sqlDataAdapter.Fill(dataTable);
			return dataTable;
		}
		catch (Exception ex)
		{
			throw new Exception(ex.Message);
		}
		finally
		{
			sqlConnection.Close();
			sqlConnection.Dispose();
		}
	}

	public DataSet RunSqlDs(string sSql)
	{
		using SqlConnection sqlConnection = new SqlConnection(_connStr);
		try
		{
			SqlDataAdapter sqlDataAdapter = new SqlDataAdapter(sSql, sqlConnection);
			DataSet dataSet = new DataSet();
			sqlDataAdapter.Fill(dataSet);
			sqlConnection.Close();
			return dataSet;
		}
		catch (Exception ex)
		{
			throw new Exception(ex.Message);
		}
		finally
		{
			sqlConnection.Close();
			sqlConnection.Dispose();
		}
	}

	public DataSet RunSqlDs(string sSql, SqlParameter[] prams)
	{
		using SqlConnection sqlConnection = new SqlConnection(_connStr);
		try
		{
			sqlConnection.Open();
			DataSet dataSet = new DataSet();
			SqlCommand sqlCommand = new SqlCommand
			{
				Connection = sqlConnection,
				CommandText = sSql,
				CommandType = CommandType.Text
			};
			if (prams != null)
			{
				foreach (SqlParameter value in prams)
				{
					sqlCommand.Parameters.Add(value);
				}
			}
			SqlDataAdapter sqlDataAdapter = new SqlDataAdapter(sqlCommand);
			sqlDataAdapter.Fill(dataSet);
			return dataSet;
		}
		catch (Exception ex)
		{
			throw new Exception(ex.Message);
		}
		finally
		{
			sqlConnection.Close();
			sqlConnection.Dispose();
		}
	}

	public void RunSqlTrans(string[] sSql)
	{
		using SqlConnection sqlConnection = new SqlConnection(_connStr);
		sqlConnection.Open();
		SqlTransaction sqlTransaction = sqlConnection.BeginTransaction();
		try
		{
			for (int i = 0; i < sSql.Length; i++)
			{
				SqlCommand sqlCommand = new SqlCommand(sSql[i], sqlConnection, sqlTransaction);
				sqlCommand.ExecuteNonQuery();
			}
			sqlTransaction.Commit();
		}
		catch (Exception ex)
		{
			sqlTransaction.Rollback();
			throw new Exception(ex.Message);
		}
		finally
		{
			sqlConnection.Close();
			sqlConnection.Dispose();
		}
	}

	public static void SqlBulkCopyByDataTable(string dataTableName, DataTable? sourceDataTable, SqlCommand pCmd)
	{
		try
		{
			using SqlBulkCopy sqlBulkCopy = new SqlBulkCopy(pCmd.Connection, SqlBulkCopyOptions.Default, pCmd.Transaction);
			sqlBulkCopy.DestinationTableName = dataTableName;
			sqlBulkCopy.BatchSize = 100000;
			int num = 0;
			while (sourceDataTable != null && num < sourceDataTable.Columns.Count)
			{
				sqlBulkCopy.ColumnMappings.Add(sourceDataTable.Columns[num].ColumnName, sourceDataTable.Columns[num].ColumnName);
				num++;
			}
			sqlBulkCopy.WriteToServer(sourceDataTable);
		}
		catch (Exception ex)
		{
			throw new Exception(ex.Message);
		}
	}

	public void SqlBulkCopyByDataTable(string dataTableName, DataTable sourceDataTable)
	{
		using SqlConnection sqlConnection = new SqlConnection(_connStr);
		using SqlBulkCopy sqlBulkCopy = new SqlBulkCopy(_connStr, SqlBulkCopyOptions.UseInternalTransaction);
		try
		{
			sqlBulkCopy.DestinationTableName = dataTableName;
			sqlBulkCopy.BatchSize = 100000;
			for (int i = 0; i < sourceDataTable.Columns.Count; i++)
			{
				sqlBulkCopy.ColumnMappings.Add(sourceDataTable.Columns[i].ColumnName, sourceDataTable.Columns[i].ColumnName);
			}
			sqlBulkCopy.WriteToServer(sourceDataTable);
		}
		catch (Exception ex)
		{
			throw new Exception(ex.Message);
		}
		finally
		{
			sqlBulkCopy.Close();
			sqlConnection.Close();
			sqlConnection.Dispose();
		}
	}

	public static bool SqlFilter(string str, out string filterStr)
	{
		try
		{
			filterStr = "";
			str = str.ToLower();
			if (str != "")
			{
				string text = "select*|and'|or'|insertinto|deletefrom|altertable|createtable|createview|dropview|createindex|dropindex|createprocedure|dropprocedure|createtrigger|droptrigger|createschema|dropschema|createdomain|alterdomain|dropdomain|);|select@|declare@|print@|char(|select |and |join |backup |database |disk |exec |insert |select |delete |update |count |chr |mid |master |truncate |char |declare |drop |drop+table |creat+table |xp_cmdshell |or |tables | select| and| join| backup| database| disk| exec| insert| select| delete| update| count|c mid| master| truncate| declare| drop| drop+table| creat+table| xp_cmdshell| or| tables| script";
				string[] array = text.Split('|');
				if (!(str == "/*"))
				{
					if (str == "*/")
					{
						filterStr = "*/";
						return true;
					}
					string[] array2 = array;
					foreach (string text2 in array2)
					{
						if (str.Contains(text2))
						{
							filterStr = text2;
							return true;
						}
					}
					return false;
				}
				filterStr = "/*";
				return true;
			}
			return false;
		}
		catch (Exception ex)
		{
			filterStr = ex.Message;
			return true;
		}
	}
}
