using System;
using System.Data;
using API.Common;
using Microsoft.Data.SqlClient;
using Newtonsoft.Json.Linq;

namespace API.DataAccess.System;

public class SysDictionary
{
	public static DataTable GetSysDictionaryList(string search, string type, string groupName, string prop = "", string order = "")
	{
		string text = ((type == "id") ? " Fid AS value,FName AS label" : ((!(type == "name")) ? " FKey AS value,FName AS label" : " FName AS value,FName AS label"));
		string text2 = text;
		string text3 = " SELECT  " + text2 + ",FZJM AS zjm FROM TSysDictionary ";
		text3 += " WHERE 1=1 AND FEnable=1";
		if (groupName != "")
		{
			text3 = text3 + " AND FGroupId =( SELECT Fid FROM TSysDictGroup WHERE FName='" + groupName + "')";
		}
		if (search != "")
		{
			text3 = text3 + " AND (FName LIKE '%" + search + "%' OR FKey LIKE '%" + search + "%' OR FZJM LIKE '%" + search + "%')";
		}
		if (prop == "")
		{
			prop = "FSort";
			order = "ASC";
		}
		return SQLHelper.LocalDB.RunSqlDt(text3, 0, 0, prop, order);
	}

	public static DataTable GetSysDictionaryList(string groupID, string groupName, string search, string enable, string isSys, string userRight, int limit = 0, int offset = 0, string prop = "", string order = "")
	{
		string text = " SELECT  Fid, FGroupId, FName, FKey, FZJM, FSort, FEnable, FSys " + SQLHelper.total + " FROM TSysDictionary ";
		text += " WHERE 1=1";
		if (enable != "")
		{
			text = text + " AND FEnable=" + enable;
		}
		if (groupID != "")
		{
			text = text + " AND FGroupId=" + groupID;
		}
		if (search != "")
		{
			text = text + " AND (FName LIKE '%" + search + "%' OR FKey LIKE '%" + search + "%' OR FZJM LIKE '%" + search + "%')";
		}
		if (groupName != "")
		{
			text = text + " AND FGroupId =( SELECT Fid FROM TSysDictGroup WHERE FName='" + groupName + "')";
		}
		if (isSys != "" && !userRight.Contains(",990140130,"))
		{
			text += " AND FSys=0";
		}
		if (prop == "")
		{
			prop = "FSort";
			order = "ASC";
		}
		return SQLHelper.LocalDB.RunSqlDt(text, limit, offset, prop, order);
	}

	public static void DictionarySave(JObject jObject, string userName, int userId, string userRight, string curTime, SqlCommand? pCmd = null)
	{
		SqlConnection sqlConnection = null;
		try
		{
			if (pCmd == null)
			{
				sqlConnection = SQLHelper.LocalDB.InitCnn();
				pCmd = sqlConnection.CreateCommand();
				pCmd.Transaction = sqlConnection.BeginTransaction();
			}
			string jObject2 = Util.GetJObject(jObject, "Fid", "0");
			string jObject3 = Util.GetJObject(jObject, "FZJM");
			string jObject4 = Util.GetJObject(jObject, "FName");
			string text = Util.GetJObject(jObject, "FKey");
			string jObject5 = Util.GetJObject(jObject, "FGroupId");
			if (text == "")
			{
				jObject["FKey"] = jObject4;
				text = jObject4;
			}
			string sSql = " SELECT Fid FROM TSysDictionary WHERE FGroupId=" + jObject5 + " AND FName = '" + jObject4 + "' AND Fid!=" + jObject2;
			string text2 = SQLHelper.RunSqlStr(sSql, pCmd);
			if (text2 != "")
			{
				throw new Exception("名称 " + jObject4 + " 已存在！");
			}
			sSql = " SELECT Fid FROM TSysDictionary WHERE FKey = '" + text + "' AND Fid!=" + jObject2;
			text2 = SQLHelper.RunSqlStr(sSql, pCmd);
			if (text2 != "")
			{
				throw new Exception("键值 " + text + " 已存在！");
			}
			if (jObject3 == "")
			{
				sSql = " SELECT dbo.GETZJM('" + jObject4 + "')";
				jObject3 = SQLHelper.RunSqlStr(sSql, pCmd);
				jObject["FZJM"] = jObject3;
			}
			string jObject6 = Util.GetJObject(jObject, "FSys");
			if (jObject6 == "1" && !userRight.Contains(",990140130,"))
			{
				throw new Exception("暂无权限");
			}
			DataCURD.Save(jObject, "TSysDictionary", "保存字典表", "Fid", userId, userName, curTime, pCmd);
			if (sqlConnection != null)
			{
				pCmd.Transaction.Commit();
			}
		}
		catch (Exception ex)
		{
			if (sqlConnection != null)
			{
				pCmd?.Transaction.Rollback();
			}
			throw new Exception(ex.Message.ToString());
		}
		finally
		{
			if (sqlConnection != null)
			{
				sqlConnection.Close();
				sqlConnection.Dispose();
			}
		}
	}

	public static void EnableDictionary(JObject jObject, string userName, int userId, string userRight, string curTime, SqlCommand? pCmd = null)
	{
		SqlConnection sqlConnection = null;
		try
		{
			if (pCmd == null)
			{
				sqlConnection = SQLHelper.LocalDB.InitCnn();
				pCmd = sqlConnection.CreateCommand();
				pCmd.Transaction = sqlConnection.BeginTransaction();
			}
			string jObject2 = Util.GetJObject(jObject, "Fid");
			string sSql = " SELECT FSys FROM TSysDictionary WHERE Fid=" + jObject2;
			if (SQLHelper.RunSqlStr(sSql, pCmd) == "1" && !userRight.Contains(",990140130,"))
			{
				throw new Exception("暂无权限");
			}
			DataCURD.Save(jObject, "TSysDictionary", "保存字典表启用状态", "Fid", userId, userName, curTime, pCmd);
			if (sqlConnection != null)
			{
				pCmd.Transaction.Commit();
			}
		}
		catch (Exception ex)
		{
			if (sqlConnection != null)
			{
				pCmd?.Transaction.Rollback();
			}
			throw new Exception(ex.Message.ToString());
		}
		finally
		{
			if (sqlConnection != null)
			{
				sqlConnection.Close();
				sqlConnection.Dispose();
			}
		}
	}

	public static void DelDictionary(string id, string userName, int userId, string userRight, string curTime, SqlCommand? pCmd = null)
	{
		SqlConnection sqlConnection = null;
		try
		{
			if (pCmd == null)
			{
				sqlConnection = SQLHelper.LocalDB.InitCnn();
				pCmd = sqlConnection.CreateCommand();
				pCmd.Transaction = sqlConnection.BeginTransaction();
			}
			string sSql = " SELECT FSys FROM TSysDictionary WHERE Fid=" + id;
			if (SQLHelper.RunSqlStr(sSql, pCmd) == "1" && !userRight.Contains(",990140130,"))
			{
				throw new Exception("暂无权限");
			}
			DataCURD.Delete("TSysDictionary", "删除字典明细", "Fid", id, userId, userName, curTime, pCmd);
			if (sqlConnection != null)
			{
				pCmd.Transaction.Commit();
			}
		}
		catch (Exception ex)
		{
			if (sqlConnection != null)
			{
				pCmd?.Transaction.Rollback();
			}
			throw new Exception(ex.Message.ToString());
		}
		finally
		{
			if (sqlConnection != null)
			{
				sqlConnection.Close();
				sqlConnection.Dispose();
			}
		}
	}
}
