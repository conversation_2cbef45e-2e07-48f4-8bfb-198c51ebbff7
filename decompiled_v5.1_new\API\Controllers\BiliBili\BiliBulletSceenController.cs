using System;
using System.Data;
using System.Threading.Tasks;
using API.BusService.System;
using API.Common;
using API.DataAccess.BiliBili;
using API.Models.Comm;
using Microsoft.AspNetCore.Mvc;

namespace API.Controllers.BiliBili;

public class BiliBulletSceenController : Controller
{
	[HttpPost]
	public async Task<Response> UpdateBulletSceen(User user)
	{
		Response mRet = new Response();
		try
		{
			await BiliBulletSceen.UpateBulletSceen(user);
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}

	[HttpPost]
	public Response GetBulletSceenList([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectSearch, "search");
			string jObject2 = Util.GetJObject(model.jObjectSearch, "listen");
			string jObject3 = Util.GetJObject(model.jObjectSearch, "trigger");
			DataTable bulletSceenList = BiliBulletSceen.GetBulletSceenList(jObject, jObject3, jObject2, model.limit, model.offset, model.prop, model.order);
			response.data = Util.GetTableResponse(bulletSceenList);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response EditBulletSceen([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			DataCURD.Save(model.jObjectParam, "TBulletSceen", "编辑弹幕", "Fid", BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime, SQLHelper.BiliLocalDB.InitCnn());
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response DelBulletSceen([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "Fid");
			DataCURD.Delete("TBulletSceen", "删除弹幕", "Fid", jObject, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime, SQLHelper.BiliLocalDB.InitCnn());
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}
}
