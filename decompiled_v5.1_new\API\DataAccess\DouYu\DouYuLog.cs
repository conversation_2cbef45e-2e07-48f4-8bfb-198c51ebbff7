using System;
using API.Common;
using API.Models.Comm;
using Newtonsoft.Json.Linq;

namespace API.DataAccess.DouYu;

public class DouYuLog
{
	public static void WriteLog(Request model, int userId, string userName, string curTime)
	{
		string jObject = Util.GetJObject(model.jObjectParam, "group");
		string jObject2 = Util.GetJObject(model.jObjectParam, "type");
		string jObject3 = Util.GetJObject(model.jObjectParam, "key");
		string jObject4 = Util.GetJObject(model.jObjectParam, "typeName");
		if (!(jObject2 == "LoginInvalid"))
		{
			if (jObject2 == "Message")
			{
				string jObject5 = Util.GetJObject(model.jObjectParam, "message");
				Util.WriteLog(jObject, DouYuCookies.GetCookieName(jObject3), jObject4, jObject5, ConsoleColor.Red);
				JObject jObject6 = new JObject
				{
					["FKey"] = jObject3,
					["FStatus"] = jObject5
				};
				DataCURD.Save(jObject6, "TCookies", "更新账号状态", "FKey", userId, userName, curTime, SQLHelper.DouYuLocalDB.InitCnn());
			}
			else
			{
				Util.WriteLog(jObject, "错误日志", jObject2, Util.GetJObject(model.jObjectParam, "message"), ConsoleColor.Red);
			}
		}
		else
		{
			Util.WriteLog(jObject, DouYuCookies.GetCookieName(jObject3), jObject4, "浏览器登录失效", ConsoleColor.Red);
			DataCURD.Save(new JObject
			{
				["FKey"] = jObject3,
				["FBrowserStatus"] = 0
			}, "TCookies", "更新浏览器状态", "FKey", userId, userName, curTime, SQLHelper.DouYuLocalDB.InitCnn());
		}
	}
}
