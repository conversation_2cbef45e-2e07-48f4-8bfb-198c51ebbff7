using System;
using System.Data;
using System.IO;
using System.Linq;
using API.Common;
using API.Models.Comm;
using Newtonsoft.Json.Linq;

namespace API.DataAccess.BiliBili;

public class BiliLog
{
	public static object GetLogList(string path)
	{
		JArray jArray = new JArray();
		if (File.Exists(path))
		{
			using StreamReader streamReader = new StreamReader(path);
			string text;
			while ((text = streamReader.ReadLine()) != null && text != "")
			{
				string[] array = text.Split('\t');
				if (array.Length == 4)
				{
					jArray.Add(new JObject
					{
						["FDate"] = array[0],
						["FTask"] = array[1],
						["FContent"] = array[2],
						["FStatus"] = array[3]
					});
				}
			}
		}
		return new
		{
			total = jArray.Count,
			rows = jArray
		};
	}

	public static object GetPathList(string date, string path)
	{
		JArray jArray = new JArray();
		DateTime dateTime = DateTime.Parse(date);
		if (path == "")
		{
			path = Directory.GetCurrentDirectory() + "\\log\\BiliBili\\" + dateTime.ToString("yyyyMM") + "\\" + dateTime.Day;
			if (Directory.Exists(path))
			{
				foreach (string item in Directory.EnumerateDirectories(path))
				{
					jArray.Add(new JObject
					{
						["Fid"] = item,
						["FName"] = Path.GetFileNameWithoutExtension(item)
					});
				}
			}
		}
		else
		{
			string[] files = Directory.GetFiles(path);
			string[] array = files;
			foreach (string text in array)
			{
				jArray.Add(new JObject
				{
					["Fid"] = text,
					["FName"] = Path.GetFileNameWithoutExtension(text)
				});
			}
		}
		return new
		{
			total = jArray.Count,
			rows = jArray
		};
	}

	public static void WriteLog(Request model, int userId, string userName, string curTime)
	{
		string jObject = Util.GetJObject(model.jObjectParam, "group");
		string jObject2 = Util.GetJObject(model.jObjectParam, "type");
		string jObject3 = Util.GetJObject(model.jObjectParam, "key");
		string jObject4 = Util.GetJObject(model.jObjectParam, "typeName");
		switch (jObject2)
		{
		case "Daily":
		case "Compulsory":
		case "Normal":
		{
			jObject4 = ((jObject2 == "Daily") ? "领取每日任务" : ((jObject2 == "Compulsory") ? "领取强制任务" : "领取里程投稿"));
			string jObject7 = Util.GetJObject(model.jObjectParam, "areaId", "0");
			if (jObject7 == "0" && jObject4 == "领取里程投稿")
			{
				jObject4 = "领取里程投稿Plus";
			}
			JArray jArray = Util.GetJObject<JArray>(model.jObjectParam, "array") ?? new JArray();
			string jObject8 = Util.GetJObject(model.jObjectParam, "cookieId", "0");
			int num = Util.GetJObject<int>(model.jObjectParam, "times") + 1;
			string taskKey = "'" + string.Join("','", (jArray ?? new JArray()).Select((JToken token) => (token["taskKey"] ?? ((JToken)"")).ToString()).ToArray()) + "'";
			DataTable cookiesTaskWriteLog = BiliCookiesTask.GetCookiesTaskWriteLog(jObject8, taskKey);
			if (cookiesTaskWriteLog.Rows.Count == 0)
			{
				Util.WriteLog(jObject, BiliCookies.GetCookieName("", jObject8), jObject4, "未领取到Cdkey,共请求" + num + "次！", ConsoleColor.Red);
				break;
			}
			foreach (DataRow row in cookiesTaskWriteLog.Rows)
			{
				Util.WriteLog(jObject, Util.GetJObject(row, "FCookieName"), jObject4, "【" + Util.GetJObject(row, "FAreaName") + "】【" + Util.GetJObject(row, "FTaskName") + "】领取成功！", ConsoleColor.Green);
			}
			Util.WriteLog(jObject, BiliCookies.GetCookieName("", jObject8), jObject4, "共请求" + num + "次！", ConsoleColor.Green);
			break;
		}
		case "LoginInvalid":
			Util.WriteLog(jObject, BiliCookies.GetCookieName(jObject3), jObject4, "浏览器登录失效", ConsoleColor.Red);
			DataCURD.Save(new JObject
			{
				["FKey"] = jObject3,
				["FBrowserStatus"] = 0
			}, "TCookies", "更新浏览器状态", "FKey", userId, userName, curTime, SQLHelper.BiliLocalDB.InitCnn());
			break;
		case "Message":
		{
			string jObject5 = Util.GetJObject(model.jObjectParam, "message");
			Util.WriteLog(jObject, BiliCookies.GetCookieName(jObject3), jObject4, jObject5, ConsoleColor.Red);
			JObject jObject6 = new JObject
			{
				["FKey"] = jObject3,
				["FStatus"] = jObject5
			};
			DataCURD.Save(jObject6, "TCookies", "更新账号状态", "FKey", userId, userName, curTime, SQLHelper.BiliLocalDB.InitCnn());
			break;
		}
		case "LotteryDraw":
			Util.WriteLog(jObject, BiliCookies.GetCookieName(jObject3), "抽奖", Util.GetJObject(model.jObjectParam, "message"), ConsoleColor.Green);
			break;
		case "LiveTask":
			Util.WriteLog(jObject, BiliCookies.GetCookieName(jObject3), "弹幕礼物观看", Util.GetJObject(model.jObjectParam, "message"), ConsoleColor.Green);
			break;
		default:
			Util.WriteLog(jObject, "错误日志", jObject2, Util.GetJObject(model.jObjectParam, "message"), ConsoleColor.Red);
			break;
		}
	}
}
