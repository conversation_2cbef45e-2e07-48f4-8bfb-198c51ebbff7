using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;

namespace API.Common;

public class RsaEncrypt
{
	private static readonly string charSet = "UTF-8";

	private static readonly string privatePkcs8 = "MIICeQIBADANBgkqhkiG9w0BAQEFAASCAmMwggJfAgEAAoGBAMz0Czg6QUtTISa2pUkloeQB/TEpHdqrfyroWpKLW9B/LWFSOGH9nyTk1pPZaeadyEZQ6gay/C0pUAetLraq9bMA/Luxq68b87uG7WX7dKytEO2/87qGpGMRs97H+GlkzWil2QO2KK4cHnAcVicPsmi5aZ72U0BWJFyPhtd+qdmrAgMBAAECgYEAvW67iAbgHt0BASVD9C3iSjpEaVHVlC165o/IVzaTcEx8Bz3Ve0zN8W3JnvIO3ebsG4HiLLr2Nk++9rltOc0eNeGMv7F1e/OFot1wN0ON6s1g4bYh1z5Uz8FcYiMWcqHHICrx+oSFeK9x+I2Zge7enQXcsVnqEhm77ZE5YczSryECQQD9nB58e5efYchF+cYbmURioX18cUMuhQbB9Aq2N55cd689Lg35KZqT8JQTp/8tQSdCJG8d2nU8VKspUKTEAuaDAkEAzuKIIoc9PVJvy90LhIPA9c1S8BPCI7EMCaTZqJ5o3VaR2dqvUZDGX7kL3kYkQ+n7mq3KIECvkEFzA+FOP96XuQJBAJQTKHW0T/YeSKoayUHp/lS8R6F2HCy4PRbXn71+wfbpZqcJEd2OHhQM3tiPOV258esbjMlYeSUNppZL4LgVnXMCQQC7Lvs9Ql+GPDAqo7ToEM1lmICR906QPIBHuX+1sJ3wpYMROWumwPa7ZRH36j6ls+6R5OwcgmpWeuE1gYTrBNsBAkEAn2pEtAljX1foQff6CLozYg/J6J9RmVFcJ6qz0LX3052qNFBQYw8CMHB7VkVNzsDIDC8LX5uP2pzTrdPLew+pPA==";

	private static readonly string publicPk = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDM9As4OkFLUyEmtqVJJaHkAf0xKR3aq38q6FqSi1vQfy1hUjhh/Z8k5NaT2WnmnchGUOoGsvwtKVAHrS62qvWzAPy7sauvG/O7hu1l+3SsrRDtv/O6hqRjEbPex/hpZM1opdkDtiiuHB5wHFYnD7JouWme9lNAViRcj4bXfqnZqwIDAQAB";

	public static string RSAEncrypt(string content, string? publicKeyPem = null)
	{
		try
		{
			if (publicKeyPem == null)
			{
				publicKeyPem = publicPk;
			}
			publicKeyPem = publicKeyPem.Replace("-----BEGIN PUBLIC KEY-----", "").Replace("-----END PUBLIC KEY-----", "").Replace("\r", "")
				.Replace("\n", "")
				.Trim();
			RSA rSA = RSA.Create();
			rSA.ImportSubjectPublicKeyInfo(Convert.FromBase64String(publicKeyPem), out var _);
			byte[] bytes = Encoding.GetEncoding(charSet).GetBytes(content);
			int num = rSA.KeySize / 8 - 11;
			if (bytes.Length <= num)
			{
				byte[] inArray = rSA.Encrypt(bytes, RSAEncryptionPadding.Pkcs1);
				return Convert.ToBase64String(inArray);
			}
			MemoryStream memoryStream = new MemoryStream(bytes);
			MemoryStream memoryStream2 = new MemoryStream();
			byte[] array = new byte[num];
			for (int num2 = memoryStream.Read(array, 0, num); num2 > 0; num2 = memoryStream.Read(array, 0, num))
			{
				byte[] array2 = new byte[num2];
				Array.Copy(array, 0, array2, 0, num2);
				byte[] array3 = rSA.Encrypt(array2, RSAEncryptionPadding.Pkcs1);
				memoryStream2.Write(array3, 0, array3.Length);
			}
			return Convert.ToBase64String(memoryStream2.ToArray(), Base64FormattingOptions.None);
		}
		catch (Exception innerException)
		{
			throw new Exception("EncryptContent = " + content + ",charset = " + charSet, innerException);
		}
	}

	public static string RSADecrypt(string content, string? privateKeyPem = null, string? keyFormat = null)
	{
		if (privateKeyPem == null)
		{
			privateKeyPem = privatePkcs8;
		}
		if (keyFormat == null)
		{
			keyFormat = "PKCS8";
		}
		try
		{
			privateKeyPem = privateKeyPem.Replace("-----BEGIN RSA PRIVATE KEY-----", "").Replace("-----END RSA PRIVATE KEY-----", "").Replace("\r", "")
				.Replace("\n", "")
				.Trim();
			privateKeyPem = privateKeyPem.Replace("-----BEGIN PRIVATE KEY-----", "").Replace("-----END PRIVATE KEY-----", "").Replace("\r", "")
				.Replace("\n", "")
				.Trim();
			RSA rSA = RSA.Create();
			int bytesRead;
			if (keyFormat == "PKCS8")
			{
				rSA.ImportPkcs8PrivateKey(Convert.FromBase64String(privateKeyPem), out bytesRead);
			}
			else
			{
				if (!(keyFormat == "PKCS1"))
				{
					throw new Exception("只支持PKCS8，PKCS1");
				}
				rSA.ImportRSAPrivateKey(Convert.FromBase64String(privateKeyPem), out bytesRead);
			}
			byte[] array = Convert.FromBase64String(content);
			int num = rSA.KeySize / 8;
			if (array.Length <= num)
			{
				byte[] bytes = rSA.Decrypt(array, RSAEncryptionPadding.Pkcs1);
				return Encoding.GetEncoding(charSet).GetString(bytes);
			}
			MemoryStream memoryStream = new MemoryStream(array);
			MemoryStream memoryStream2 = new MemoryStream();
			byte[] array2 = new byte[num];
			for (int num2 = memoryStream.Read(array2, 0, num); num2 > 0; num2 = memoryStream.Read(array2, 0, num))
			{
				byte[] array3 = new byte[num2];
				Array.Copy(array2, 0, array3, 0, num2);
				byte[] array4 = rSA.Decrypt(array3, RSAEncryptionPadding.Pkcs1);
				memoryStream2.Write(array4, 0, array4.Length);
			}
			return Encoding.GetEncoding(charSet).GetString(memoryStream2.ToArray());
		}
		catch (Exception innerException)
		{
			throw new Exception("DecryptContent = " + content + ",charset = " + charSet, innerException);
		}
	}
}
