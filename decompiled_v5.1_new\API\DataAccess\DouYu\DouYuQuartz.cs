using System;
using System.Data;
using System.Threading.Tasks;
using API.Common;
using API.Quartz;
using Microsoft.Data.SqlClient;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Quartz;

namespace API.DataAccess.DouYu;

public class DouYuQuartz
{
	public static DataTable GetQuartzList(string id, string groupId, string search, int userId, int limit = 0, int offset = 0, string prop = "", string order = "")
	{
		string text = " SELECT '' AS FStatus,T2.FName AS FJobName,T2.FLabel AS FJobLabel,CASE T1.FSeconds WHEN 0 THEN '无限' ELSE CAST(T1.FSeconds AS NVARCHAR(100))+'秒' END AS FSecondsName";
		text += " ,T1.Fid,T1.FExecTime,T1.FGroupId,T1.FDifference,T1.FSeconds,T1.FJobId,T1.FAreaId,T1.FCookieId,T1.FParam,T1.FEnable";
		text += " ,T3.FName AS FAreaName,T4.FStatus AS FGroupStatus,CASE FDifference WHEN 0 THEN '' ELSE CAST(T1.FDifference AS NVARCHAR(100))+'秒' END AS FDifferenceName";
		text += " ,STUFF((SELECT ','+ CAST(B1.FName AS VARCHAR(100)) FROM TCookies B1 WHERE CHARINDEX(','+CAST(B1.Fid AS NVARCHAR(100))+',',','+T1.FCookieId+',')>0 FOR XML PATH('')),1,1,'') AS FCookieName";
		text = text + SQLHelper.total + " FROM TQuartz T1 LEFT JOIN TQuartzJob T2 ON T2.Fid=T1.FJobId";
		text += " LEFT JOIN TArea T3 ON T3.Fid =T1.FAreaId";
		text += " LEFT JOIN TQuartzGroup T4 ON T4.Fid=T1.FGroupId";
		text = text + " WHERE T1.FUserId=" + userId + " AND T1.FGroupId=" + groupId;
		if (id != "")
		{
			text = text + " AND T1.Fid=" + id;
		}
		if (search != "")
		{
			text = text + " AND T2.FName LIKE '%" + search + "%'";
		}
		if (prop == "")
		{
			prop = "T1.FEnable DESC, CASE WHEN T1.FExecTime > CAST(GETDATE() AS TIME) THEN 1 ELSE 0 END DESC,T1.FExecTime ";
			order = "ASC";
		}
		return SQLHelper.DouYuLocalDB.RunSqlDt(text, limit, offset, prop, order);
	}

	public static DataTable GetQuartzJobList(string search, int limit = 0, int offset = 0, string prop = "", string order = "")
	{
		string text = " SELECT * FROM TQuartzJob WHERE 1=1";
		if (search != "")
		{
			text = text + " AND FName LIKE '%" + search + "%'";
		}
		if (prop == "")
		{
			prop = "FSort";
			order = "ASC";
		}
		return SQLHelper.DouYuLocalDB.RunSqlDt(text, limit, offset, prop, order);
	}

	public static async Task SaveQuartz(ISchedulerFactory schedulerFactory, JObject jObject, int userId, string userName, string curTime)
	{
		SqlConnection conn = SQLHelper.DouYuLocalDB.InitCnn();
		SqlCommand pCmd = conn.CreateCommand();
		pCmd.Transaction = conn.BeginTransaction();
		try
		{
			IScheduler scheduler = await schedulerFactory.GetScheduler();
			string jObject2 = Util.GetJObject(jObject, "FGroupId");
			string sSql = " SELECT FStatus FROM TQuartzGroup WHERE Fid=" + jObject2;
			string text = SQLHelper.RunSqlStr(sSql, pCmd);
			jObject["FUserId"] = userId;
			jObject["FEnable"] = 1;
			string id = DataCURD.Save(jObject, "TQuartz", "保存定时任务", "Fid", userId, userName, curTime, pCmd);
			if (text == "1")
			{
				string jObject3 = Util.GetJObject(jObject, "FExecTime");
				int jObject4 = Util.GetJObject<int>(jObject, "FDifference");
				int seconds = Util.GetJObject<int>(jObject, "FSeconds");
				string jobName = Util.GetJObject(jObject, "FJobName");
				string jObject5 = Util.GetJObject(jObject, "FAreaId");
				string jObject6 = Util.GetJObject(jObject, "FCookieId");
				string jObject7 = Util.GetJObject(jObject, "FParam");
				DateTime dateTime = DateTime.ParseExact(jObject3, "HH:mm:ss", null);
				string cronExpression = dateTime.Second + " " + dateTime.Minute + " " + dateTime.Hour + " ? * *";
				JObject jObjectQuartz = new JObject
				{
					["areaId"] = jObject5,
					["cookieId"] = jObject6,
					["param"] = jObject7,
					["difference"] = jObject4
				};
				if (await scheduler.CheckExists(new JobKey(jobName, "定时任务-斗鱼-" + id)))
				{
					if (await QzUtil.ExecutingJob(scheduler, "定时任务-斗鱼-" + id, jobName))
					{
						throw new Exception("任务正在执行，请先停止任务！");
					}
					if (!(await scheduler.DeleteJob(new JobKey(jobName, "定时任务-斗鱼-" + id))))
					{
						throw new Exception("当前任务无法编辑，请稍后再试！");
					}
				}
				await QzUtil.ScheduleJobPlus<QzDouYu>(scheduler, "定时任务-斗鱼-" + id, jobName, cronExpression, seconds, jObjectQuartz, userId);
			}
			pCmd.Transaction.Commit();
		}
		catch (Exception ex)
		{
			pCmd.Transaction.Rollback();
			throw new Exception(ex.Message);
		}
		finally
		{
			conn.Close();
			conn.Dispose();
		}
	}

	public static async Task DelQuartz(ISchedulerFactory schedulerFactory, JObject jObject, int userId, string userName, string curTime)
	{
		SqlConnection conn = SQLHelper.DouYuLocalDB.InitCnn();
		SqlCommand pCmd = conn.CreateCommand();
		pCmd.Transaction = conn.BeginTransaction();
		try
		{
			IScheduler scheduler = await schedulerFactory.GetScheduler();
			string id = Util.GetJObject(jObject, "Fid");
			string jobName = Util.GetJObject(jObject, "FJobName");
			DataCURD.Delete("TQuartz", "删除定时任务", "Fid", id, userId, userName, curTime, pCmd);
			if (await scheduler.CheckExists(new JobKey(jobName, "定时任务-斗鱼-" + id)))
			{
				if (await QzUtil.ExecutingJob(scheduler, "定时任务-斗鱼-" + id, jobName))
				{
					throw new Exception("任务正在执行，请先停止任务！");
				}
				if (!(await scheduler.DeleteJob(new JobKey(jobName, "定时任务-斗鱼-" + id))))
				{
					throw new Exception("当前任务无法删除，请稍后再试！");
				}
			}
			pCmd.Transaction.Commit();
		}
		catch (Exception ex)
		{
			pCmd.Transaction.Rollback();
			throw new Exception(ex.Message);
		}
		finally
		{
			conn.Close();
			conn.Dispose();
		}
	}

	public static async Task<int> EnableQuartz(ISchedulerFactory schedulerFactory, JObject jObject, int userId, string userName, string curTime)
	{
		string id = Util.GetJObject(jObject, "Fid");
		string enable = Util.GetJObject(jObject, "FEnable");
		string jObject2 = Util.GetJObject(jObject, "FGroupId");
		DataTable dt = GetQuartzList(id, jObject2, "", userId);
		if (dt.Rows.Count == 0)
		{
			throw new Exception("当前数据不正确！");
		}
		DataTable quartzGroupList = DouYuQuartzGroup.GetQuartzGroupList(jObject2, "", userId);
		bool groupStatus = false;
		if (quartzGroupList.Rows.Count == 1)
		{
			if (quartzGroupList.Rows[0]["FStatus"].ToString() == "1")
			{
				groupStatus = true;
			}
			IScheduler scheduler = await schedulerFactory.GetScheduler();
			string jobName = dt.Rows[0]["FJobName"].ToString() ?? "";
			bool flag = await scheduler.CheckExists(new JobKey(jobName, "定时任务-斗鱼-" + id));
			if (groupStatus)
			{
				if (enable == "1")
				{
					if (!flag)
					{
						string s = dt.Rows[0]["FExecTime"].ToString() ?? "";
						int num = int.Parse(dt.Rows[0]["FDifference"].ToString() ?? "0");
						int seconds = int.Parse(dt.Rows[0]["FSeconds"].ToString() ?? "0");
						string text = dt.Rows[0]["FAreaId"].ToString() ?? "";
						string text2 = dt.Rows[0]["FCookieId"].ToString() ?? "";
						string text3 = dt.Rows[0]["FParam"].ToString() ?? "";
						DateTime dateTime = DateTime.ParseExact(s, "HH:mm:ss", null);
						string cronExpression = dateTime.Second + " " + dateTime.Minute + " " + dateTime.Hour + " ? * *";
						JObject jObject3 = new JObject
						{
							["areaId"] = text,
							["cookieId"] = text2,
							["param"] = text3,
							["difference"] = num
						};
						await QzUtil.ScheduleJobPlus<QzDouYu>(scheduler, "定时任务-斗鱼-" + id, jobName, cronExpression, seconds, jObject3, userId);
					}
				}
				else if (flag)
				{
					if (await QzUtil.ExecutingJob(scheduler, "定时任务-斗鱼-" + id, jobName))
					{
						throw new Exception("任务正在执行，请先停止任务！");
					}
					if (!(await scheduler.DeleteJob(new JobKey(jobName, "定时任务-斗鱼-" + id))))
					{
						throw new Exception("当前任务无法停用，请稍后再试！");
					}
				}
			}
			DataCURD.Save(new JObject
			{
				["Fid"] = id,
				["FEnable"] = enable
			}, "TQuartz", ((enable == "1") ? "启用" : "停用") + "定时任务", "Fid", userId, userName, curTime, SQLHelper.DouYuLocalDB.InitCnn());
			return int.Parse(enable);
		}
		throw new Exception("当前数据不正确！");
	}

	public static string ShareQuartz(JObject jObject, int userId)
	{
		string jObject2 = Util.GetJObject(jObject, "Fid");
		if (jObject2 == "0")
		{
			throw new Exception("当前分组无任务！");
		}
		string sSql = " SELECT * FROM TQuartz WHERE FGroupId=" + jObject2 + " AND FUserId=" + userId;
		DataTable dataTable = SQLHelper.DouYuLocalDB.RunSqlDt(sSql);
		if (dataTable.Rows.Count == 0)
		{
			throw new Exception("当前分组无任务！");
		}
		for (int i = 0; i < dataTable.Rows.Count; i++)
		{
			dataTable.Rows[i]["FUserId"] = 0;
			dataTable.Rows[i]["FGroupId"] = 0;
			dataTable.Rows[i]["FCookieId"] = "";
		}
		return RsaEncrypt.RSAEncrypt(JsonConvert.SerializeObject(new JObject
		{
			["type"] = "斗鱼",
			["rows"] = JArray.FromObject(dataTable)
		}));
	}

	public static void ExportQuartz(JObject jObject, int userId, string userName, string curTime)
	{
		SqlConnection sqlConnection = SQLHelper.DouYuLocalDB.InitCnn();
		SqlCommand sqlCommand = sqlConnection.CreateCommand();
		sqlCommand.Transaction = sqlConnection.BeginTransaction();
		try
		{
			string jObject2 = Util.GetJObject(jObject, "value");
			string text = Util.GetJObject(jObject, "Fid");
			if (text == "0")
			{
				text = DouYuQuartzGroup.SaveQuartzGroup(jObject, userId, userName, curTime, sqlCommand);
			}
			else
			{
				DouYuQuartzGroup.SaveQuartzGroup(jObject, userId, userName, curTime, sqlCommand);
				string sSql = " SELECT FStatus FROM TQuartzGroup WHERE Fid=" + text;
				string text2 = SQLHelper.RunSqlStr(sSql, sqlCommand);
				if (text2 != "0")
				{
					throw new Exception("请先停止当前任务分组！");
				}
			}
			JObject jToken = JObject.Parse(RsaEncrypt.RSADecrypt(jObject2));
			string jObject3 = Util.GetJObject(jToken, "type");
			if (jObject3 == "斗鱼")
			{
				JArray jObject4 = Util.GetJObject<JArray>(jToken, "rows");
				if (jObject4 != null && jObject4.Count > 0)
				{
					DataCURD.Delete("TQuartz", "批量删除任务", "FGroupId", text, userId, userName, curTime, sqlCommand);
					foreach (JToken item in jObject4)
					{
						JObject jObject5 = (JObject)item;
						jObject5["Fid"] = 0;
						jObject5["FUserId"] = userId;
						jObject5["FGroupId"] = text;
						DataCURD.Save(jObject5, "TQuartz", "导入任务", "Fid", userId, userName, curTime, sqlCommand);
					}
					sqlCommand.Transaction.Commit();
					return;
				}
				throw new Exception("没有任务信息分享个der！");
			}
			throw new Exception("分享信息类型不正确！");
		}
		catch (Exception ex)
		{
			sqlCommand.Transaction.Rollback();
			throw new Exception(ex.Message);
		}
		finally
		{
			sqlConnection.Close();
			sqlConnection.Dispose();
		}
	}

	public static DataTable GetQuartzExecList(int userId)
	{
		string text = " SELECT T2.FName AS FJobName,T2.FLabel AS FJobLabel";
		text += " ,T1.Fid,T1.FExecTime,T1.FDifference,T1.FSeconds,T1.FAreaId,T1.FCookieId,T1.FParam,T1.FEnable";
		text += " FROM TQuartz T1 LEFT JOIN TQuartzJob T2 ON T2.Fid=T1.FJobId";
		text += " LEFT JOIN TQuartzGroup T3 ON T3.Fid=T1.FGroupId AND T3.FUserId=T1.FUserId";
		text = text + " WHERE T3.FStatus=1 AND T1.FEnable=1 AND T1.FUserId=" + userId;
		text += " ORDER BY FExecTime";
		return SQLHelper.DouYuLocalDB.RunSqlDt(text);
	}
}
