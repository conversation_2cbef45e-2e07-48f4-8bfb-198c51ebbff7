using System;
using System.Data;
using API.BusService.System;
using API.Common;
using API.DataAccess.BiliBili;
using API.Models.Comm;
using Microsoft.AspNetCore.Mvc;

namespace API.Controllers.BiliBili;

public class BiliCookiesTaskController : Controller
{
	[HttpPost]
	public Response GetCookiesTaskList([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectSearch, "cookieId");
			string jObject2 = Util.GetJObject(model.jObjectSearch, "areaId");
			DataTable cookiesTaskList = BiliCookiesTask.GetCookiesTaskList(jObject, jObject2, BusSysUser.Instance.User.Id, model.limit, model.offset, model.prop, model.order);
			response.data = Util.GetTableResponse(cookiesTaskList);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response SetCompulsory([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			BiliCookiesTask.SetCompulsory(model.jObjectParam, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response SaveTask([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			BiliCookiesTask.SaveTask(model.jObjectParam, BusSysUser.Instance.User.Id);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response SetSort([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			BiliCookiesTask.SetSort(model.jObjectParam, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response RestoreDefault([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "cookieId");
			string jObject2 = Util.GetJObject(model.jObjectParam, "areaId");
			BiliCookiesTask.RestoreDefault(jObject, jObject2, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response SyncAll([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "cookieId");
			string jObject2 = Util.GetJObject(model.jObjectParam, "areaId");
			BiliCookiesTask.SyncAll(jObject, jObject2, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response DelAll([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "cookieId");
			string jObject2 = Util.GetJObject(model.jObjectParam, "areaId");
			string jObject3 = Util.GetJObject(model.jObjectParam, "id");
			BiliCookiesTask.DelAll(jObject, jObject2, jObject3, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response EnableSwitch([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			response.data = BiliCookiesTask.EnableSwitch(model.jObjectParam, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
		}
		catch (Exception ex)
		{
			string jObject = Util.GetJObject(model.jObjectParam, "FEnable");
			response.data = ((!(jObject == "1")) ? 1 : 0);
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}
}
