using System;
using System.Data;
using API.BusService.System;
using API.Common;
using API.DataAccess.DouYu;
using API.Models.Comm;
using Microsoft.AspNetCore.Mvc;

namespace API.Controllers.DouYu;

public class DouYuGeetestController : Controller
{
	[HttpPost]
	public Response GetGeetestList([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectSearch, "search");
			DataTable geetestList = DouYuGeetest.GetGeetestList(jObject, model.limit, model.offset, model.prop, model.order);
			response.data = Util.GetTableResponse(geetestList);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response EditGeetest([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			DataCURD.Save(model.jObjectParam, "TGeetest", "编辑打码", "Fid", BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime, SQLHelper.DouYuLocalDB.InitCnn());
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response EnableGeetest([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			DouYuGeetest.EnableGeetest(model.jObjectParam, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response DelGeetest([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "Fid");
			DataCURD.Delete("TGeetest", "删除打码", "Fid", jObject, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime, SQLHelper.DouYuLocalDB.InitCnn());
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}
}
