using System;
using System.Data;
using Microsoft.Data.SqlClient;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace API.Common;

public class DataCURD
{
	public static string Save(JObject jObject, string tableName, string operation, string key, int userId, string userName, string? curTime, SqlCommand pCmd)
	{
		try
		{
			string content = string.Empty;
			string empty = string.Empty;
			if (curTime == null || curTime == "")
			{
				curTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss:fff");
			}
			string text = Util.GetJObject(jObject, key, "0");
			empty = " DECLARE @tablename NVARCHAR(1000)='" + tableName + "'";
			empty += " SELECT column_name = a.name, is_identity = CASE WHEN COLUMNPROPERTY( a.id,a.name,'IsIdentity')=1 THEN 'YES'ELSE '' END, typname = b.name";
			empty += " ,column_notnull = CASE WHEN a.isnullable=1 THEN '0'ELSE '1' END,column_default =CASE WHEN e.text IS NULL THEN 0 ELSE 1 END";
			empty += " ,column_description = ISNULL(SUBSTRING(CONVERT(NVARCHAR,g.value),1,CASE WHEN  CHARINDEX(':',CONVERT(nvarchar,g.value))>=1 THEN CHARINDEX(':',CONVERT(nvarchar,g.value))-1 ELSE LEN(CONVERT(nvarchar,g.value)) END),a.name)";
			empty += " FROM syscolumns a left join systypes b on a.xusertype=b.xusertype";
			empty += " INNER JOIN sysobjects d ON a.id=d.id AND d.xtype='U' AND d.name<>'dtproperties'";
			empty += " LEFT JOIN syscomments e ON a.cdefault=e.id";
			empty += " LEFT JOIN sys.extended_properties g on a.id = G.major_id AND a.colid = g.minor_id  ";
			empty += " WHERE d.name=@tablename ORDER BY a.id,a.colorder ";
			DataTable dataTable = SQLHelper.RunSqlDt(empty, pCmd);
			string text2 = "";
			string text3 = "";
			string text4 = "";
			string text5 = "";
			string text6 = "";
			string text7 = "";
			string text8 = "";
			string text9 = "";
			string text10 = "";
			string text11 = "";
			JObject jObject2 = new JObject { ["Key"] = "" };
			foreach (DataRow row in dataTable.Rows)
			{
				text2 = row["column_name"].ToString() ?? "";
				text4 = row["column_description"].ToString() ?? "";
				text3 = row["column_notnull"].ToString() ?? "";
				text5 = row["column_default"].ToString() ?? "";
				text6 = row["is_identity"].ToString() ?? "";
				text7 = row["typname"].ToString() ?? "";
				JToken jToken = jObject[text2];
				if (!(text6 != "YES"))
				{
					continue;
				}
				if (text3 == "1" && jToken == null && text5 == "0" && text == "0")
				{
					throw new Exception("请求参数不正确！");
				}
				if ((!(text3 == "1") || jToken != null || !(text5 == "0") || !(text != "0")) && (!(text3 == "1") || jToken != null || !(text5 == "1")) && (!(text3 == "0") || jToken != null))
				{
					if (jToken == null)
					{
						throw new Exception("意外的错误！");
					}
					if (jToken.Type == JTokenType.Null || jToken.ToString().ToUpper().Equals("NULL", StringComparison.CurrentCultureIgnoreCase))
					{
						pCmd.Parameters.AddWithValue("@" + text2, DBNull.Value);
					}
					else
					{
						pCmd.Parameters.AddWithValue("@" + text2, jToken.ToString());
					}
					if (text == "0")
					{
						text9 = text9 + text2 + ",";
						text10 = text10 + "@" + text2 + ",";
						continue;
					}
					text11 = text11 + text2 + "=@" + text2 + ",";
					jObject2[text2] = jToken.ToString();
					JObject jObject3 = jObject2;
					jObject3["Key"] = jObject3["Key"]?.ToString() + text2 + ",";
				}
			}
			if (text == "0")
			{
				text8 = " INSERT INTO " + tableName + " (" + text9.TrimEnd(',') + ") values(" + text10.TrimEnd(',') + ") ";
				text8 = text8 + " SELECT IDENT_CURRENT('" + tableName + "')";
				text = SQLHelper.RunSqlStr(text8, pCmd);
				text8 = "SELECT * FROM " + tableName + " WHERE " + key + "='" + text + "'";
				content = JsonConvert.SerializeObject(new JObject
				{
					["type"] = "新增",
					["data"] = JArray.FromObject(SQLHelper.RunSqlDt(text8, pCmd))
				});
			}
			else
			{
				text8 = "SELECT " + jObject2["Key"]?.ToString().Trim(',') + " FROM " + tableName + " WHERE " + key + "='" + text + "'";
				text8 = text8 + " UPDATE " + tableName + " SET " + text11.Trim(',') + " WHERE " + key + "='" + text + "'";
				DataTable dataTable2 = SQLHelper.RunSqlDt(text8, pCmd);
				if (dataTable2.Rows.Count > 0)
				{
					JArray jArray = JArray.FromObject(dataTable2);
					jArray.Add(jObject2);
					content = JsonConvert.SerializeObject(new JObject
					{
						["type"] = "编辑",
						["data"] = jArray
					});
				}
			}
			WriteLog(tableName, operation, key, text, content, userId, userName, curTime, pCmd);
			return text;
		}
		catch (Exception ex)
		{
			throw new Exception(ex.Message.ToString());
		}
	}

	public static string Save(JObject jObject, string tableName, string operation, string key, int userId, string userName, string? curTime, SqlConnection conn)
	{
		SqlCommand sqlCommand = conn.CreateCommand();
		sqlCommand.Transaction = conn.BeginTransaction();
		try
		{
			string content = string.Empty;
			string empty = string.Empty;
			if (curTime == null || curTime == "")
			{
				curTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss:fff");
			}
			string text = Util.GetJObject(jObject, key, "0");
			empty = empty + " DECLARE @tablename NVARCHAR(1000)='" + tableName + "'";
			empty += " SELECT column_name = a.name, is_identity = CASE WHEN COLUMNPROPERTY( a.id,a.name,'IsIdentity')=1 THEN 'YES'ELSE '' END, typname = b.name";
			empty += " ,column_notnull = CASE WHEN a.isnullable=1 THEN '0'ELSE '1' END,column_default =CASE WHEN e.text IS NULL THEN 0 ELSE 1 END";
			empty += " ,column_description = ISNULL(SUBSTRING(CONVERT(NVARCHAR,g.value),1,CASE WHEN  CHARINDEX(':',CONVERT(nvarchar,g.value))>=1 THEN CHARINDEX(':',CONVERT(nvarchar,g.value))-1 ELSE LEN(CONVERT(nvarchar,g.value)) END),a.name)";
			empty += " FROM syscolumns a left join systypes b on a.xusertype=b.xusertype";
			empty += " INNER JOIN sysobjects d ON a.id=d.id AND d.xtype='U' AND d.name<>'dtproperties'";
			empty += " LEFT JOIN syscomments e ON a.cdefault=e.id";
			empty += " LEFT JOIN sys.extended_properties g on a.id = G.major_id AND a.colid = g.minor_id  ";
			empty += " WHERE d.name=@tablename ORDER BY a.id,a.colorder ";
			DataTable dataTable = SQLHelper.RunSqlDt(empty, sqlCommand);
			string text2 = "";
			string text3 = "";
			string text4 = "";
			string text5 = "";
			string text6 = "";
			string text7 = "";
			string text8 = "";
			string text9 = "";
			string text10 = "";
			string text11 = "";
			JObject jObject2 = new JObject { ["Key"] = "" };
			foreach (DataRow row in dataTable.Rows)
			{
				text2 = row["column_name"].ToString() ?? "";
				text4 = row["column_description"].ToString() ?? "";
				text3 = row["column_notnull"].ToString() ?? "";
				text5 = row["column_default"].ToString() ?? "";
				text6 = row["is_identity"].ToString() ?? "";
				text7 = row["typname"].ToString() ?? "";
				JToken jToken = jObject[text2];
				if (!(text6 != "YES"))
				{
					continue;
				}
				if (text3 == "1" && jToken == null && text5 == "0" && text == "0")
				{
					throw new Exception("请求参数不正确！");
				}
				if ((!(text3 == "1") || jToken != null || !(text5 == "0") || !(text != "0")) && (!(text3 == "1") || jToken != null || !(text5 == "1")) && (!(text3 == "0") || jToken != null))
				{
					if (jToken == null)
					{
						throw new Exception("意外的错误！");
					}
					if (jToken.Type == JTokenType.Null || jToken.ToString().ToUpper().Equals("NULL", StringComparison.CurrentCultureIgnoreCase))
					{
						sqlCommand.Parameters.AddWithValue("@" + text2, DBNull.Value);
					}
					else
					{
						sqlCommand.Parameters.AddWithValue("@" + text2, jToken.ToString());
					}
					if (text == "0")
					{
						text9 = text9 + text2 + ",";
						text10 = text10 + "@" + text2 + ",";
						continue;
					}
					text11 = text11 + text2 + "=@" + text2 + ",";
					jObject2[text2] = jToken.ToString();
					JObject jObject3 = jObject2;
					jObject3["Key"] = jObject3["Key"]?.ToString() + text2 + ",";
				}
			}
			if (text == "0")
			{
				text8 = " INSERT INTO " + tableName + " (" + text9.TrimEnd(',') + ") values(" + text10.TrimEnd(',') + ") ";
				text8 = text8 + " SELECT IDENT_CURRENT('" + tableName + "')";
				text = SQLHelper.RunSqlStr(text8, sqlCommand);
				text8 = "SELECT * FROM " + tableName + " WHERE " + key + "='" + text + "'";
				content = JsonConvert.SerializeObject(new JObject
				{
					["type"] = "新增",
					["data"] = JArray.FromObject(SQLHelper.RunSqlDt(text8, sqlCommand))
				});
			}
			else
			{
				text8 = "SELECT " + jObject2["Key"]?.ToString().Trim(',') + " FROM " + tableName + " WHERE " + key + "='" + text + "'";
				text8 = text8 + " UPDATE " + tableName + " SET " + text11.Trim(',') + "  WHERE " + key + "='" + text + "'";
				DataTable dataTable2 = SQLHelper.RunSqlDt(text8, sqlCommand);
				if (dataTable2.Rows.Count > 0)
				{
					JArray jArray = JArray.FromObject(dataTable2);
					jArray.Add(jObject2);
					content = JsonConvert.SerializeObject(new JObject
					{
						["type"] = "编辑",
						["data"] = jArray
					});
				}
			}
			WriteLog(tableName, operation, key, text, content, userId, userName, curTime, sqlCommand);
			sqlCommand.Transaction.Commit();
			return text;
		}
		catch (Exception ex)
		{
			sqlCommand.Transaction.Rollback();
			throw new Exception(ex.Message.ToString());
		}
		finally
		{
			conn.Close();
			conn.Dispose();
		}
	}

	public static int Delete(string tableName, string operation, string key, string keyValue, int userId, string userName, string? curTime, SqlCommand pCmd)
	{
		try
		{
			string[] array = key.Split(",");
			string[] array2 = keyValue.Split(",");
			if (array.Length != array2.Length)
			{
				throw new Exception("参数与键值不匹配！");
			}
			string text = "";
			for (int i = 0; i < array.Length; i++)
			{
				if (i != 0)
				{
					text += " AND ";
				}
				text = text + array[i] + " = @" + array[i];
				pCmd.Parameters.AddWithValue("@" + array[i], array2[i]);
			}
			string text2 = " SELECT * FROM " + tableName + " WHERE " + text;
			text2 = text2 + " DELETE " + tableName + " WHERE " + text;
			DataTable dataTable = SQLHelper.RunSqlDt(text2, pCmd);
			string content = JsonConvert.SerializeObject(new JObject
			{
				["type"] = "删除",
				["data"] = JArray.FromObject(dataTable)
			});
			WriteLog(tableName, operation, key, keyValue, content, userId, userName, curTime, pCmd);
			return dataTable.Rows.Count;
		}
		catch (Exception ex)
		{
			throw new Exception(ex.Message.ToString());
		}
	}

	public static int Delete(string tableName, string operation, string key, string keyValue, int userId, string userName, string? curTime, SqlConnection conn)
	{
		SqlCommand sqlCommand = conn.CreateCommand();
		sqlCommand.Transaction = conn.BeginTransaction();
		try
		{
			string[] array = key.Split(",");
			string[] array2 = keyValue.Split(",");
			if (array.Length != array2.Length)
			{
				throw new Exception("参数与键值不匹配！");
			}
			string text = "";
			for (int i = 0; i < array.Length; i++)
			{
				if (i != 0)
				{
					text += " AND ";
				}
				text = text + array[i] + " = @" + array[i];
				sqlCommand.Parameters.AddWithValue("@" + array[i], array2[i]);
			}
			string text2 = " SELECT * FROM " + tableName + " WHERE " + text;
			text2 = text2 + " DELETE " + tableName + " WHERE " + text;
			DataTable dataTable = SQLHelper.RunSqlDt(text2, sqlCommand);
			string content = JsonConvert.SerializeObject(new JObject
			{
				["type"] = "删除",
				["data"] = JArray.FromObject(dataTable)
			});
			WriteLog(tableName, operation, key, keyValue, content, userId, userName, curTime, sqlCommand);
			if (conn != null)
			{
				sqlCommand.Transaction.Commit();
			}
			return dataTable.Rows.Count;
		}
		catch (Exception ex)
		{
			sqlCommand.Transaction.Rollback();
			throw new Exception(ex.Message.ToString());
		}
		finally
		{
			conn.Close();
			conn.Dispose();
		}
	}

	public static void WriteLog(string tableName, string operation, string field, string fieldValue, string content, int userId, string userName, string? curTime, SqlCommand pCmd)
	{
		try
		{
			if (curTime == null || curTime == "")
			{
				curTime = SQLHelper.RunSqlStr("SELECT GETDATE()", pCmd);
			}
			string text = " INSERT INTO TSysOperationLog (FTableName,FOperation,FField,FFieldValue,FContent,FUserName,FUserId,FCurTime)";
			text += " VALUES (@tableName,@operation,@field,@fieldValue,@content,@userName,@userId,@curTime)";
			pCmd.Parameters.AddWithValue("@tableName", tableName);
			pCmd.Parameters.AddWithValue("@operation", operation);
			pCmd.Parameters.AddWithValue("@field", field);
			pCmd.Parameters.AddWithValue("@fieldValue", fieldValue);
			pCmd.Parameters.AddWithValue("@content", content);
			pCmd.Parameters.AddWithValue("@userId", userId);
			pCmd.Parameters.AddWithValue("@userName", userName);
			pCmd.Parameters.AddWithValue("@curTime", curTime);
			SQLHelper.RunSqlStr(text, pCmd);
		}
		catch (Exception ex)
		{
			throw new Exception(ex.Message.ToString());
		}
	}
}
