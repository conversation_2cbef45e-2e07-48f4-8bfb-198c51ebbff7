using System;
using System.Data;
using System.Threading.Tasks;
using API.Common;
using API.Quartz;
using Microsoft.Data.SqlClient;
using Newtonsoft.Json.Linq;
using Quartz;

namespace API.DataAccess.DouYu;

public class DouYuQuartzGroup
{
	public static DataTable GetQuartzGroupList(string id, string search, int userId, int limit = 0, int offset = 0, string prop = "", string order = "")
	{
		string text = " SELECT * FROM TQuartzGroup WHERE FUserId=" + userId;
		if (search != "")
		{
			text = text + " AND FName LIKE '%" + search + "%'";
		}
		if (id != "")
		{
			text = text + " AND Fid=" + id;
		}
		if (prop == "")
		{
			prop = "FSort";
			order = "ASC";
		}
		return SQLHelper.DouYuLocalDB.RunSqlDt(text, limit, offset, prop, order);
	}

	public static string SaveQuartzGroup(JObject jObject, int userId, string userName, string curTime, SqlCommand? pCmd)
	{
		string jObject2 = Util.GetJObject(jObject, "FName");
		string jObject3 = Util.GetJObject(jObject, "Fid");
		string sSql = " SELECT 1 FROM TQuartzGroup WHERE FName='" + jObject2 + "' AND FUserId=" + userId + " AND Fid !=" + jObject3;
		string text = SQLHelper.DouYuLocalDB.RunSqlStr(sSql);
		if (text == "1")
		{
			throw new Exception("定时任务分组存在重复！");
		}
		jObject["FUserId"] = userId;
		if (pCmd == null)
		{
			return DataCURD.Save(jObject, "TQuartzGroup", "定时任务分组", "Fid", userId, userName, curTime, SQLHelper.DouYuLocalDB.InitCnn());
		}
		return DataCURD.Save(jObject, "TQuartzGroup", "定时任务分组", "Fid", userId, userName, curTime, pCmd);
	}

	public static void DelQuartzGroup(JObject jObject, int userId, string userName, string curTime)
	{
		SqlConnection sqlConnection = SQLHelper.DouYuLocalDB.InitCnn();
		SqlCommand sqlCommand = sqlConnection.CreateCommand();
		sqlCommand.Transaction = sqlConnection.BeginTransaction();
		try
		{
			string jObject2 = Util.GetJObject(jObject, "id");
			string sSql = " SELECT FStatus FROM TQuartzGroup WHERE Fid =" + jObject2;
			string text = SQLHelper.RunSqlStr(sSql, sqlCommand);
			if (text == "1")
			{
				throw new Exception("无法删除正在运行的定时任务！");
			}
			DataCURD.Delete("TQuartzGroup", "删除定时任务分组", "Fid", jObject2, userId, userName, curTime, sqlCommand);
			DataCURD.Delete("TQuartz", "删除定时任务对照", "FGroupId", jObject2, userId, userName, curTime, sqlCommand);
			sqlCommand.Transaction.Commit();
		}
		catch (Exception ex)
		{
			sqlCommand.Transaction.Rollback();
			throw new Exception(ex.Message);
		}
		finally
		{
			sqlConnection.Close();
			sqlConnection.Dispose();
		}
	}

	public static async Task ShutdownGroup(ISchedulerFactory schedulerFactory, JObject jObject, int userId, string userName, string curTime)
	{
		IScheduler scheduler = await schedulerFactory.GetScheduler();
		string jObject2 = Util.GetJObject(jObject, "id");
		DataCURD.Save(new JObject
		{
			["Fid"] = jObject2,
			["FStatus"] = 0
		}, "TQuartzGroup", "关闭任务分组", "Fid", userId, userName, curTime, SQLHelper.DouYuLocalDB.InitCnn());
		DataTable dt = DouYuQuartz.GetQuartzList("", jObject2, "", userId);
		for (int i = 0; i < dt.Rows.Count; i++)
		{
			string text = dt.Rows[i]["Fid"].ToString() ?? "";
			string name = dt.Rows[i]["FJobName"].ToString() ?? "";
			JobKey jobKey = new JobKey(name, "定时任务-斗鱼-" + text);
			if (await scheduler.CheckExists(jobKey))
			{
				await scheduler.DeleteJob(jobKey);
			}
		}
	}

	public static async Task StartGroup(ISchedulerFactory schedulerFactory, JObject jObject, int userId, string userName, string curTime)
	{
		IScheduler scheduler = await schedulerFactory.GetScheduler();
		string jObject2 = Util.GetJObject(jObject, "id");
		DataCURD.Save(new JObject
		{
			["Fid"] = jObject2,
			["FStatus"] = 1
		}, "TQuartzGroup", "启动任务分组", "Fid", userId, userName, curTime, SQLHelper.DouYuLocalDB.InitCnn());
		DataTable dt = DouYuQuartz.GetQuartzList("", jObject2, "", userId);
		for (int i = 0; i < dt.Rows.Count; i++)
		{
			string id = dt.Rows[i]["Fid"].ToString() ?? "";
			string jobName = dt.Rows[i]["FJobName"].ToString() ?? "";
			string enable = dt.Rows[i]["FEnable"].ToString() ?? "";
			JobKey jobKey = new JobKey(jobName, "定时任务-斗鱼-" + id);
			if (!(await scheduler.CheckExists(jobKey)) && enable == "1")
			{
				string s = dt.Rows[i]["FExecTime"].ToString() ?? "";
				int num = int.Parse(dt.Rows[i]["FDifference"].ToString() ?? "0");
				int seconds = int.Parse(dt.Rows[i]["FSeconds"].ToString() ?? "0");
				string text = dt.Rows[i]["FAreaId"].ToString() ?? "";
				string text2 = dt.Rows[i]["FCookieId"].ToString() ?? "";
				string text3 = dt.Rows[i]["FParam"].ToString() ?? "";
				DateTime dateTime = DateTime.ParseExact(s, "HH:mm:ss", null);
				string cronExpression = dateTime.Second + " " + dateTime.Minute + " " + dateTime.Hour + " ? * *";
				JObject jObject3 = new JObject
				{
					["areaId"] = text,
					["cookieId"] = text2,
					["param"] = text3,
					["difference"] = num
				};
				await QzUtil.ScheduleJobPlus<QzDouYu>(scheduler, "定时任务-斗鱼-" + id, jobName, cronExpression, seconds, jObject3, userId);
			}
		}
	}
}
