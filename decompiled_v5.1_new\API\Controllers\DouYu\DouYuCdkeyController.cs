using System;
using System.Data;
using API.BusService.System;
using API.Common;
using API.DataAccess.DouYu;
using API.Models.Comm;
using Microsoft.AspNetCore.Mvc;

namespace API.Controllers.DouYu;

public class DouYuCdkeyController : Controller
{
	[HttpPost]
	public Response GetCdkeyList([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			int id = BusSysUser.Instance.User.Id;
			string jObject = Util.GetJObject(model.jObjectSearch, "areaId");
			string jObject2 = Util.GetJObject(model.jObjectSearch, "cookieId");
			string jObject3 = Util.GetJObject(model.jObjectSearch, "activityId");
			string jObject4 = Util.GetJObject(model.jObjectSearch, "search");
			string jObject5 = Util.GetJObject(model.jObjectSearch, "status");
			DataTable cdkeyList = DouYuCdkey.GetCdkeyList(jObject5, jObject, jObject2, jObject3, jObject4, id, model.limit, model.offset, model.prop, model.order);
			response.data = Util.GetTableResponse(cdkeyList);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response ExportCdkeyList([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			int id = BusSysUser.Instance.User.Id;
			string jObject = Util.GetJObject(model.jObjectSearch, "id");
			response.data = DouYuCdkey.ExportCdkeyList(jObject, BusSysUser.Instance.User.Id);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}
}
