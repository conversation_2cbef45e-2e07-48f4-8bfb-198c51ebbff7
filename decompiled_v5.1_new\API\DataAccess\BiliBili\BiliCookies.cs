using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using API.BusService.BiliBili;
using API.Common;
using API.DataAccess.System;
using API.Models.Comm;
using Microsoft.Data.SqlClient;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace API.DataAccess.BiliBili;

public class BiliCookies
{
	public static async Task<string> Recharge(string cookieId, int money, int userId)
	{
		DataTable cookiesList = GetCookiesList(cookieId, "", userId);
		if (cookiesList.Rows.Count == 1)
		{
			DataRow dataRow = cookiesList.Rows[0];
			string name = dataRow["FKey"].ToString() ?? "";
			string cookie = dataRow["FCookie"].ToString() ?? "";
			string header = dataRow["FHeaders"].ToString() ?? "";
			string text = dataRow["FProxyAddress"].ToString() ?? "";
			string userName = dataRow["FProxyUserName"].ToString() ?? "";
			string password = dataRow["FProxyPassword"].ToString() ?? "";
			string text2 = dataRow["FRoomId"].ToString() ?? "";
			string text3 = dataRow["FCsrf"].ToString() ?? "";
			HttpClientHandler defaultHandler = null;
			if (text != "")
			{
				defaultHandler = new HttpClientHandler
				{
					Proxy = new WebProxy
					{
						Address = new Uri(text),
						Credentials = new NetworkCredential(userName, password)
					}
				};
			}
			Dictionary<string, string> dictionary = HttpClientFactory.FormataHeader(header, cookie);
			dictionary.Remove("Accept-Language");
			HttpClientFactory httpClientFactory = new HttpClientFactory(name, dictionary, defaultHandler);
			string text4 = "1";
			if (text2 == "")
			{
				text2 = "1";
				text4 = "11";
			}
			string text5 = "platform=pc&build=0&pay_cash=" + money * 1000 + "&pay_bp=0&context_id=" + text2 + "&context_type=" + text4 + "&goods_id=1&goods_num=" + money;
			text5 = text5 + "&goods_type=2&live_statistics=%7B%22pc_client%22%3A%22pcWeb%22%2C%22jumpfrom%22%3A%22-99998%22%2C%22room_category%22%3A%220%22%2C%22trackid%22%3A%22-99998%22%7D&statistics=%7B%22platform%22%3A0%2C%22pc_client%22%3A%22pcWeb%22%7D&ios_bp=0&common_bp=0&csrf_token=" + text3 + "&csrf=" + text3 + "&visit_id=";
			JObject jObject = await httpClientFactory.Post("https://api.live.bilibili.com/xlive/revenue/v1/order/createQrCodeOrder", text5);
			if (Util.GetJObject(jObject, "code") == "0")
			{
				return Util.GetJObject(jObject["data"], "code_url");
			}
			throw new Exception(Util.GetJObject(jObject, "message"));
		}
		throw new Exception("请选择账号！");
	}

	public static void UpdatePower(string cookieId, int userId, string userName, string curTime)
	{
		DataTable cookiesList = GetCookiesList(cookieId, "", userId);
		List<Task> list = new List<Task>();
		ConcurrentBag<JObject> jArray = new ConcurrentBag<JObject>();
		for (int i = 0; i < cookiesList.Rows.Count; i++)
		{
			int index = i;
			DataRow dataRow = cookiesList.Rows[index];
			string id = dataRow["Fid"].ToString() ?? "";
			string key = dataRow["FKey"].ToString() ?? "";
			string cookie = dataRow["FCookie"].ToString() ?? "";
			string header = dataRow["FHeaders"].ToString() ?? "";
			string proxyAddress = dataRow["FProxyAddress"].ToString() ?? "";
			string proxyUserName = dataRow["FProxyUserName"].ToString() ?? "";
			string proxyPassword = dataRow["FProxyPassword"].ToString() ?? "";
			list.Add(Task.Run(async delegate
			{
				await GetInfo(id, key, cookie, header, proxyAddress, proxyUserName, proxyPassword, jArray);
			}));
		}
		foreach (Task item in list)
		{
			item.Wait();
		}
		for (int num = 0; num < jArray.Count; num++)
		{
			JObject jObject = jArray.ElementAt(num);
			DataCURD.Save(jObject, "TCookies", "更新等级电池", "Fid", userId, userName, curTime, SQLHelper.BiliLocalDB.InitCnn());
		}
	}

	private static async Task GetInfo(string cookieId, string key, string cookie, string header, string proxyAddress, string proxyUserName, string proxyPassword, ConcurrentBag<JObject> bag)
	{
		HttpClientHandler defaultHandler = null;
		if (proxyAddress != "")
		{
			defaultHandler = new HttpClientHandler
			{
				Proxy = new WebProxy
				{
					Address = new Uri(proxyAddress),
					Credentials = new NetworkCredential(proxyUserName, proxyPassword)
				}
			};
		}
		Dictionary<string, string> dictionary = HttpClientFactory.FormataHeader(header, cookie);
		dictionary.Remove("Accept-Language");
		HttpClientFactory httpClientFactory = new HttpClientFactory(key, dictionary, defaultHandler);
		JObject jObject = await httpClientFactory.Get("https://api.bilibili.com/x/web-interface/nav");
		string result = "0";
		if (Util.GetJObject(jObject, "code") == "0")
		{
			result = Util.GetJObject(Util.GetJObject<JToken>(jObject["data"], "level_info"), "current_level");
		}
		jObject = await httpClientFactory.Get("https://api.live.bilibili.com/xlive/web-ucenter/user/get_user_info");
		if (Util.GetJObject(jObject, "code") == "0")
		{
			result = result + " / " + Util.GetJObject<int>(jObject["data"], "gold") / 100;
		}
		bag.Add(new JObject
		{
			["Fid"] = cookieId,
			["FInfo"] = result
		});
	}

	public static void CleanMessage()
	{
		SQLHelper.BiliLocalDB.RunSqlText(" UPDATE TCookies SET FStatus=''");
	}

	public static string ExportCookie(string cookieId, User user)
	{
		string text = " SELECT FKey AS [Key],T1.FSort AS 序号,FName AS 账号名称,FCookie AS Cookie,T2.FAddress+CASE T2.FUserName WHEN '' THEN '' ELSE ':'+T2.FUserName+':'+T2.FPassword END AS 代理地址 FROM TCookies T1 ";
		text = text + "  LEFT JOIN TProxy T2 ON T2.Fid=T1.FProxyId AND T2.FEnable=1 WHERE T1.FUserId=" + user.Id + " AND T1.Fid IN (" + cookieId + ") ORDER BY T1.FSort";
		DataTable dt = SQLHelper.BiliLocalDB.RunSqlDt(text);
		Util.FileDownload(out string absolute, out string relative);
		ExcelHelper.X2003.TableToExcelForXLS(dt, absolute);
		return relative;
	}

	public static async Task Supplement(string cookieId, int userId, string userName, string curTime)
	{
		DataTable cookiesList = GetCookiesList(cookieId, "", userId, "");
		JArray jArray = JArray.FromObject(cookiesList);
		for (int i = 0; i < jArray.Count; i++)
		{
			JToken drCookie = jArray[i];
			string name = drCookie["FName"]?.ToString() ?? "";
			try
			{
				if (drCookie["Fid"]?.ToString() == null)
				{
				}
				string name2 = drCookie["FKey"]?.ToString() ?? "";
				string cookie = drCookie["FCookie"]?.ToString() ?? "";
				string header = drCookie["FHeaders"]?.ToString() ?? "";
				if (drCookie["FCsrf"]?.ToString() == null)
				{
				}
				string text = drCookie["FProxyAddress"]?.ToString() ?? "";
				string userName2 = drCookie["FProxyUserName"]?.ToString() ?? "";
				string password = drCookie["FProxyPassword"]?.ToString() ?? "";
				HttpClientHandler defaultHandler = null;
				if (text != "")
				{
					defaultHandler = new HttpClientHandler
					{
						Proxy = new WebProxy
						{
							Address = new Uri(text),
							Credentials = new NetworkCredential(userName2, password)
						}
					};
				}
				Dictionary<string, string> dictionary = HttpClientFactory.FormataHeader(header, cookie);
				dictionary.Remove("Accept-Language");
				HttpClientFactory httpClientFactory = new HttpClientFactory(name2, dictionary, defaultHandler);
				string name3 = name;
				Util.WriteLog("BiliBili", name3, "补全Cookie", await httpClientFactory.GetIp());
				Dictionary<string, string> cookieDictionary = new Dictionary<string, string>();
				if (!string.IsNullOrEmpty(cookie))
				{
					string[] array = cookie.Split(';');
					string[] array2 = array;
					foreach (string text2 in array2)
					{
						string[] array3 = text2.Trim().Split('=');
						if (array3.Length == 2)
						{
							cookieDictionary[array3[0]] = array3[1];
						}
					}
				}
				cookieDictionary.TryAdd("_uuid", BusBiliUtil.GetUUID());
				cookieDictionary.TryAdd("b_lsid", BusBiliUtil.GetLSID());
				cookieDictionary.TryAdd("b_nut", DateTimeOffset.Now.ToUnixTimeSeconds().ToString());
				httpClientFactory.AddHeaders(null, Util.DictionaryToCookieString(cookieDictionary));
				Util.GetCookieByKey(cookie, "buvid4");
				Util.GetCookieByKey(cookie, "buvid3");
				if (!cookieDictionary.ContainsKey("buvid3") || !cookieDictionary.ContainsKey("buvid4"))
				{
					JObject jObject = await httpClientFactory.Get("https://api.bilibili.com/x/frontend/finger/spi");
					if (!cookieDictionary.ContainsKey("buvid3"))
					{
						cookieDictionary.Add("buvid3", Util.GetJObject(jObject["data"], "b_3"));
					}
					if (!cookieDictionary.ContainsKey("buvid4"))
					{
						cookieDictionary.Add("buvid4", Util.GetJObject(jObject["data"], "b_4"));
					}
				}
				httpClientFactory.AddHeaders(null, Util.DictionaryToCookieString(cookieDictionary));
				string jObject2 = Util.GetJObject(Util.GetJObject<JToken>(await httpClientFactory.Get("https://api.bilibili.com/x/player/wbi/v2?aid=112960173246550&cid=500001649369065"), "responseHeaders"), "Set-Cookie");
				string cookieByKey = Util.GetCookieByKey(jObject2, "sid");
				if (cookieByKey != "")
				{
					cookieDictionary["sid"] = cookieByKey;
				}
				httpClientFactory.AddHeaders(null, Util.DictionaryToCookieString(cookieDictionary));
				if (!cookieDictionary.TryGetValue("bili_ticket_expires", out string value) || !long.TryParse(value, out var result) || result < DateTimeOffset.UtcNow.ToUnixTimeSeconds())
				{
					string text3 = DateTimeOffset.Now.ToUnixTimeSeconds().ToString();
					string text4 = BusBiliUtil.Hash("ts" + text3, "XgwSnGZ1p", "HMACSHA256");
					JObject jObject = await httpClientFactory.Post("https://api.bilibili.com/bapis/bilibili.api.ticket.v1.Ticket/GenWebTicket?key_id=ec02&hexsign=" + text4 + "&context[ts]=" + text3 + "&csrf=" + cookieDictionary["bili_jct"].ToString(), "");
					if (Util.GetJObject(jObject, "code") == "0")
					{
						cookieDictionary["bili_ticket"] = Util.GetJObject(jObject["data"], "ticket");
						cookieDictionary["bili_ticket_expires"] = (Util.GetJObject<int>(jObject["data"], "created_at") + Util.GetJObject<int>(jObject["data"], "ttl")).ToString();
					}
				}
				JObject jToken = await httpClientFactory.Get("https://api.bilibili.com/x/web-interface/nav");
				if (Util.GetJObject(jToken, "code") != "0")
				{
					drCookie["FStatus"] = Util.GetJObject(jToken, "message");
				}
				drCookie["FCookie"] = Util.DictionaryToCookieString(cookieDictionary);
				Util.WriteLog("BiliBili", name, "补全Cookie", "补全成功！");
			}
			catch (Exception ex)
			{
				drCookie["FStatus"] = ex.Message;
				Util.WriteLog("BiliBili", name, "补全Cookie", "补全失败|" + ex.Message, ConsoleColor.Red);
			}
			DataCURD.Save(JObject.FromObject(drCookie), "TCookies", "补全Cookie", "Fid", userId, userName, curTime, SQLHelper.BiliLocalDB.InitCnn());
		}
	}

	public static void UpdateUserAgent(string cookieId, string userAgent, int userId, string userName, string curTime)
	{
		DataTable cookiesList = GetCookiesList(cookieId, "", userId);
		for (int i = 0; i < cookiesList.Rows.Count; i++)
		{
			DataRow dr = cookiesList.Rows[i];
			string jObject = Util.GetJObject(dr, "FHeaders");
			JObject jObject2 = JObject.Parse(jObject);
			jObject2["User-Agent"] = userAgent;
			JObject jObject3 = new JObject
			{
				["Fid"] = Util.GetJObject(dr, "Fid"),
				["FHeaders"] = JsonConvert.SerializeObject(jObject2)
			};
			DataCURD.Save(jObject3, "TCookies", "更新UserAgent", "Fid", userId, userName, curTime, SQLHelper.BiliLocalDB.InitCnn());
		}
	}

	public static void UpdateProxy(string cookieId, string proxyId, int userId, string userName, string curTime)
	{
		DataTable cookiesList = GetCookiesList(cookieId, "", userId);
		for (int i = 0; i < cookiesList.Rows.Count; i++)
		{
			DataRow dr = cookiesList.Rows[i];
			JObject jObject = new JObject
			{
				["Fid"] = Util.GetJObject(dr, "Fid"),
				["FProxyId"] = proxyId
			};
			DataCURD.Save(jObject, "TCookies", "更新代理", "Fid", userId, userName, curTime, SQLHelper.BiliLocalDB.InitCnn());
		}
	}

	public static async Task ImportCookies(User user, string src, string curTime, SqlCommand? pCmd = null)
	{
		DataTable dt = GetCookiesList("", "", "", "", user.Id, 0, 0, "FSort", "ASC");
		SqlConnection cnn = null;
		try
		{
			if (pCmd == null)
			{
				cnn = SQLHelper.BiliLocalDB.InitCnn();
				pCmd = cnn.CreateCommand();
				pCmd.Transaction = cnn.BeginTransaction();
			}
			SQLHelper.RunTableLockx("TCookies", pCmd);
			string text = Directory.GetCurrentDirectory() + "\\wwwroot\\" + src;
			if (!File.Exists(text))
			{
				throw new Exception("文件不存在！");
			}
			DataTable dataTable = ExcelHelper.GetDataTable(text);
			for (int i = 0; i < dataTable.Columns.Count; i++)
			{
				string text2 = dataTable.Columns[i].ColumnName.Trim();
				if (text2 != "Cookie" && text2 != "代理地址" && text2 != "Key" && text2 != "账号名称" && text2 != "序号")
				{
					throw new Exception("导入的文件格式不正确，请检查文件格式！");
				}
			}
			foreach (DataRow row in dataTable.Rows)
			{
				string text3 = Util.GetJObject(row, "Cookie");
				string jObject = Util.GetJObject(row, "代理地址");
				string jObject2 = Util.GetJObject(row, "Key");
				string jObject3 = Util.GetJObject(row, "账号名称");
				int.TryParse(Util.GetJObject(row, "序号"), out var result);
				string identifying = "";
				string tel = "";
				try
				{
					JArray jArray = JArray.Parse(text3);
					text3 = "";
					foreach (JToken item in jArray)
					{
						string jObject4 = Util.GetJObject(item, "name");
						string jObject5 = Util.GetJObject(item, "value");
						if (jObject4 == "DedeUserID")
						{
							identifying = jObject5;
						}
						if (!(jObject4 == "province"))
						{
							if (jObject4 == "phone")
							{
								tel = jObject5;
							}
							else if (!text3.Contains(jObject4 + "="))
							{
								text3 = text3 + jObject4 + "=" + jObject5 + "; ";
							}
						}
					}
				}
				catch
				{
					string[] array = text3.Split(';');
					text3 = "";
					for (int j = 0; j < array.Length; j++)
					{
						string[] array2 = array[j].Trim().Split('=');
						if (array2.Length != 2)
						{
							continue;
						}
						string text4 = array2[0];
						string text5 = array2[1];
						if (text4 == "DedeUserID")
						{
							identifying = text5;
						}
						if (!(text4 == "province"))
						{
							if (text4 == "phone")
							{
								tel = text5;
							}
							else if (!text3.Contains(text4 + "="))
							{
								text3 = text3 + text4 + "=" + text5 + "; ";
							}
						}
					}
				}
				await ImportCookie(user, curTime, dt, jObject2, text3.Trim().TrimEnd(';'), jObject3, result, jObject, tel, identifying, pCmd);
			}
			if (cnn != null)
			{
				pCmd.Transaction.Commit();
			}
		}
		catch (Exception ex)
		{
			if (cnn != null)
			{
				pCmd?.Transaction.Rollback();
			}
			throw new Exception(ex.Message.ToString());
		}
		finally
		{
			if (cnn != null)
			{
				cnn.Close();
				cnn.Dispose();
			}
		}
		static async Task ImportCookie(User user2, string curTime2, DataTable dataTable2, string key, string cookie, string cookieName, int sort, string proxy, string text8, string text6, SqlCommand pCmd2)
		{
			string proxyId = "0";
			HttpClientHandler defaultHandler = null;
			if (proxy != "")
			{
				string[] array3 = proxy.Split(':');
				string ipAddress = array3[0] + ":" + array3[1] + ":" + array3[2];
				string userCode = "";
				string userPWD = "";
				if (array3.Length >= 5)
				{
					userCode = array3[3];
					userPWD = array3[4];
				}
				proxyId = await BiliProxy.SaveProxy(new JObject
				{
					["Fid"] = 0,
					["FUserId"] = user2.Id,
					["FGroupId"] = 0,
					["FAddress"] = ipAddress,
					["FUserName"] = userCode,
					["FPassword"] = userPWD,
					["FSort"] = 0
				}, user2, curTime2, noTest: true);
				defaultHandler = new HttpClientHandler
				{
					Proxy = new WebProxy
					{
						Address = new Uri(ipAddress),
						Credentials = new NetworkCredential(userCode, userPWD)
					}
				};
			}
			DataRow[] array4;
			if (!(key != ""))
			{
				array4 = ((!(text6 != "")) ? null : dataTable2.Select("FIdentifying='" + text6 + "'"));
			}
			else
			{
				array4 = dataTable2.Select("FKey='" + key + "'");
				if (array4 == null || array4.Length == 0)
				{
					array4 = dataTable2.Select("FIdentifying='" + text6 + "'");
				}
			}
			dataTable2 = ((array4 == null || array4.Length == 0) ? new DataTable() : array4.CopyToDataTable());
			string text7;
			JObject jObject6;
			if (dataTable2.Rows.Count > 0)
			{
				jObject6 = (JObject)JArray.FromObject(dataTable2)[0];
				text7 = Util.GetJObject(jObject6, "FHeaders");
			}
			else
			{
				jObject6 = new JObject
				{
					["userId"] = user2.Id,
					["userName"] = user2.Name,
					["type"] = "BiliBili",
					["addCount"] = 1
				};
				Request model = new Request
				{
					jObjectParam = jObject6,
					curTime = curTime2
				};
				object obj2 = await Util.Request("/Common/AddCookies", model);
				if (obj2 == null)
				{
					throw new Exception("连接服务器失败！");
				}
				JArray jArray2 = JArray.FromObject(obj2);
				jObject6 = (JObject)jArray2[0];
				text7 = "{\"Accept\":\"*/*\",\"Accept-Language\":\"zh-CN,zh;q=0.9,zh;q=0.8;q=0.8\",\"User-Agent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36\",\"sec-ch-ua\":\"\\\"Chromium\\\";v=\\\"109\\\"\",\"sec-ch-ua-mobile\":\"?0\",\"sec-ch-ua-platform\":\"\\\"Windows\\\"\",\"Content-Type\":\"application/x-www-form-urlencoded\"}";
			}
			jObject6["FCookie"] = cookie;
			jObject6["FName"] = cookieName;
			jObject6["FSort"] = sort;
			jObject6["FHeaders"] = text7;
			jObject6["FCsrf"] = Util.GetCookieByKey(cookie, "bili_jct");
			jObject6["FIdentifying"] = Util.GetCookieByKey(cookie, "DedeUserID");
			if (text8 != "")
			{
				jObject6["FTel"] = text8;
			}
			jObject6["FProxyId"] = proxyId;
			long num = long.Parse(Util.GetCookieByKey(cookie, "bili_ticket_expires", "0"));
			if (num != 0L)
			{
				jObject6["FCookieExpires"] = DateTimeOffset.FromUnixTimeSeconds(num).LocalDateTime.ToString("yyyy-MM-dd HH:mm:ss");
			}
			Dictionary<string, string> dictionary = HttpClientFactory.FormataHeader(text7, cookie);
			dictionary.Remove("Accept-Language");
			HttpClientFactory httpClientFactory = new HttpClientFactory(cookieName, dictionary, defaultHandler);
			JObject jObject7 = await httpClientFactory.Get("https://api.live.bilibili.com/xlive/app-blink/v1/room/GetInfo?platform=pc");
			if (Util.GetJObject(jObject7, "code") == "0")
			{
				jObject6["FRoomId"] = Util.GetJObject(jObject7["data"], "room_id");
				if (cookieName == "")
				{
					jObject6["FName"] = Util.GetJObject(jObject7["data"], "uname");
				}
			}
			else
			{
				jObject6["FStatus"] = Util.GetJObject(jObject7, "message");
			}
			jObject6["FBrowserStatus"] = 2;
			string text9 = DataCURD.Save(jObject6, "TCookies", "导入账号", "Fid", user2.Id, user2.Name, curTime2, pCmd2);
			jObject6["Fid"] = text9;
			await EditCookie(jObject6, user2.Id, user2.Name, curTime2, pCmd2);
		}
	}

	public static async Task AddCookies(string cookieNum, int userId, string userName, string curTime, SqlCommand? pCmd = null)
	{
		SqlConnection cnn = null;
		try
		{
			if (pCmd == null)
			{
				cnn = SQLHelper.BiliLocalDB.InitCnn();
				pCmd = cnn.CreateCommand();
				pCmd.Transaction = cnn.BeginTransaction();
			}
			if (!int.TryParse(cookieNum, out var result))
			{
				throw new Exception("添加的账号数量不正确！");
			}
			if (10 < result || result <= 0)
			{
				throw new Exception("添加的账号数量不正确！");
			}
			JObject jObjectParam = new JObject
			{
				["userId"] = userId,
				["userName"] = userName,
				["type"] = "BiliBili",
				["addCount"] = cookieNum
			};
			Request request = new Request
			{
				jObjectParam = jObjectParam,
				curTime = curTime
			};
			object obj = await Util.Request("/Common/AddCookies", request);
			JArray jArray = ((obj != null) ? JArray.FromObject(obj) : SysUserCookies.AddCookies(request));
			DataTable sourceDataTable = jArray.ToObject<DataTable>() ?? new DataTable();
			SQLHelper.SqlBulkCopyByDataTable("TCookies", sourceDataTable, pCmd);
			if (cnn != null)
			{
				pCmd.Transaction.Commit();
			}
		}
		catch (Exception ex)
		{
			if (cnn != null)
			{
				pCmd?.Transaction.Rollback();
			}
			throw new Exception(ex.Message.ToString());
		}
		finally
		{
			if (cnn != null)
			{
				cnn.Close();
				cnn.Dispose();
			}
		}
	}

	public static DataTable GetCookiesList(string id, string areaId, string search, string enable, int userId, int limit = 0, int offset = 0, string prop = "", string order = "")
	{
		areaId = ((areaId == "") ? "0" : areaId);
		string text = " ";
		text += " SELECT T1.Fid,T1.FKey,T1.FName,T1.FRoomId,T1.FIdentifying,T1.FCsrf,T1.FCookie,T1.FHeaders,T1.FCountryId,T1.FTel,T1.FSMSUrl";
		text += " ,'http://live.bilibili.com/'+T1.FRoomId AS FRoomNo,T1.FProxyId,T1.FSort,T1.FCookieExpires,T1.FInfo";
		text += " ,T1.FRoomTitle,T1.FRoomRTMP";
		text += " ,T2.FAddress AS FProxyAddress,T2.FUserName AS FProxyUserName,T2.FPassword AS FProxyPassword";
		if (areaId != "0")
		{
			text += " ,T4.FCompleteName,CASE ISNULL(T5.FCount,0) WHEN 0 THEN '请更新任务！' ELSE ISNULL(T3.FAwardName,'暂无抢领任务！') END AS FAwardName2";
			text += " ,CASE WHEN ISNULL(T6.FExpirationTime,T1.FExpirationTime)<=GETDATE() THEN '账号已到期|'+T1.FStatus WHEN ISNULL(T6.FExpirationTime,T1.FExpirationTime)-2<=GETDATE() THEN '账号即将到期' WHEN T1.FBrowserStatus=0 THEN '浏览器登录失效'  ELSE T1.FStatus END AS FStatusName";
			text += " ,ISNULL(T6.FExpirationTime,T1.FExpirationTime) AS FExpirationTime";
		}
		else
		{
			text += " ,CASE WHEN T1.FExpirationTime<=GETDATE() THEN '账号已到期' WHEN T1.FExpirationTime-2<=GETDATE() THEN '账号即将到期' WHEN T1.FBrowserStatus=0 THEN '浏览器登录失效'  ELSE T1.FStatus END AS FStatusName";
			text += " ,T1.FExpirationTime";
		}
		text = text + SQLHelper.total + " FROM TCookies T1 ";
		text = text + " LEFT JOIN TProxy T2 ON T2.FEnable=1 AND T2.Fid=T1.FProxyId AND T2.FUserId=" + userId;
		if (areaId != "0")
		{
			text += " LEFT JOIN (SELECT TT.FCookieId,TT.FAwardName FROM (SELECT FCookieId,FAwardName, ROW_NUMBER() OVER (PARTITION BY FCookieId ORDER BY FSort) AS FNo ";
			text = text + " FROM TCookiesTask WHERE FEnable=1 AND FDaily=0 AND FReceiveStatus=1 AND FStockTotal!=FStockConsumed AND FUserId=" + userId + " AND FAreaId=" + areaId + ")";
			text += " TT WHERE TT.FNo = 1) T3 ON T3.FCookieId=T1.Fid";
			text = text + " LEFT JOIN (SELECT FCookieId,MAX(CAST(FComplete AS INT)) AS FCompleteName FROM TCookiesTask WHERE FUserId=" + userId + " AND FAreaId=" + areaId + " ";
			text += "            AND FComplete!='' AND FStockTotal!=FStockConsumed GROUP BY FCookieId) T4 ON T4.FCookieId=T1.Fid";
			text = text + " LEFT JOIN ( SELECT FCookieId,COUNT(*) AS FCount FROM TCookiesTask WHERE FAreaId=" + areaId + " GROUP BY FCookieId) T5 ON T5.FCookieId=T1.Fid";
			text = text + " LEFT JOIN TCookieArea T6 ON T6.FKey=T1.FKey AND T6.FAreaId=" + areaId + " AND T6.FUserId=" + userId;
		}
		text = text + " WHERE T1.FEnable=1 AND T1.FUserId=" + userId;
		if (id != "")
		{
			text = text + " AND T1.Fid IN (" + id + ")";
		}
		if (search != "")
		{
			text = text + " AND ( T1.FName LIKE '%" + search + "%' OR T1.FRoomId LIKE '%" + search + "%' OR T1.FIdentifying LIKE '%" + search + "%' OR T2.FAddress LIKE '%" + search + "%'  )";
		}
		if (enable == "1")
		{
			text = ((!(areaId != "0")) ? (text + " AND T1.FExpirationTime > GETDATE()") : (text + " AND ISNULL(T6.FExpirationTime,T1.FExpirationTime) > GETDATE()"));
		}
		if (areaId != "0")
		{
			text = text + " AND ( T1.FKey IN (SELECT FKey FROM TCookieArea WHERE FAreaId=" + areaId + " AND FUserId=" + userId + " AND ( T6.FMileage=1 OR T6.FAricles=1)) ";
			text = text + " OR T1.FKey NOT IN (SELECT FKey FROM TCookieArea WHERE FUserId=" + userId + ")  OR ISNULL(T6.FExpirationTime,T1.FExpirationTime) < GETDATE())";
		}
		if (prop == "")
		{
			prop = "FSort";
			order = "ASC";
		}
		return SQLHelper.BiliLocalDB.RunSqlDt(text, limit, offset, prop, order);
	}

	public static DataTable GetCookiesListByKey(string id)
	{
		string sSql = " SELECT FName,FRoomId,FIdentifying,FCsrf,FCookie,FHeaders,FInfo FROM TCookies WHERE Fid=" + id;
		return SQLHelper.BiliLocalDB.RunSqlDt(sSql);
	}

	public static DataTable GetCookiesList(string id, string identifying, int userId, string enable = "1", string areaId = "0")
	{
		string text = " ";
		text += " SELECT T1.Fid,T1.FKey,T1.FName,T1.FRoomId,T1.FIdentifying,T1.FCsrf,T1.FCookie,T1.FHeaders,T1.FCountryId,T1.FTel,T1.FSMSUrl,T1.FProxyId";
		text += " ,T2.FAddress AS FProxyAddress,T2.FUserName AS FProxyUserName,T2.FPassword AS FProxyPassword";
		text += " ,T1.FRoomTitle,T1.FRoomRTMP,T1.FBrowserStatus,ISNULL(T3.FMileage,1) AS FMileage,ISNULL(T3.FAricles,1) AS FAricles";
		text = text + SQLHelper.total + " FROM TCookies T1 ";
		text = text + " LEFT JOIN TProxy T2 ON T2.FEnable=1 AND T2.Fid=T1.FProxyId AND T2.FUserId=" + userId;
		text = text + " LEFT JOIN TCookieArea T3 ON T3.FKey=T1.FKey AND T3.FAreaId=" + areaId + " AND T3.FUserId=" + userId;
		text = text + " WHERE T1.FEnable=1 AND T1.FUserId=" + userId;
		if (enable == "1")
		{
			text += " AND ISNULL(T3.FExpirationTime,T1.FExpirationTime) > GETDATE()";
		}
		if (identifying != "")
		{
			text = text + " AND T1.FIdentifying IN (" + identifying + ")";
		}
		if (id != "")
		{
			text = text + " AND T1.Fid IN (" + id + ")";
		}
		return SQLHelper.BiliLocalDB.RunSqlDt(text);
	}

	public static async Task EditCookie(JObject jObject, int userId, string userName, string curTime, SqlCommand? pCmd = null)
	{
		SqlConnection cnn = null;
		try
		{
			if (pCmd == null)
			{
				cnn = SQLHelper.BiliLocalDB.InitCnn();
				pCmd = cnn.CreateCommand();
				pCmd.Transaction = cnn.BeginTransaction();
			}
			string jObject2 = Util.GetJObject(jObject, "FIdentifying");
			string jObject3 = Util.GetJObject(jObject, "FKey");
			if (jObject2 != "")
			{
				Request request = new Request
				{
					jObjectParam = new JObject
					{
						["userId"] = userId,
						["userName"] = userName,
						["key"] = jObject3,
						["identifying"] = jObject2
					},
					curTime = curTime
				};
				object obj = await Util.Request("/Common/EditCookie", request);
				if (obj == null)
				{
					obj = SysUserCookies.EditCookie(request);
				}
				jObject["FExpirationTime"] = obj.ToString();
			}
			string jObject4 = Util.GetJObject(jObject, "FCookie");
			if (jObject4 != "")
			{
				long num = long.Parse(Util.GetCookieByKey(jObject4, "bili_ticket_expires", "0"));
				if (num != 0L)
				{
					jObject["FCookieExpires"] = DateTimeOffset.FromUnixTimeSeconds(num).LocalDateTime.ToString("yyyy-MM-dd HH:mm:ss");
				}
			}
			DataCURD.Save(jObject, "TCookies", "编辑账号", "Fid", userId, userName, curTime, pCmd);
			Util.WriteLog("BiliBili", Util.GetJObject(jObject, "FName"), "更新Cookie信息", "当前Cookie有效期：" + DateTimeOffset.FromUnixTimeSeconds(long.Parse(Util.GetCookieByKey(jObject4, "bili_ticket_expires", "0"))).LocalDateTime.ToString("yyyy-MM-dd HH:mm:ss"), ConsoleColor.Green);
			if (cnn != null)
			{
				pCmd.Transaction.Commit();
			}
		}
		catch (Exception ex)
		{
			if (cnn != null)
			{
				pCmd?.Transaction.Rollback();
			}
			throw new Exception(ex.Message.ToString());
		}
		finally
		{
			if (cnn != null)
			{
				cnn.Close();
				cnn.Dispose();
			}
		}
	}

	public static async Task DelCookie(JObject jObject, int userId, string userName, string curTime, SqlCommand? pCmd = null)
	{
		SqlConnection cnn = null;
		try
		{
			if (pCmd == null)
			{
				cnn = SQLHelper.BiliLocalDB.InitCnn();
				pCmd = cnn.CreateCommand();
				pCmd.Transaction = cnn.BeginTransaction();
			}
			string id = Util.GetJObject(jObject, "Fid");
			string jObject2 = Util.GetJObject(jObject, "FIdentifying");
			string key = Util.GetJObject(jObject, "FKey");
			Request request = new Request
			{
				jObjectParam = new JObject
				{
					["userId"] = userId,
					["userName"] = userName,
					["key"] = key,
					["identifying"] = jObject2
				},
				curTime = curTime
			};
			if (await Util.Request("/Common/DelCookie", request) == null)
			{
				_ = (object)SysUserCookies.DelCookie(request);
			}
			DataCURD.Delete("TCookies", "删除账号", "Fid", id, userId, userName, curTime, pCmd);
			DataCURD.Delete("TCdkey", "删除Cdkey", "FCookieId", id, userId, userName, curTime, pCmd);
			DataCURD.Delete("TCookiesLive", "删除直播视频", "FCookieId", id, userId, userName, curTime, pCmd);
			DataCURD.Delete("TCookiesLiveConfig", "删除直播配置", "FCookieId", id, userId, userName, curTime, pCmd);
			DataCURD.Delete("TCookiesTask", "删除任务信息", "FCookieId", id, userId, userName, curTime, pCmd);
			DataCURD.Delete("TCookieArea", "删除分区关系信息", "FKey", key, userId, userName, curTime, pCmd);
			try
			{
				string path = Directory.GetCurrentDirectory() + "\\Util\\Chromium\\Bili-" + key;
				if (!Directory.Exists(path))
				{
					Directory.Delete(path, recursive: true);
				}
			}
			catch (IOException ex)
			{
				Console.WriteLine("无法删除文件夹: " + ex.Message);
			}
			catch (UnauthorizedAccessException ex2)
			{
				Console.WriteLine("没有权限删除文件夹: " + ex2.Message);
			}
			if (cnn != null)
			{
				pCmd.Transaction.Commit();
			}
		}
		catch (Exception ex3)
		{
			if (cnn != null)
			{
				pCmd?.Transaction.Rollback();
			}
			throw new Exception(ex3.Message.ToString());
		}
		finally
		{
			if (cnn != null)
			{
				cnn.Close();
				cnn.Dispose();
			}
		}
	}

	public static async Task DelCookiePlus(JObject jObject, int userId, string userName, string curTime, SqlCommand? pCmd = null)
	{
		SqlConnection cnn = null;
		try
		{
			if (pCmd == null)
			{
				cnn = SQLHelper.BiliLocalDB.InitCnn();
				pCmd = cnn.CreateCommand();
				pCmd.Transaction = cnn.BeginTransaction();
			}
			string id = Util.GetJObject(jObject, "Fid");
			string jObject2 = Util.GetJObject(jObject, "FIdentifying");
			string key = Util.GetJObject(jObject, "FKey");
			Request request = new Request
			{
				jObjectParam = new JObject
				{
					["userId"] = userId,
					["userName"] = userName,
					["key"] = key,
					["identifying"] = jObject2,
					["del"] = "1"
				},
				curTime = curTime
			};
			if (await Util.Request("/Common/DelCookie", request) == null)
			{
				_ = (object)SysUserCookies.DelCookie(request);
			}
			DataCURD.Delete("TCookies", "删除账号", "Fid", id, userId, userName, curTime, pCmd);
			DataCURD.Delete("TCdkey", "删除Cdkey", "FCookieId", id, userId, userName, curTime, pCmd);
			DataCURD.Delete("TCookiesLive", "删除直播视频", "FCookieId", id, userId, userName, curTime, pCmd);
			DataCURD.Delete("TCookiesLiveConfig", "删除直播配置", "FCookieId", id, userId, userName, curTime, pCmd);
			DataCURD.Delete("TCookiesTask", "删除任务信息", "FCookieId", id, userId, userName, curTime, pCmd);
			DataCURD.Delete("TCookieArea", "删除分区关系信息", "FKey", key, userId, userName, curTime, pCmd);
			try
			{
				string path = Directory.GetCurrentDirectory() + "\\Util\\Chromium\\Bili-" + key;
				if (!Directory.Exists(path))
				{
					Directory.Delete(path, recursive: true);
				}
			}
			catch (IOException ex)
			{
				Console.WriteLine("无法删除文件夹: " + ex.Message);
			}
			catch (UnauthorizedAccessException ex2)
			{
				Console.WriteLine("没有权限删除文件夹: " + ex2.Message);
			}
			if (cnn != null)
			{
				pCmd.Transaction.Commit();
			}
		}
		catch (Exception ex3)
		{
			if (cnn != null)
			{
				pCmd?.Transaction.Rollback();
			}
			throw new Exception(ex3.Message.ToString());
		}
		finally
		{
			if (cnn != null)
			{
				cnn.Close();
				cnn.Dispose();
			}
		}
	}

	public static async Task EmptyCookie(JObject jObject, int userId, string userName, string curTime, SqlCommand? pCmd = null)
	{
		SqlConnection cnn = null;
		try
		{
			if (pCmd == null)
			{
				cnn = SQLHelper.BiliLocalDB.InitCnn();
				pCmd = cnn.CreateCommand();
				pCmd.Transaction = cnn.BeginTransaction();
			}
			string id = Util.GetJObject(jObject, "Fid");
			string jObject2 = Util.GetJObject(jObject, "FIdentifying");
			string key = Util.GetJObject(jObject, "FKey");
			Request request = new Request
			{
				jObjectParam = new JObject
				{
					["userId"] = userId,
					["userName"] = userName,
					["key"] = key,
					["identifying"] = jObject2
				},
				curTime = curTime
			};
			if (await Util.Request("/Common/EmptyCookie", request) == null)
			{
				_ = (object)SysUserCookies.DelCookie(request);
			}
			jObject = new JObject
			{
				["Fid"] = id,
				["FStatus"] = "",
				["FName"] = "",
				["FRoomId"] = "",
				["FIdentifying"] = "",
				["FCsrf"] = "",
				["FCookie"] = "",
				["FHeaders"] = "",
				["FInfo"] = "",
				["FCountryId"] = "0",
				["FTel"] = "",
				["FSMSUrl"] = "",
				["FProxyId"] = "0",
				["FSort"] = "9999",
				["FCookieExpires"] = "NULL",
				["FBrowserStatus"] = "NULL"
			};
			DataCURD.Save(jObject, "TCookies", "重置账号", "Fid", userId, userName, curTime, pCmd);
			DataCURD.Delete("TCdkey", "删除Cdkey", "FCookieId", id, userId, userName, curTime, pCmd);
			DataCURD.Delete("TCookiesLive", "删除直播视频", "FCookieId", id, userId, userName, curTime, pCmd);
			DataCURD.Delete("TCookiesLiveConfig", "删除直播配置", "FCookieId", id, userId, userName, curTime, pCmd);
			DataCURD.Delete("TCookiesTask", "删除任务信息", "FCookieId", id, userId, userName, curTime, pCmd);
			try
			{
				string path = Directory.GetCurrentDirectory() + "\\Util\\Chromium\\Bili-" + key;
				if (!Directory.Exists(path))
				{
					Directory.Delete(path, recursive: true);
				}
			}
			catch (IOException ex)
			{
				Console.WriteLine("无法删除文件夹: " + ex.Message);
			}
			catch (UnauthorizedAccessException ex2)
			{
				Console.WriteLine("没有权限删除文件夹: " + ex2.Message);
			}
			if (cnn != null)
			{
				pCmd.Transaction.Commit();
			}
		}
		catch (Exception ex3)
		{
			if (cnn != null)
			{
				pCmd?.Transaction.Rollback();
			}
			throw new Exception(ex3.Message.ToString());
		}
		finally
		{
			if (cnn != null)
			{
				cnn.Close();
				cnn.Dispose();
			}
		}
	}

	public static async Task UpdateCookieExpires(User user, string curTime)
	{
		int userId = user.Id;
		string userName = user.Name;
		Request request = new Request
		{
			jObjectParam = new JObject
			{
				["userId"] = userId,
				["userName"] = userName,
				["type"] = "BiliBili"
			},
			curTime = curTime
		};
		object obj = await Util.Request("/Common/GetUserCookiesList", request);
		if (obj == null)
		{
			obj = SysUserCookies.GetUserCookiesList(request);
		}
		JArray source = JArray.FromObject(obj);
		DataTable cookiesList = GetCookiesList("", "", "", "", userId);
		foreach (JObject item in source.Cast<JObject>())
		{
			DataRow[] array = cookiesList.Select("FKey='" + Util.GetJObject(item, "FKey") + "'") ?? Array.Empty<DataRow>();
			if (array.Length != 0)
			{
				DataCURD.Save(item, "TCookies", "更新到期时间", "FKey", userId, userName, curTime, SQLHelper.BiliLocalDB.InitCnn());
				continue;
			}
			item["Fid"] = 0;
			item["FUserId"] = userId;
			DataCURD.Save(item, "TCookies", "新增账号", "Fid", userId, userName, curTime, SQLHelper.BiliLocalDB.InitCnn());
		}
	}

	public static async Task UpdateCookieExpires2(User user, string curTime)
	{
		int userId = user.Id;
		string userName = user.Name;
		Request request = new Request
		{
			jObjectParam = new JObject
			{
				["userId"] = userId,
				["userName"] = userName,
				["type"] = "BiliBili"
			},
			curTime = curTime
		};
		object obj = await Util.Request("/Common/GetUserCookiesList2", request);
		if (obj == null)
		{
			obj = SysUserCookies.GetUserCookiesList2(request);
		}
		JArray jArray = JArray.FromObject(obj);
		DataTable dataTable = jArray.ToObject<DataTable>() ?? new DataTable();
		if (dataTable.Rows.Count != 0)
		{
			DataCURD.Delete("TCookieArea", "删除", "FUserId", userId.ToString(), userId, userName, curTime, SQLHelper.BiliLocalDB.InitCnn());
			SQLHelper.BiliLocalDB.SqlBulkCopyByDataTable("TCookieArea", dataTable);
		}
		else if (("," + user.Rights + ",").Contains(",110210100,"))
		{
			string sSql = " UPDATE TCookieArea SET FExpirationTime=T1.FExpirationTime FROM TCookies T1 WHERE T1.FKey=TCookieArea.FKey AND T1.FUserId=" + userId;
			SQLHelper.BiliLocalDB.RunSqlText(sSql);
		}
		else
		{
			DataCURD.Delete("TCookieArea", "删除", "FUserId", userId.ToString(), userId, userName, curTime, SQLHelper.BiliLocalDB.InitCnn());
		}
	}

	public static string GetCookieName(string key, string id = "")
	{
		string text = " SELECT FName FROM TCookies WHERE 1=1";
		if (key != "")
		{
			text = text + " AND FKey='" + key + "'";
		}
		else
		{
			if (!(id != ""))
			{
				return "未知账号";
			}
			text = text + " AND Fid=" + id;
		}
		return SQLHelper.BiliLocalDB.RunSqlStr(text);
	}
}
