using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using API.BusService.System;
using API.Common;
using API.DataAccess.BiliBili;
using API.Models.Comm;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;

namespace API.Controllers.BiliBili;

public class BiliArticlesController : Controller
{
	[HttpPost]
	public Response GetTitleVideo([FromBody] Request model, User user)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectSearch, "areaId");
			string jObject2 = Util.GetJObject(model.jObjectSearch, "areaName");
			response.data = BiliArticles.GetTitleVideo(jObject, jObject2, user.Id);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response DelTitle([FromBody] Request model, User user)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "id");
			string jObject2 = Util.GetJObject(model.jObjectParam, "areaId");
			BiliArticles.DelTitle(jObject, jObject2, user, model.curTime);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response GetArticlesTitleList([FromBody] Request model, User user)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectSearch, "search");
			string jObject2 = Util.GetJObject(model.jObjectSearch, "areaId");
			DataTable articlesTitleList = BiliArticles.GetArticlesTitleList(jObject, jObject2, user.Id, model.limit, model.offset, model.prop, model.order);
			response.data = Util.GetTableResponse(articlesTitleList);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response GetArticlesList([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectSearch, "search");
			string jObject2 = Util.GetJObject(model.jObjectSearch, "areaId");
			string jObject3 = Util.GetJObject(model.jObjectSearch, "taskId");
			string jObject4 = Util.GetJObject(model.jObjectSearch, "cookieId");
			string jObject5 = Util.GetJObject(model.jObjectSearch, "type");
			string jObject6 = Util.GetJObject(model.jObjectSearch, "date");
			int jObject7 = Util.GetJObject<int>(model.jObjectSearch, "times");
			string jObject8 = Util.GetJObject(model.jObjectSearch, "export");
			string jObject9 = Util.GetJObject(model.jObjectSearch, "taskStatus");
			JArray jArray = BiliArticles.GetArticlesList(jObject2, jObject, jObject6, jObject7, jObject5, jObject3, jObject9, jObject4, BusSysUser.Instance.User.Id);
			if (jObject8 == "1")
			{
				DataTable dataTable = jArray.ToObject<DataTable>();
				if (dataTable != null)
				{
					dataTable.Columns.Remove("Fid");
					dataTable.Columns.Remove("FKey");
					dataTable.Columns.Remove("FHeaders");
					if (jObject5 == "4")
					{
						string areaName = BiliArea.GetAreaName(jObject2);
						dataTable.Columns.Remove("FDate");
						jArray = new JArray();
						foreach (DataRow row in dataTable.Rows)
						{
							foreach (DataColumn column in dataTable.Columns)
							{
								if (!(column.ColumnName != "FCookieName") || !(column.ColumnName != "FCookieId"))
								{
									continue;
								}
								string text = row[column.ColumnName].ToString() ?? "";
								if (!(text != ""))
								{
									continue;
								}
								string[] array = text.Split('|');
								for (int i = 0; i < array.Length; i++)
								{
									string[] array2 = array[i].Split(',');
									if (array2.Length == 2)
									{
										string text2 = array2[0];
										string text3 = array2[1];
										jArray.Add(new JObject
										{
											["名称"] = row["FCookieName"].ToString(),
											["日期"] = column.ColumnName,
											["分区"] = areaName,
											["内容"] = text2,
											["播放量"] = text3,
											["秘钥"] = RsaEncrypt.RSAEncrypt(row["FCookieId"].ToString() + "," + areaName + "," + jObject2 + "," + text2.Replace("500----", ""))
										});
									}
								}
							}
						}
						if (jArray.Count > 0)
						{
							dataTable = jArray.ToObject<DataTable>() ?? new DataTable();
						}
					}
					else
					{
						dataTable.Columns.Remove("FCookieId");
					}
					Util.FileDownload(out string absolute, out string relative);
					ExcelHelper.X2003.TableToExcelForXLS(dataTable, absolute);
					response.data = relative;
				}
			}
			else
			{
				if (model.prop != "")
				{
					if (model.order == "ascending")
					{
						jArray = new JArray(jArray.OrderByDescending((JToken obj) => obj[model.prop]));
					}
					else if (model.order == "descending")
					{
						jArray = new JArray(jArray.OrderBy((JToken obj) => obj[model.prop]));
					}
				}
				response.data = new
				{
					total = jArray.Count,
					rows = jArray.Skip(model.offset).Take((model.limit == 0) ? 10000 : model.limit)
				};
			}
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response SaveArticles([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			BiliArticles.SaveArticles(model.jObjectParam, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public async Task<Response> UpdateArticles([FromBody] Request model)
	{
		Response mRet = new Response();
		try
		{
			JArray jArray = Util.GetJObject<JArray>(model.jObjectParam, "array") ?? new JArray();
			await BiliArticles.UpdateArticles(jArray, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}

	[HttpPost]
	public async Task<Response> DelArticles([FromBody] Request model)
	{
		Response mRet = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "val");
			string jObject2 = Util.GetJObject(model.jObjectParam, "cookieId");
			await BiliArticles.DelArticles(jObject, jObject2, BusSysUser.Instance.User.Id);
		}
		catch (Exception ex)
		{
			mRet.code = 500;
			mRet.message = Util.ExceptionMessage(ex.Message);
		}
		return mRet;
	}

	[HttpPost]
	public Response GetUserList([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectSearch, "search");
			response.data = BiliArticles.GetUserList(jObject);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response GetFileList([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectSearch, "groupId");
			int jObject2 = Util.GetJObject<int>(model.jObjectSearch, "page");
			int jObject3 = Util.GetJObject<int>(model.jObjectSearch, "pageSize");
			string jObject4 = Util.GetJObject(model.jObjectSearch, "type");
			string jObject5 = Util.GetJObject(model.jObjectSearch, "keyword");
			string jObject6 = Util.GetJObject(model.jObjectSearch, "path");
			response.data = BiliArticles.GetFileList(jObject6, jObject4, jObject, jObject5, jObject2, jObject3);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response DelFile([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			List<string> list = Util.GetJObject<List<string>>(model.jObjectParam, "array") ?? new List<string>();
			foreach (string item in list)
			{
				Util.FileDelete(Directory.GetCurrentDirectory() + "\\WWWRoot\\" + item);
			}
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response GetConfig([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "key");
			string jObject2 = Util.GetJObject(model.jObjectParam, "areaId");
			response.data = BiliArticles.GetConfig(jObject, jObject2, BusSysUser.Instance.User.Id);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response SaveConfig([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			BiliArticles.SaveConfig(model.jObjectParam, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response GetVideoList([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectSearch, "search");
			string text = AppSettings.GetVal("TokenPrefix") + Token.GetTokenValue(BusSysUser.Instance.User.Id.ToString());
			string val = AppSettings.GetVal("TokenKey");
			string val2 = AppSettings.GetVal("Url", "Kestrel:Endpoints:Http");
			string text2 = Directory.GetCurrentDirectory() + "\\Util\\Chromium\\DouYuDefault";
			string fileName = text2 + "\\Chromium.exe";
			ProcessStartInfo processStartInfo = new ProcessStartInfo();
			processStartInfo.FileName = fileName;
			processStartInfo.Arguments = "-a " + text + " -c \"" + BusSysUser.Instance.User.Cookie + "\" -t Common -i 0 -k 快手小工具 -m \"https://www.kuaishou.com/search/video?searchKey=" + jObject + "\" -s 0 -u " + val2 + " --token " + val;
			ProcessStartInfo startInfo = processStartInfo;
			Process.Start(startInfo)?.WaitForExit();
			response.data = JArray.Parse(BiliArticles.GetVideo());
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response SaveVideo([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "VideoStr");
			BiliArticles.SaveVideo(jObject, BusSysUser.Instance.User.Id, BusSysUser.Instance.User.Name, model.curTime);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response EasySubmit([FromBody] Request model, User user)
	{
		Response response = new Response();
		try
		{
			List<Task> list = new List<Task>();
			string jObject = Util.GetJObject(model.jObjectParam, "ids");
			if (jObject == "")
			{
				throw new Exception("请选择账号！");
			}
			string jObject2 = Util.GetJObject(model.jObjectParam, "areaName");
			string areaId = Util.GetJObject(model.jObjectParam, "areaId");
			int time = Util.GetJObject<int>(model.jObjectParam, "time");
			int num = Util.GetJObject<int>(model.jObjectParam, "num");
			string jObject3 = Util.GetJObject(model.jObjectParam, "title");
			JArray jArray = BiliArticles.GetCookieArticlesInfo(jObject, jObject3, areaId, jObject2, user.Id);
			list.Add(Task.Run(() => BiliArticles.Submit(jArray, areaId, time, num, user.Id)));
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response Submit([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			List<Task> list = new List<Task>();
			JArray jArray = Util.GetJObject<JArray>(model.jObjectParam, "array") ?? new JArray();
			string areaId = Util.GetJObject(model.jObjectParam, "areaId", "0");
			int time = Util.GetJObject<int>(model.jObjectParam, "time");
			int num = Util.GetJObject<int>(model.jObjectParam, "num");
			list.Add(Task.Run(() => BiliArticles.Submit(jArray, areaId, time, num, BusSysUser.Instance.User.Id)));
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response DoVideo([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			JArray jArray = Util.GetJObject<JArray>(model.jObjectParam, "array") ?? new JArray();
			string jObject = Util.GetJObject(model.jObjectParam, "path");
			response.data = BiliArticles.DoVideo(jArray, jObject);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response DoTxt([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "src");
			response.data = BiliArticles.DoTxt(jObject);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response AutoVideo([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			int jObject = Util.GetJObject<int>(model.jObjectParam, "num");
			string jObject2 = Util.GetJObject(model.jObjectParam, "path");
			response.data = BiliArticles.AutoVideo(jObject, jObject2);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response SplitVideo([FromBody] Request model)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "path");
			string jObject2 = Util.GetJObject(model.jObjectParam, "areaId");
			int jObject3 = Util.GetJObject<int>(model.jObjectParam, "time");
			BiliArticles.SplitVideo(jObject, jObject2, jObject3);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response UploadAppeal([FromBody] Request model, User user)
	{
		Response response = new Response();
		try
		{
			List<Task> list = new List<Task>();
			string src = Util.GetJObject(model.jObjectParam, "src");
			string cookieId = Util.GetJObject(model.jObjectParam, "cookieId");
			list.Add(Task.Run(() => BiliArticles.UploadAppeal(cookieId, src, user.Id)));
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}

	[HttpPost]
	public Response UploadTitle([FromBody] Request model, User user)
	{
		Response response = new Response();
		try
		{
			string jObject = Util.GetJObject(model.jObjectParam, "src");
			string jObject2 = Util.GetJObject(model.jObjectParam, "areaId");
			BiliArticles.UploadTitle(jObject, jObject2, user);
		}
		catch (Exception ex)
		{
			response.code = 500;
			response.message = Util.ExceptionMessage(ex.Message);
		}
		return response;
	}
}
